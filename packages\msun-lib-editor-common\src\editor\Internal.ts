import Editor from "./Editor";
import Page from "./Page";
import Cell from "./Cell";
import WaterMark from "./WaterMark";
import Shape from "./shape";
import XField from "./XField";
import { Path } from "./Path";
import { Direction, TransformDataType } from "./Definition";
import { ShapeMode } from "./Constant";
import EditorHelper from "./EditorHelper";
import Group from "./Groups";
import Comment from "./Comment";
import Paragraph from "./Paragraph";
import Table from "./Table";
import Character from "./Character";
import CommentBox from "./CommentBox";
import { isRow } from "./Utils";
import AutoFill from "./AutoFill";

/**
 * 内部使用函数及变量
 */
export default class Internal {
  editor: Editor;

  inputAdapter!: HTMLTextAreaElement;

  markInput!: HTMLElement;

  cacheFieldAuto: { id: string, textList: Array<object> } = { id: "", textList: [] }//缓存文本域自动化原文本域数据

  drag_data: string = "";

  current_page: Page | null = null; // 当前光标所在页，因为该属性只在编辑页眉页脚时有用，所以归为内部属性

  cursorFlashingTimer: number = -1; // 光标闪动定时器ID

  originConfigItems: any = {};

  page_position: number = 0; // 该页顶部边界 距离canvas最顶部边界的距离

  client_top: number = 0; // 便于计算input的高度

  client_left: number = 0;

  resize_cell: Cell | null = null; // 是在 viewData 中的 Cell

  resize_cell_position: Direction = Direction.up;

  pointer_down_position: { x: number; y: number } = { x: 0, y: 0 };

  // 标尺拖动相关状态
  ruler_dragging: boolean = false; // 是否正在拖动标尺指示器
  ruler_drag_target: 'left' | 'right' | null = null; // 拖动目标：左边距或右边距
  ruler_drag_start_x: number = 0; // 拖动开始时的鼠标X坐标
  ruler_drag_start_margin: number = 0; // 拖动开始时的边距值
  initial_left_margin: number = 0; // 初始左边距，用于固定刻度基准
  ruler_scale_base_x: number = 0; // 拖动时固定的刻度基准X坐标

  print_absolute_y: number = 0;

  shape_editor: "startXY" | "endXY" | number | boolean = false; // 浮动图形是否出于编辑状态

  focusMark: WaterMark | null = null;// 光标聚焦处的mark

  italicWatermark: any = { //倾斜水印，不储存数据，仅显示
    text: "",
    direction: "right",
    module: ["pageWrite",],
    font: {
      fontSize: 16,
      fontFamily: "宋体",
      opacity: 0.5,
      color: "#7F7F7F",
    }
  }

  is_drag_mark: boolean = false; // 是否拖拽mark

  is_edit_mark: any = undefined;// 是否编辑mark

  dx: number = 0; // 距离浮动模型原点的 x 距离

  markId: string = "";

  draw_shape: number = ShapeMode.NoShape; // 是否绘制浮动图形

  focus_shape: Shape = new Shape();// 光标聚焦处的shape

  is_drag_shape: boolean = false;// 是否拖动shape

  is_in_shape: Shape | boolean = false; // 是否点击在了shape上

  is_in_field_drag: boolean = false; // 是否点在了文本域拖拽宽度的点上

  focus_drag_field: XField | null = null //当前点击的文本域

  is_point_down: boolean = false //是否点击了一遍文本域

  shape_before_move_xy: { x: number; y: number } = { x: 0, y: 0 };// 拖动shape时鼠标移动暂存上一次移动的鼠标位置

  mark_before_move_xy: { x: number; y: number } = { x: 0, y: 0 };// 拖动mark时鼠标移动暂存上一次移动的鼠标位置

  is_drag_mobile: boolean = false //移动端选区拖拽

  currentFieldInfo: {
    boxFieldId: string, // 当前复选框的文本域 id
    checked: boolean,
    otherFieldId: Map<string, true>, // 非复选框 不要的文本域 的 id
  } = {
    boxFieldId: "",
    checked: false,
    otherFieldId: new Map(),
  };


  scroll_bar_top: number = 0;

  cell_is_scroll: boolean = false; // 单元格是否可以滚动

  scroll_cell_info: Cell | null = null; // 可滚动单元格的cell信息

  click_scroll_bar: boolean = false;

  move_in_cell_bar: boolean = false; // 是否移动到了可滚动单元格的滚动条上

  click_cell_bar: boolean = false; // 是否点击在可滚动单元格的滚动条上

  drag_in_path: any[] = []; // 实时记录的拖拽的虚拟光标位置

  field_shadow_xy: { x: number; y: number, endX: number, type: string, maxX: number } = { x: 0, y: 0, endX: 0, type: "max", maxX: 0 };// 拖动设置文本域宽度时储存位置

  is_in_link: boolean = false

  cursor_state: any = {
    type: "default",
    element: undefined,
    area: undefined

  };// 光标类型

  pointer_down_state: any = this.cursor_state;

  point_is_selected: boolean = false;

  area_location: { start_absolute_x: number, end_absolute_x: number, start_absolute_y: number, end_absolute_y: number, transition_x: number, transition_y: number } = { start_absolute_x: 0, end_absolute_x: 0, start_absolute_y: 0, end_absolute_y: 0, transition_x: 0, transition_y: 0 };

  tempFields: Map<XField, boolean> = new Map(); // 临时保存文本域 目前是 setCharacterSize 里边用

  is_mark_text: boolean = false;// 水印模式下是否插入文字框

  brush_selection: boolean = false;// 记录点击格式刷之前为选区或非选区参数

  current_has_temp_bg_color_characters: any[] = [];

  scroll_timer: any;

  form_view_ori_path: Path = [];// 用于记录表单模式下文本域原来的坐标 原来为私有属性

  rendering: boolean = false; // 原来为私有属性

  imageSrcObj: any = {};// 加载过的图片合集

  originStartPath: Path = [];// 用在shift加方向键制造选区

  overDrawStyleInfo: any = null; // 鼠标经过的文本域信息

  focusDrawStyleInfo: any = null; // 获得焦点的文本域信息

  scrollBarMovedHeight: number = 0;

  cursorIsOnScrollBar: boolean = false;

  cursorIsCrossScrollBar: boolean = false;

  scrollBarPosition: number = 0;

  scrollBarTop: number = 0;

  currentCommentScrollBarPosition: number = 0;

  commentMovedHeight: number = 0;

  currentCommentPosition: number = 0;

  totalCommentHeight: number = 0;

  IsInCommentRange: boolean = false;

  rowHeight: number = 0;

  showCompleteUserName:any={}

  formula_name: string | null = null

  holdMouseAnimationId: number = -1;

  onFloatLine: boolean = false; // 是否点在了浮动模型的上边线上

  onFloatPoint: undefined | number; // 是否点在了浮动模型的八个点上

  transformData: TransformDataType = {
    images: [],
    fields: []
  };

  is_in_shape_sign: any = {}; // 是否在shape的三个按钮里

  focus_continue_shape: any = null;// 缓存第一次点击继续绘制折线的shape

  autoFill: AutoFill = new AutoFill();

  isCrossTable: Boolean = false

  currentTableTop: number = 0

  currentTableId: string = ""

  mobileScaleX: number = 0;

  mobileScaleY: number = 0;

  is_mobile_edit: boolean = false

  mobile_radius: number = 12  //移动端选区水滴半径

  pointer_down_ev: PointerEvent | null = null

  pointer_move_ev: PointerEvent | null = null

  pointer_up_ev: PointerEvent | null = null

  currentComment: any = null; // 点击箭头后 应该展示的批注
  
  commentCurrentPage: Page | undefined; // 光标所在批注的那一页 如果光标没在批注里边 当前页就是 undefined

  currentClickCommentPage: Page | undefined; // 当前点击在的批注所在的页 如果用 commentCurrentPage 的话 holde_move 的时候 移除该页面就绘制选区了这样不行

  tempBoxMap: any = new Map(); // 为了复选框的背景色绘制  fieldBgColor 配置打开时 才进行绘制背景色

  VL: any = {
    is_mobile_selection: false, //是否移动端选区
    is_mobile_button: false //编辑按钮是否出现
  } // 用于需要与vue层联动的属性

  // 放大后相对于没放大前的偏移量
  get view_scale_offset() {
    const editor = this.editor;
    return ((1 - editor.viewScale) * (parseInt(editor.init_canvas.style.width))) / (2 * editor.viewScale);
  }

  constructor(editor: Editor) {
    this.editor = editor;
  }

  /**
   * 初始化方法，程序启动时只需要执行一次，用于解析初始数据，准备其他必要数据
   * @param canvas 绘制内容的canvas dom
   * @param input 代理事件的input dom
   */
  attach(canvas: HTMLCanvasElement, input: HTMLTextAreaElement, div: HTMLElement): void {
    return EditorHelper.init(this.editor, canvas, input, div);
  }

  /**
   * 获取搜索用的字符串
   * @param numberOfWords 最大可搜索的字数
   * @param symbol 符号数组 会只搜索该符号后边的字符串
   * @returns
   */
  getSearchStr({ numberOfWords = 5, symbol = ["，", "。", "[", "]"] }: { numberOfWords?: number, symbol?: string[] } = { numberOfWords: 5, symbol: ["，", "。", "[", "]"] }) {
    return EditorHelper.getSearchStr({ numberOfWords, symbol, editor: this.editor });
  }

  /**
   * 鼠标在按键按下状态下移动
   * @param x
   * @param y
   */
  hold_mouse_move(x: number, y: number) {
    return EditorHelper.holdMouseMove(this.editor, x, y);
  }

  /**
   * 鼠标在按键释放状态下移动
   * @param x
   * @param y
   */
  release_mouse_move(x: number, y: number) {
    return EditorHelper.releaseMouseMove(this.editor, x, y);
  }

  /**
   * 双击事件
   * @param x 鼠标横坐标
   * @param y 鼠标纵坐标
   * @returns 返回操作成功否
   */
  dblclick(x: number, y: number) {
    return EditorHelper.dblclick(this.editor, x, y);
  }

  /**
     * 三击事件
     */
  tripleClick(event: MouseEvent) {
    return EditorHelper.tripleClick(event, this.editor);
  }

  /**
   * 判断是否存在分组表单
   */
  existFormGroup() {
    const group = this.editor.root_cell.groups.find(g => g.is_form);
    if (group) {
      return true;
    }
    return false;
  }

  /**
   * 获取点击处shape
   */
  getFocusShape(x: number, y: number, editor: Editor) {
    const shape = Shape.isInShape(x - this.editor.page_left, y + this.editor.scroll_top, this.editor);
    this.editor.closeShapesEditor();
    if (shape) {
      shape.is_editor = true;
      if (this.draw_shape === ShapeMode.ContinueLine) {
        editor.internal.focus_shape.is_editor = true;
      }
    }
    return shape;
  }

  // 获取有shape的页面集合
  getPageShapes() {
    return Shape.getPageShapes(this.editor);
  }

  /**
   * 判断是否在可视区内
   * @param y 相对于canvas画布(不是画板)最顶部的距离(包括了scroll_top和editor_padding_top)
   * @returns 在可视区内 为true 否则为false
   */
  isInVisualArea(y: number): boolean {
    return EditorHelper.isInVisualArea(this.editor, y);
  }

  /**
   * 滚动条滚动逻辑
   * @param y
   */
  scrollBarScroll(y: number) {
    return EditorHelper.dragScrollBar(this.editor, y);
  }

  scrollCellBar(y: number) {
    return EditorHelper.scrollCellBar(this.editor, y);
  }

  /**
   * 计算滚动条滚到上部的距离
   * @param value 每次滚动的距离
   */
  scroll(value: number): void {
    return EditorHelper.scroll(this.editor, value);
  }

  /**
   * 鼠标按下时根据坐标设置anchor，focus
   * @param x
   * @param y
   * @param type 值为up时表示是在鼠标按键抬起时执行的，默认为按下时执行
   */
  anchor_focus_by(x: number, y: number, type?: "up"): any {
    return EditorHelper.anchorFocusBy(this.editor, x, y, type);
  }

  /**
   *设置复选框选中辅助函数
   * @param res_info 光标点击根据坐标获取到的点击信息
   * @returns 返回true代表复选框设置的整个执行过程已经完成，无需再继续执行其他函数
   */
  setElementCheckedHelper(res_info: any): Boolean | undefined {
    return EditorHelper.setElementCheckedHelper(this.editor, res_info);
  }

  // 拖拽放下成为文本域
  makeDragDataToField(str: String) {
    return EditorHelper.dragToField(this.editor, str);
  }

  // 将数据插入到文档中
  putStr2Canvas(str: string, keepOrigin: boolean) {
    return EditorHelper.putStr2Canvas(this.editor, str, keepOrigin);
  }

  handleCommentIDSetByParagraphsTables(paragraphs_tables: (Paragraph | Table)[], group_id: string) {
    return Comment.handleCommentIDSetByParagraphsTables(this.editor, paragraphs_tables, group_id, false);
  }

  // 批注 处理分组外的批注 document
  handleCommentIDSet_document() {
    const paragraphs_tables = this.editor.current_cell.paragraph.filter(paragraphOrTable => !paragraphOrTable.group_id);
    this.handleCommentIDSetByParagraphsTables(paragraphs_tables, "document");
  }

  // 批注 处理所有分组内的批注 group
  handleCommentIDSet_group(current_group: Group) {
    const paragraphs_tables: (Paragraph | Table)[] = current_group.paragraph;
    this.handleCommentIDSetByParagraphsTables(paragraphs_tables, current_group.id);
  }

  // 处理批注 id 的集合
  handleCommentIDSet(current_group: Group | "document" | "all", isCus: Boolean = false) {
    if (isCus) {
      return Comment.handleCommentIDSet(this.editor, current_group, true);
    } else {
      return Comment.handleCommentIDSet(this.editor, current_group, false);
    }
  }

  // 切换批注模式
  toggleCommentMode(is_comment_mode: boolean) {
    return Comment.toggleMode(this.editor, is_comment_mode);
  }

  /**
   * 根据批注 id 获取批注
   * @param comment_id 批注 id
   * @returns
   */
  getCommentByCommentID(comment_id: string) {
    return Comment.getCommentByID(this.editor, comment_id);
  }

  /**
   * 根据批注 id 获取信息
   * @param comment_id 批注 id
   * @param cell root_cell 或者 header_cell footer_cell
   * @returns 是否成功
   */
  getInfoByCommentID(comment_id: string, cell: Cell = this.editor.current_cell) {
    return Comment.getInfoByID(this.editor, comment_id, false, cell);
  }

  /**
   * 更新批注
   * @param id 批注 id
   * @param updateValue 批注内容
   * @returns 是否成功
   */
  updateComments(id: string, updateValue: string) {
    return Comment.updateComments(this.editor, id, updateValue);
  }

  /**
   * 根据批注 id 设置高亮
   * @param id 批注 id
   * @returns 是否成功
   */
  addHightLighterByCommentID(id: string): Boolean {
    return Comment.addHightLighterByID(this.editor, id);
  }

  /**
   * 根据批注 id 取消高亮
   * @param comment_id 批注 id
   */
  unHightLighterByCommentID(comment_id: string) {
    const res = this.getInfoByCommentID(comment_id);
    this.unHightLighter(res.characters);
  }

  /**
   * 设置高亮
   * @param characters 添加高亮的字符
   */
  addHighLighter(characters: Character[]) {
    return EditorHelper.addHighLight(this.editor, characters);
  }

  /**
   * 取消高亮
   * @param characters 取消高亮的字符
   */
  unHightLighter(characters: Character[]) {
    return EditorHelper.unHighLight(this.editor, characters);
  }

  createCommentBox() {
    return CommentBox.createCommentBox(this.editor);
  }

  getCommentInfo() {
    return Comment.getCommentInfo(this.editor);
  }

  handleCusCommentIDSetByParagraphsTables(paragraphs_tables: (Paragraph | Table)[], group_id: string) {
    return Comment.handleCommentIDSetByParagraphsTables(this.editor, paragraphs_tables, group_id, true);
  }

  // 自定义批注 处理分组外的批注 document
  handleCusCommentIDSet_document() {
    const paragraphs_tables = this.editor.current_cell.paragraph.filter(paragraphOrTable => !paragraphOrTable.group_id);
    this.handleCusCommentIDSetByParagraphsTables(paragraphs_tables, "document");
  }

  // 自定义批注 处理所有分组内的批注 group
  handleCusCommentIDSet_group(current_group: Group) {
    const paragraphs_tables: (Paragraph | Table)[] = current_group.paragraph;
    this.handleCusCommentIDSetByParagraphsTables(paragraphs_tables, current_group.id);
  }

  // 处理自定义批注 id 的集合
  handleCusCommentIDSet(current_group: Group | "document" | "all") {
    return Comment.handleCommentIDSet(this.editor, current_group, true);
  }

  // 通过自定义批注ID获得自定义批注的para_path end_path character
  getInfoByCusCommentId(cusCommentId: string, cell: Cell = this.editor.current_cell) {
    return Comment.getInfoByID(this.editor, cusCommentId, true, cell);
  }

  /**
    * 根据批注 id 设置高亮
    * @param id 批注 id
    * @returns 是否成功
    */
  addHighlightByCusCommentId(id: string): Boolean {
    return Comment.addHightLighterByID(this.editor, id, true);
  }

  /**
    * 根据批注 id 取消高亮
    * @param comment_id 批注 id
    */
  removeHighlightByCusCommentId(comment_id: string) {
    const res = this.getInfoByCusCommentId(comment_id);
    this.removeCusCommentHighlight(res.characters);
  }

  /**
    * 设置高亮
    * @param characters 添加高亮的字符
    */
  addCusCommentHighlight(characters: Character[]) {
    return EditorHelper.addHighLight(this.editor, characters);
  }

  /**
    * 取消高亮
    * @param characters 取消高亮的字符
    */
  removeCusCommentHighlight(characters: Character[]) {
    return EditorHelper.unHighLight(this.editor, characters);
  }

  // 获取行高的数量
  getCountOfRowHeight(): number {
    const map = new Map();
    this.editor.pages.forEach(page => {
      for (const row of page.children) {
        if (isRow(row) && row.parent.getLocation() === "root") {
          const rowHeight = Math.ceil(row.bottom - row.top);
          if (map.has(rowHeight)) {
            map.get(rowHeight).count++;
          } else {
            map.set(rowHeight, { value: rowHeight, count: 1 });
          }
        }
      }
    });
    const arr = [...map.values()];
    arr.sort((a, b) => b.count - a.count);
    // 考虑文档只有一个表格的情况
    return arr.length ? arr[0].value : 0;
  }
}
