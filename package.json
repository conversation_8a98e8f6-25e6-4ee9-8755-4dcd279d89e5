{"name": "msun-editor-monore<PERSON>", "private": true, "version": "10.12.38", "type": "module", "scripts": {"build": "pnpm -r run build", "design": "pnpm --filter msun-lib-editor-design run serve", "base": "pnpm --filter msun-editor run start", "common": "pnpm --filter msun-lib-editor-common run start", "data": "pnpm --filter msun-lib-editor-transform run start", "vue": "pnpm --filter msun-editor-vue run serve", "mobile": "pnpm --filter msun-lib-editor-mobile run serve", "demo": "pnpm --filter msun-editor-demo run serve", "publish": "pnpm --filter msun-lib-editor-common publish --no-git-checks --registry=http://nexus.chis.msunsoft.com:8081/repository/npm-private/ && pnpm --filter msun-lib-editor-transform publish --no-git-checks --registry=http://nexus.chis.msunsoft.com:8081/repository/npm-private/ && pnpm --filter msun-editor publish --no-git-checks --registry=http://nexus.chis.msunsoft.com:8081/repository/npm-private/ && pnpm --filter msun-editor-vue publish --no-git-checks --registry=http://nexus.chis.msunsoft.com:8081/repository/npm-private/ && pnpm --filter msun-lib-editor-design publish --no-git-checks --registry=http://nexus.chis.msunsoft.com:8081/repository/npm-private/", "bump": "node ./bump-patch-version.js patch", "bump:minor": "node ./bump-patch-version.js minor"}, "workspaces": ["packages/*"], "dependencies": {}, "devDependencies": {"@changesets/cli": "2.26.2", "@manypkg/get-packages": "^2.2.1"}}