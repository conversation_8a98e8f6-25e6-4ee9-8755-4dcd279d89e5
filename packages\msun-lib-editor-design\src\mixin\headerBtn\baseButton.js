import OCRClient from "../OCRClient";
const baseStartBtnMixIn = {
  data() {
    return {
      brush: false,
      colorData: {
        fontColor: "#000",
        bgColor: "#fff",
      },
      baseStartBtn: [
        {
          type: "icon",
          icon: "icon-chexiao",
          title: "撤销(Ctrl+Z)",
          func: this.revoke,
        },
        {
          type: "icon",
          icon: "icon-huifu",
          title: "恢复(Ctrl+Y)",
          func: this.redoes,
        },
        { type: "line" },
        {
          type: "icon",
          icon: "icon-geshishua",
          title: "格式刷",
          func: this.formatBrush,
        },
        {
          type: "icon",
          icon: "icon-icon-svg-qingchuyangshi",
          title: "清除格式",
          func: this.clearStyle,
        },
        { type: "line" },
        {
          type: "select",
          dataType: "fontType",
          width: 40,
          func: this.changeFontType,
        },
        {
          type: "select",
          dataType: "fontSize",
          width: 50,
          func: this.changeFontSize,
        },
        {
          type: "icon",
          icon: "icon-Word-add",
          title: "字号加",
          func: () => {
            this.setFontSizeBiggerOrSmaller("bigger");
          },
        },
        {
          type: "icon",
          icon: "icon-Word-minus",
          title: "字号减",
          func: () => {
            this.setFontSizeBiggerOrSmaller("smaller");
          },
        },
        { type: "line" },
        {
          type: "icon",
          icon: "icon-jiacu",
          title: "加粗",
          key: "bold",
          val: true,
          func: this.fontBold,
        },
        {
          type: "icon",
          icon: "icon-qingxie1",
          title: "斜体",
          key: "italic",
          val: true,
          func: this.fontItalic,
        },
        {
          type: "icon",
          icon: "icon-Underline",
          title: "下划线",
          key: "underline",
          val: true,
          func: this.fontUnderline,
        },
        {
          type: "icon",
          icon: "icon-dblUnderLine",
          title: "双下划线",
          key: "dblUnderLine",
          val: true,
          func: this.fontDblUnderline,
        },
        {
          type: "icon",
          icon: "icon-shanchuxian",
          title: "删除线",
          key: "strikethrough",
          val: true,
          func: this.fontDeleteLine,
        },
        {
          type: "icon",
          icon: "icon-shangbiao",
          title: "上标",
          key: "script",
          val: 1,
          func: this.fontUpScript,
        },
        {
          type: "icon",
          icon: "icon-xiabiao",
          title: "下标",
          key: "script",
          val: 2,
          func: this.fontDownScript,
        },
        {
          type: "color",
          icon: "icon-A",
          title: "字体颜色",
          func: this.changeFontColor,
        },
        {
          type: "color",
          isBg: true,
          icon: "icon-beijingyanse",
          title: "背景颜色",
          func: this.changeFontColor,
        },
        { type: "line" },
        {
          type: "icon",
          icon: "icon-ziyuan",
          title: "左对齐",
          key: "align",
          val: "left",
          func: () => {
            this.changeAline("left");
          },
        },
        {
          type: "icon",
          icon: "icon-juzhongduiqi",
          title: "居中对齐",
          key: "align",
          val: "center",
          func: () => {
            this.changeAline("center");
          },
        },
        {
          type: "icon",
          icon: "icon-youduiqi",
          title: "右对齐",
          key: "align",
          val: "right",
          func: () => {
            this.changeAline("right");
          },
        },
        {
          type: "icon",
          icon: "icon-ziyuan1",
          title: "分散对齐",
          key: "align",
          val: "dispersed",
          func: () => {
            this.changeAline("dispersed");
          },
        },
        {
          type: "icon",
          icon: "icon-shangduiqi",
          title: "上对齐(单元格设置最小高度后生效)",
          key: "vertical_align",
          val: "top",
          func: () => {
            this.changeAline("top");
          },
        },
        {
          type: "icon",
          icon: "icon-shangxiajuzhong",
          title: "上下居中对齐(单元格设置最小高度后生效)",
          key: "vertical_align",
          val: "center",
          func: () => {
            this.changeAline("vertical_center");
          },
        },
        {
          type: "icon",
          icon: "icon-Q-xiaduiqi",
          title: "下对齐(单元格设置最小高度后生效)",
          key: "vertical_align",
          val: "bottom",
          func: () => {
            this.changeAline("bottom");
          },
        },
        {
          type: "icon",
          icon: "icon-docuAlign",
          title: "字符对齐",
          key: "align",
          val: "docuAlign",
          func: () => {
            this.changeAline("docuAlign");
          },
        },
        { type: "line" },
        {
          type: "fieldSymbolMenu",
          icon: "icon-wenben",
          title: "文本域边框类型",
          showType: "fieldSymbol",
          children: [
            {
              type: "textIcon",
              icon: "icon-line-bracketszhongkuohao",
              title: "默认（不打印）",
              value: "normal",
              func: () => {
                this.changeFieldSymbolType("normal");
                this.selectedSymbolIcon = "icon-line-bracketszhongkuohao";
              },
            },
            {
              type: "textIcon",
              icon: "icon-fangkuang",
              title: "边框（打印）",
              value: "printInput",
              func: () => {
                this.changeFieldSymbolType("printInput");
                this.selectedSymbolIcon = "icon-fangkuang";
              },
            },
            {
              type: "textIcon",
              icon: "icon-xuxiankuang",
              title: "边框（不打印）",
              value: "input",
              func: () => {
                this.changeFieldSymbolType("input");
                this.selectedSymbolIcon = "icon-xuxiankuang";
              },
            },
            {
              type: "textIcon",
              icon: "icon-zitixiahuaxian",
              title: "底线（打印）",
              value: "line",
              func: () => {
                this.changeFieldSymbolType("line");
                this.selectedSymbolIcon = "icon-zitixiahuaxian";
              },
            },
          ],
        },
        {
          type: "fieldAlign",
          icon: "icon-field-zuoduiqi",
          title: "文本域内字符对齐方式（当文本域设置最小宽度后生效）",
          showType: "fieldAlign",
          children: [
            {
              type: "textIcon",
              icon: "icon-field-zuoduiqi",
              title: "左对齐",
              value: "left",
              func: () => {
                this.changeFieldAlign("left");
                this.selectedFieldAlignIcon = "icon-field-zuoduiqi";
              },
            },
            {
              type: "textIcon",
              icon: "icon-field-youduiqi",
              title: "右对齐",
              value: "right",
              func: () => {
                this.changeFieldAlign("right");
                this.selectedFieldAlignIcon = "icon-field-youduiqi";
              },
            },
            {
              type: "textIcon",
              icon: "icon-field-juzhongduiqi",
              title: "居中对齐",
              value: "center",
              func: () => {
                this.changeFieldAlign("center");
                this.selectedFieldAlignIcon = "icon-field-juzhongduiqi";
              },
            },
          ],
        },
        // {
        //   type: "icon",
        //   icon: "icon-shujuhuanyuan",
        //   title: "插入辅助文本域(自动命名)",
        //   func: () => {
        //     this.insertHelperField();
        //   },
        // },
        {
          type: "icon",
          icon: "icon-shujuhuanyuan",
          title: "重新插入文本域（保持相同name）",
          func: () => {
            this.reInsertField();
          },
        },
        { type: "line" },
        {
          type: "groupMenu",
          icon: "icon-suoyoukuangxian",
          title: "表格线",
          showType: "tableLine",
          children: [
            {
              type: "textIcon",
              icon: "icon-xiakuangxian",
              title: "下边线",
              value: "under_line",
              func: () => {
                this.dealTableLine("under_line");
                this.selectedIcon = "icon-xiakuangxian";
              },
            },
            {
              type: "textIcon",
              icon: "icon-shangkuangxian",
              title: "上边线",
              value: "top_line",
              func: () => {
                this.dealTableLine("top_line");
                this.selectedIcon = "icon-shangkuangxian";
              },
            },
            {
              type: "textIcon",
              icon: "icon-zuokuangxian",
              title: "左边线",
              value: "left_line",
              func: () => {
                this.dealTableLine("left_line");
                this.selectedIcon = "icon-zuokuangxian";
              },
            },
            {
              type: "textIcon",
              icon: "icon-youkuangxian",
              title: "右边线",
              value: "right_line",
              func: () => {
                this.dealTableLine("right_line");
                this.selectedIcon = "icon-youkuangxian";
              },
            },
            {
              type: "textIcon",
              icon: "icon-wuxiankuang",
              title: "清除框线",
              value: "no_line",
              func: () => {
                this.dealTableLine("no_line");
                this.selectedIcon = "icon-wuxiankuang";
              },
            },
            {
              type: "textIcon",
              icon: "icon-suoyoukuangxian",
              title: "所有框线",
              value: "all_line",
              func: () => {
                this.dealTableLine("all_line");
                this.selectedIcon = "icon-suoyoukuangxian";
              },
            },
            {
              type: "textIcon",
              icon: "icon-waicekuangxian",
              title: "外部框线",
              value: "out_line",
              func: () => {
                this.dealTableLine("out_line");
                this.selectedIcon = "icon-waicekuangxian";
              },
            },
            {
              type: "textIcon",
              icon: "icon-neibukuangxian",
              title: "内部框线",
              value: "inside_line",
              func: () => {
                this.dealTableLine("inside_line");
                this.selectedIcon = "icon-neibukuangxian";
              },
            },
            {
              type: "textIcon",
              icon: "icon-neibuhengxiankuang",
              title: "内部横框线",
              value: "inside_flat_line",
              func: () => {
                this.dealTableLine("inside_flat_line");
                this.selectedIcon = "icon-neibuhengxiankuang";
              },
            },
            {
              type: "textIcon",
              icon: "icon-neibushuxiankuang",
              title: "内部竖框线",
              value: "inside_vertical_line",
              func: () => {
                this.dealTableLine("inside_vertical_line");
                this.selectedIcon = "icon-neibushuxiankuang";
              },
            },
            {
              type: "textIcon",
              icon: "icon-xieshangkuangxian",
              title: "斜上框线",
              value: "inside_inclined_bottom_line",
              func: () => {
                this.dealTableLine("inside_inclined_bottom_line");
                this.selectedIcon = "icon-xieshangkuangxian";
              },
            },

            {
              type: "textIcon",
              title: "双斜上框线",
              value: "inside_inclined_bottom_line2",
              icon: "icon-shuangshangxiexian",
              func: () => {
                this.dealTableLine("icon-shuangshangxiexian");
                this.selectedIcon = "icon-shuangshangxiexian";
              },
            },
            {
              type: "textIcon",
              icon: "icon-xiexiakuangxian",
              title: "斜下框线",
              value: "inside_inclined_top_line",
              func: () => {
                this.dealTableLine("inside_inclined_top_line");
                this.selectedIcon = "icon-xiexiakuangxian";
              },
            },
            {
              type: "textIcon",
              title: "双斜下框线",
              value: "inside_inclined_top_line2",
              icon: "icon-shuangxiaxiexian",
              func: () => {
                this.dealTableLine("icon-shuangxiaxiexian");
              },
            },
          ],
        },
        {
          type: "icon",
          icon: "icon-hebingdanyuange",
          title: "合并单元格",
          func: this.mergeCell,
        },
        {
          type: "icon",
          icon: "icon-771bianjiqi_chaifendanyuange",
          title: "拆分单元格",
          func: this.splitCell,
        },
        {
          type: "icon",
          icon: "icon-fengebiaoge",
          title: "分割表格，将选区范围内拆分为多个表格",
          func: () => {
            this.splitTableByFullRowsWithAnchors();
          },
        },
        {
          type: "icon",
          icon: "icon-biaogepinjie",
          title: "表格拼接，使用锚点文本域拼接多个表格，对已拼接的使用会隔离开",
          func: () => {
            this.multiTableSplicing();
          },
        },
        { type: "line" },
        {
          type: "icon",
          icon: "icon-wuxuliebiao",
          title: "无序列表",
          func: this.toList,
        },
        {
          type: "orderList",
          icon: "icon-youxuliebiao",
          title: "有序列表",
          showType: "orderList",
          func: this.showIconTextMenu,
        },
        { type: "line" },
        {
          type: "icon",
          icon: "icon-print1",
          title: "直接打印",
          func: () => {
            this.immediatelyPrint("json");
          },
        },
        {
          type: "icon",
          icon: "icon-print",
          title: "系统打印预览",
          func: this.systemPrint,
        },
        {
          type: "icon",
          icon: "icon-shangchuan",
          title: "上传",
          func: this.upload,
        },
      ],
      ocrClient: new OCRClient(),
      fileList: [],
    };
  },
  mounted() {
    if (this.instance && this.instance.localTest.useLocal) {
      this.baseStartBtn.push({
        type: "icon",
        icon: "icon-biaodashi",
        title: "测试",
        func: this.test,
      });
    }
  },
  methods: {
    initFontStyleAndType() {
      this.instance.builtInVariable.fontTypeList.forEach((e) => {
        this.selectData.fontType.push({
          option: e,
          value: e,
        });
      });
      this.selectData.fontSize = this.instance.builtInVariable.fontSizeList;
    },
    // 自适应大小
    adaptSize() {
      const { editor } = this.instance;
      editor.setMaxViewScale();
      this.editorScale = editor.viewScale;
    },
    // 恢复成1倍
    recovery() {
      const { editor } = this.instance;
      editor.setViewScale(1);
      this.editorScale = editor.viewScale;
    },
    // 撤销
    revoke() {
      this.instance.history.undo();
    },
    // 恢复
    redoes() {
      this.instance.history.redo();
    },
    // 保存
    save() {
      this.$editor.info("请自行定义保存方法");
    },
    saveAs() {
      this.instance.editor.download("模板.json");
    },
    // 格式刷
    formatBrush() {
      this.brush = true;
      this.instance.editor.formatBrush();
    },
    // 清除样式
    clearStyle() {
      this.instance.editor.deleteStyle();
    },
    // 修改字体类型
    changeFontType(value, option, item) {
      this.initData[item.dataType] = {
        option,
        value,
      };
      this.instance.editor.change_font_style({ family: value });
    },
    setFontSizeBiggerOrSmaller(value) {
      this.instance.editor.setSelectionCharacterSize(value);
      this.instance.editor.refreshDocument();
    },
    //   改变字体大小
    changeFontSize(value, option, item) {
      this.initData[item.dataType] = {
        option,
        value,
      };
      this.instance.editor.change_font_style({ height: value });
    },
    // 字体加粗
    fontBold() {
      const bold = this.instance.editor.contextState.font.bold;
      this.instance.editor.change_font_style({ bold: !bold });
      this.$forceUpdate();
    },
    // 字体倾斜
    fontItalic() {
      const italic = this.instance.editor.contextState.font.italic;
      this.instance.editor.change_font_style({ italic: !italic });
      this.$forceUpdate();
    },
    // 字体下划线
    fontUnderline() {
      const underline = this.instance.editor.contextState.font.underline;
      this.instance.editor.change_font_style({ underline: !underline });
      this.$forceUpdate();
    },
    // 字体双划线
    fontDblUnderline() {
      const dblUnderLine = this.instance.editor.contextState.font.dblUnderLine;
      this.instance.editor.change_font_style({
        dblUnderLine: !dblUnderLine,
      });
      this.$forceUpdate();
    },
    // 字体删除线
    fontDeleteLine() {
      const strikethrough =
        this.instance.editor.contextState.font.strikethrough;
      this.instance.editor.change_font_style({
        strikethrough: !strikethrough,
      });
      this.$forceUpdate();
    },
    // 上标
    fontUpScript() {
      const script = this.instance.editor.contextState.font.script;
      this.instance.editor.change_font_style({ script: script !== 1 ? 1 : 3 });
      this.$forceUpdate();
    },
    // 下标
    fontDownScript() {
      const script = this.instance.editor.contextState.font.script;
      this.instance.editor.change_font_style({ script: script !== 2 ? 2 : 3 });
      this.$forceUpdate();
    },
    // 打开/关闭颜色选择器
    showColorPicker(isBg) {
      let colorVue;
      if (isBg) {
        colorVue = this.$refs.bgcolorPicker[0];
      } else {
        colorVue = this.$refs.colorPicker[0];
      }
      colorVue.openStatus = true;
    },
    // 关闭颜色选择器
    closeColorPicker() {
      const colorVue = this.$refs.colorPicker[0];
      const bgColorVue = this.$refs.bgcolorPicker[0];
      if (colorVue && bgColorVue) {
        colorVue.closePanel();
        bgColorVue.closePanel();
      }
    },
    // 颜色选择器修改字体颜色回调
    changeColor(e, isBg) {
      if (isBg) {
        this.colorData.bgColor = e;
        this.instance.editor.change_font_style({ bgColor: e });
      } else {
        this.colorData.fontColor = e;
        this.instance.editor.change_font_style({ color: e });
      }
      this.instance.editor.focus();
    },
    // 点击字母修改字体颜色
    changeFontColor(isBg) {
      if (isBg) {
        this.instance.editor.change_font_style({
          bgColor: this.colorData.bgColor,
        });
      } else {
        this.instance.editor.change_font_style({
          color: this.colorData.fontColor,
        });
      }
    },
    // 对齐方向
    changeAline(data) {
      if (data === "top" || data === "bottom" || data === "vertical_center") {
        data = data === "vertical_center" ? "center" : data;
        this.paraStyle.vertical_align = data;
        this.instance.editor.setVerticalAlign(data);
      } else {
        this.paraStyle.align = data;
        this.instance.editor.changeContentAlign(data);
      }
    },
    changeFieldAlign(type) {
      const editor = this.instance.editor;
      const selection = editor.selection;

      if (selection.isCollapsed) {
        editor.reviseFieldAttr({ align: type });
      } else {
        const fieldsId = Object.keys(
          selection.selected_fields_chars.fieldIdVsChars
        );
        if (fieldsId.length) {
          const selectedFields = [];
          fieldsId.forEach((id) => {
            const field = editor.getFieldById(id);
            selectedFields.push(field);
          });
          selectedFields.forEach((f) => {
            editor.reviseFieldAttr({ field: f, align: type });
          });
          editor.focus();
        }
      }
    },
    //改变文本域边框类型
    changeFieldSymbolType(type) {
      const editor = this.instance.editor;
      const selection = editor.selection;

      if (selection.isCollapsed) {
        editor.reviseFieldAttr({ display_type: type });
      } else {
        const fieldsId = Object.keys(
          selection.selected_fields_chars.fieldIdVsChars
        );
        if (fieldsId.length) {
          const selectedFields = [];
          fieldsId.forEach((id) => {
            const field = editor.getFieldById(id);
            selectedFields.push(field);
          });
          selectedFields.forEach((f) => {
            editor.reviseFieldAttr({ field: f, display_type: type });
          });
          editor.focus();
        }
      }
    },
    // 立即打印
    async immediatelyPrint(type) {
      // 打印份数
      const afterPrint = function (e) {};
      // 立即打印
      this.instance.immediatelyPrint(null, afterPrint, type);
    },
    // 浏览器打印
    browserPrint() {
      const afterPrint = function (e) {};
      this.instance.editor.print("browserPrint", afterPrint);
    },
    // 系统打印
    systemPrint() {
      const afterPrint = function (e) {};
      // 老版图片打印预览
      this.instance.systemPrint(afterPrint, "json");
    },
    async processConversion() {
      try {
        const options = {
          apply_document_tree: 1,
          apply_merge: 1,
          catalog_details: 1,
          dpi: 144,
          formula_level: 1,
          get_excel: 1,
          get_image: "objects",
          markdown_details: 1,
          page_count: 1000,
          page_details: 1,
          paratext_mode: "annotation",
          parse_mode: "auto",
          table_flavor: "html",
          crop_dewarp: 1, // 1 是裁剪 0 是不裁剪
        };

        const responseStr = await this.ocrClient.recognize(
          this.fileList[0],
          options
        );
        const response = JSON.parse(responseStr);
        console.log("---扫描结果--", response);

        // 展示OCR识别结果
        const markdownStr = response.result.markdown;
        const html = marked(markdownStr);
        console.log(html, "这是我要的吧");
        debugger;
      } catch (error) {
        this.converting = false;
        message.error("OCR处理失败: " + error.message);
      }
    },
    upload() {
      const password = localStorage.getItem("editorPassword");
      if (!password || password !== "Bianjiqiocr") {
        // 需要页面弹窗 让用户输入密码跟 Bianjiqiocr 做比对 如果不通过就 return 掉 如果通过了 就更新 localStorage 继续往下走
      }
      // 创建文件输入元素
      const input = document.createElement("input");
      input.type = "file";
      input.accept = "image/*"; // 只接受图片文件
      input.multiple = false; // 只允许选择一个文件
      input.style.display = "none";

      // 文件选择处理函数
      const handleFileSelect = async (event) => {
        const file = event.target.files[0];
        if (!file) return;
        // 读取文件内容
        const reader = new FileReader();
        reader.readAsText(file);

        this.fileList = [file];
        await this.processConversion();
        // 清理input元素
        document.body.removeChild(input);
        this.fileList = [];
      };

      // 绑定事件并触发文件选择
      input.addEventListener("change", handleFileSelect);
      document.body.appendChild(input);
      input.click();
    },
    // 打印预览
    printView() {
      const afterPrint = function () {};
      // pdf的浏览器打印预览
      this.instance.printView("print", afterPrint);
    },

    // 打印PDF
    convertPDF() {
      // 下载pdf
      this.instance.convertPDF();
    },
    toList() {
      this.instance.editor.addList(false);
    },
    toOrderList(data) {
      this.instance.editor.addList(true);
      this.instance.editor.changeListNumStyle(data);
      this.showIconSelect = "";
    },
    multiTableSplicing() {
      const res = this.instance.editor.multiTableSplicing();
      if (res) {
        this.$editor.success("已显示/隐藏表格下方空行");
      } else {
        this.$editor.warn("未发现表格下方需要显示或隐藏的空行");
      }
    },
    splitTableByFullRowsWithAnchors() {
      const res = this.instance.editor.splitTableByFullRowsWithAnchors();
      if (res) {
        this.$editor.success("已拆分");
      } else {
        this.$editor.warn("未发现需要拆分的表格");
      }
    },
  },
};
export default baseStartBtnMixIn;
