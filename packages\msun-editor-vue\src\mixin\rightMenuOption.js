// 所有的右键菜单选项

// 基础
export const baseMenuList = [
  {
    value: "查找替换",
    key: "searchAndReplace",
    icon: "icon-chazhaotihuan-replace-xian",
    isDisabled: false, // 是否禁用
    shortCutKey: "Ctrl + F",
  },
  {
    value: "打开文件",
    key: "openFile",
    icon: "icon-wenbenyu",
    isDisabled: false,
  },
  {
    value: "重做",
    key: "redo",
    icon: "icon-huifu",
    isDisabled: false, // 是否禁用
    shortCutKey: "Ctrl + Y",
  },
  {
    value: "系统打印",
    key: "systemPrint",
    icon: "icon-yijixitong_dayinlingyaodan",
    isDisabled: false, // 是否禁用
  },
  {
    value: "打印预览",
    key: "printView",
    icon: "icon-yijixitong_dayinlingyaodan",
    isDisabled: false, // 是否禁用
  },
  {
    value: "打印当前页",
    key: "printCurrentPage",
    icon: "icon-yijixitong_dayinlingyaodan",
    isDisabled: false, // 是否禁用
  },
  {
    value: "开启/关闭图形模式",
    key: "shape_mode",
    icon: "icon-yijixitong_dayinlingyaodan",
    isDisabled: false, // 是否禁用
  },
  {
    value: "开启插入线",
    key: "insert_shape_line",
    icon: "icon-yijixitong_dayinlingyaodan",
    isDisabled: false, // 是否禁用
  },
  {
    value: "开启插入椭圆",
    key: "insert_shape_elliptic",
    icon: "icon-yijixitong_dayinlingyaodan",
    isDisabled: false, // 是否禁用
  },
  {
    value: "开启插入叉",
    key: "insert_shape_cross",
    icon: "icon-yijixitong_dayinlingyaodan",
    isDisabled: false, // 是否禁用
  },
  {
    value: "开启插入矩形",
    key: "insert_shape_rect",
    icon: "icon-yijixitong_dayinlingyaodan",
    isDisabled: false, // 是否禁用
  },
  {
    value: "开启插入折线",
    key: "insert_fold_line",
    icon: "icon-yijixitong_dayinlingyaodan",
    isDisabled: false, // 是否禁用
  },
  {
    value: "直接打印",
    key: "immediatelyPrint",
    icon: "icon-yijixitong_dayinlingyaodan",
    isDisabled: false, // 是否禁用
  },
  {
    value: "立即续打",
    key: "printContinueImmediate",
    icon: "icon-yijixitong_dayinlingyaodan",
    isDisabled: false, // 是否禁用
  },
  {
    value: "插入分数",
    key: "insertFraction",
    icon: "icon-yijixitong_dayinlingyaodan",
    isDisabled: false, // 是否禁用
  },
  {
    value: "插入按钮",
    key: "insert_button",
    icon: "icon-yijixitong_dayinlingyaodan",
    isDisabled: false, // 是否禁用
  },
  {
    value: "区域打印",
    key: "area_print",
    icon: "icon-yijixitong_dayinlingyaodan",
    isDisabled: false, // 是否禁用
  },
  {
    value: "添加批注",
    key: "add_comment",
    icon: "icon-yijixitong_dayinlingyaodan",
    isDisabled: false, // 是否禁用
  },
  {
    value: "医学公式",
    key: "medicalCalcFormula",
    icon: "icon-yixuegongshi",
    isDisabled: false,
  },
  {
    value: "页面设置",
    key: "setPageConfig",
    icon: "icon-yemianshezhi",
    isDisabled: false, // 是否禁用
  },
  {
    value: "开启/关闭水印功能",
    key: "water_mark",
    icon: "icon-shuiyin",
    isDisabled: false, // 是否禁用
  },
  {
    value: "插入水印图片",
    key: "insert_mark_image",
    icon: "icon-shuiyin",
    isDisabled: false, // 是否禁用
  },
  {
    value: "插入自定义水印图片",
    key: "insert_custom_mark_image",
    icon: "icon-shuiyin",
    isDisabled: false, // 是否禁用
  },
  {
    value: "插入水印文字",
    key: "insert_mark_text",
    icon: "icon-shuiyin",
    isDisabled: false, // 是否禁用
  },
  {
    value: "关闭插入水印文字",
    key: "close_mark_text",
    icon: "icon-shuiyin",
    isDisabled: false, // 是否禁用
  },
  {
    value: "设置水印属性",
    key: "set_mark_prop",
    icon: "icon-shuiyin",
    isDisabled: false, // 是否禁用
  },
  {
    value: "单独模式",
    key: "single_mark_mode",
    icon: "icon-shuiyin",
    isDisabled: false, // 是否禁用
  },
  {
    value: "重复模式",
    key: "repeat_mark_mode",
    icon: "iicon-shuiyin",
    isDisabled: false, // 是否禁用
  },
  {
    value: "水印字体",
    key: "text_mark_style",
    icon: "icon-shuiyin",
    isDisabled: false, // 是否禁用
  },
  {
    value: "撤销",
    key: "ctrlZ",
    icon: "icon-chexiao",
    isDisabled: false,
    shortCutKey: "Ctrl + Z",
  },
  {
    value: "复制",
    key: "copy",
    icon: "icon-fuzhi",
    isDisabled: false,
    shortCutKey: "Ctrl + C",
  },
  {
    value: "剪切",
    key: "cut",
    icon: "icon-jianqie",
    isDisabled: false,
    shortCutKey: "Ctrl + X",
  },
  {
    value: "粘贴",
    key: "paste",
    icon: "icon-jurassic_paste",
    isDisabled: false,
    shortCutKey: "Ctrl + V",
  },
  {
    value: "粘贴为纯文本",
    key: "pasteText",
    icon: "icon-jurassic_paste",
    isDisabled: false,
    shortCutKey: "Ctrl + Shift + V",
  },
  {
    value: "删除",
    key: "deleteSelection",
    icon: "icon-delete",
    isDisabled: false,
    shortCutKey: "Backspace",
  },
  {
    value: "字体",
    key: "font",
    icon: "icon-zitishezhi-shuangse",
    isDisabled: false,
  },
  {
    value: "段落",
    key: "paragraph",
    icon: "icon-docuAlign",
    isDisabled: false,
  },
  {
    value: "倾斜水印",
    key: "italicWaterMark",
    icon: "icon-shitifenzu",
    isDisabled: false,
  },
  {
    value: "插入分组",
    key: "insertGroup",
    icon: "icon-shitifenzu",
    isDisabled: false,
  },
  {
    value: "插入水平线",
    key: "insertHorizontalLine",
    icon: "icon-fengexian",
    isDisabled: false,
  },
  {
    value: "分组排序",
    key: "sortGroup",
    icon: "icon-paixu",
    isDisabled: false,
  },
  {
    value: "插入列表",
    key: "insertList",
    icon: "icon-liebiao",
    isDisabled: false,
  },
  {
    value: "扫描解析",
    key: "scanAndAnalyze",
    icon: "icon-scan_document_icon",
    isDisabled: false,
  },
];

// 插入表格
export const insertTbl = [
  {
    value: "插入表格",
    key: "insertTable",
    icon: "icon-biaoge",
    isDisabled: false,
  },
];

// 文本域属性
export const fieldPropMethod = [
  {
    value: "文本域属性",
    key: "fieldProp",
    icon: "icon-wenbenyu",
    isDisabled: false,
  },
];
// 卡尺属性
export const caliperPropMethod = [
  {
    value: "卡尺属性",
    key: "caliperProp",
    icon: "icon-wenbenyu",
    isDisabled: false,
  },
];
// 复选框属性
export const boxTypeField = [
  {
    value: "复选框属性",
    key: "choiceProp",
    icon: "icon-fuxuankuang",
    isDisabled: false,
  },
];

// 分组相关
export const groupMethod = [
  {
    value: "删除分组",
    key: "delGroup",
    icon: "icon-shanchu",
    isDisabled: false,
  },
  {
    value: "修改分组属性",
    key: "modifyProperty",
    icon: "icon-shijian",
    isDisabled: false,
  },
  {
    value: "锁定/解锁分组",
    key: "lockGroup",
    icon: "icon-lock",
    isDisabled: false,
  },
];

// 下载PDF
export const convertPDFMethod = [
  {
    value: "下载PDF",
    key: "convertPDF",
    icon: "icon-download",
    isDisabled: false,
  },
];

// 续打
export const printContinueMethod = [
  {
    value: "续打",
    key: "printContinue",
    icon: "icon-print",
    isDisabled: false,
  },
];

// 对齐方式
export const alignTypeList = [
  {
    value: "左对齐",
    key: "alignLeft",
    icon: "icon-ziyuan",
    isDisabled: false,
  },
  {
    value: "居中对齐",
    key: "alignCenter",
    icon: "icon-juzhongduiqi",
    isDisabled: false,
  },
  {
    value: "右对齐",
    key: "alignRight",
    icon: "icon-youduiqi",
    isDisabled: false,
  },
];

// 重新/继续编号
export const listNumRestart = [
  {
    value: "重新/继续编号",
    key: "restartListIndex",
    icon: "icon-ziyuan",
    isDisabled: false,
  },
];

// 基础的插入
export const insertListAboutBase = [
  {
    value: "文本域",
    key: "insertField",
    icon: "icon-wenbenyu",
    isDisabled: false,
  },
  {
    value: "单选框",
    key: "insertRadio",
    icon: "icon-radio-checked",
    isDisabled: false,
  },
  {
    value: "复选框",
    key: "insertCheckbox",
    icon: "icon-fuxuankuang",
    isDisabled: false,
  },
  {
    value: "卡尺",
    key: "insertCaliper",
    icon: "icon-fuxuankuang",
    isDisabled: false,
  },
  {
    value: "自定义多选框",
    key: "choice",
    icon: "icon-fuxuankuang",
    isDisabled: false,
  },
  {
    value: "医学表达式",
    key: "insertFormula",
    icon: "icon-biaodashi",
    isDisabled: false,
  },
  {
    value: "图片",
    key: "insertLocalImage",
    icon: "icon-charutupian",
    isDisabled: false,
  },
  {
    value: "插入可标记图",
    key: "insertLocalMarkableImage",
    icon: "icon-charutupian",
    isDisabled: false,
  },
];

// 点在表格内时 默认的右键菜单选项
export const tableMenuList = [
  {
    value: "表格属性",
    key: "modifyTableAttr",
    icon: "icon-biaoge",
    isDisabled: false,
  },
  {
    value: "单元格属性",
    key: "modifyCellAttr",
    icon: "icon-biaoge",
    isDisabled: false,
  },
  {
    value: "设置单元格成组", // 设置单元格成组
    key: "setCellsGroup",
    icon: "icon-biaoge",
    isDisabled: false,
  },
  {
    value: "取消单元格成组", // 取消单元格成组
    key: "cancelCellsGroup",
    icon: "icon-biaoge",
    isDisabled: false,
  },
  {
    value: "设置固定表头",
    key: "setFixedTableHeader",
    icon: "icon-biaoge",
    isDisabled: false,
  },
  {
    value: "取消固定表头",
    key: "cancelFixedTableHeader",
    icon: "icon-biaoge",
    isDisabled: false,
  },
  {
    value: "禁止删除该表格",
    key: "disabledDeleteTheTable",
    icon: "icon-jinzhitishi",
    isDisabled: false,
  },
  {
    value: "允许删除该表格",
    key: "allowDeleteTheTable",
    icon: "icon-52",
    isDisabled: false,
  },
  {
    value: "合并单元格",
    key: "mergeCell",
    icon: "icon-hebingdanyuange",
    isDisabled: false,
  },
  {
    value: "拆分单元格",
    key: "splitCell",
    icon: "icon-771bianjiqi_chaifendanyuange",
    isDisabled: false,
  },
  // {
  //   value: "选区修改表格背景色",
  //   key: "changeTableBgColor",
  //   icon: "icon-771bianjiqi_chaifendanyuange",
  //   isDisabled: false,
  // },
  {
    value: "平均行高",
    key: "averageRowHeight",
    icon: "icon-771bianjiqi_chaifendanyuange",
    isDisabled: false,
  },
  {
    value: "平均列宽",
    key: "averageColWidth",
    icon: "icon-771bianjiqi_chaifendanyuange",
    isDisabled: false,
  },
];

// 关于表格的删除选项
export const deleteListAboutTbl = [
  {
    value: "删除表格",
    key: "deleteTbl",
    icon: "icon-shanchubiaoge",
    isDisabled: false,
  },
  {
    value: "删除表格行",
    key: "delTblRow",
    icon: "icon-shanchuhang",
    isDisabled: false,
  },
  {
    value: "删除表格列",
    key: "delTblCol",
    icon: "icon-shanchulie",
    isDisabled: false,
  },
  {
    value: "删除表格上方空行",
    key: "deleteEmptyParaUp",
    icon: "icon-delete",
    isDisabled: false,
  },
  {
    value: "删除表格下方空行",
    key: "deleteEmptyParaDown",
    icon: "icon-delete",
    isDisabled: false,
  },
];

// 关于表格的插入
export const insertListAboutTbl = [
  {
    value: "表格上方插入空行",
    key: "insertEmptyParaFromUp",
    icon: "icon-docuAlign",
    isDisabled: false,
  },
  {
    value: "表格下方插入空行",
    key: "insertEmptyParaFromDown",
    icon: "icon-docuAlign",
    isDisabled: false,
  },
  {
    value: "上方插入一行",
    key: "insertRowFromUp",
    icon: "icon-insertrowabove",
    isDisabled: false,
  },
  {
    value: "下方插入一行",
    key: "insertRowFromDown",
    icon: "icon-insertrowbelow",
    isDisabled: false,
  },
  {
    value: "左侧插入一列",
    key: "insertColFromLeft",
    icon: "icon-insertrowleft",
    isDisabled: false,
  },
  {
    value: "右侧插入一列",
    key: "insertColFromRight",
    icon: "icon-insertrowright",
    isDisabled: false,
  },
];

// 编辑页眉页脚
export const headerFooter = [
  {
    value: "编辑页眉页脚",
    key: "editorHF",
    icon: "icon-bianji",
    isDisabled: false,
  },
];

// 插入页码
export const footerList = [
  {
    value: "插入页码",
    key: "insertPageNum",
    icon: "icon-xuanxiangcharu",
    isDisabled: false,
  },
];

// 编辑正文
export const editorRootCell = [
  {
    value: "编辑",
    key: "editorRootCell",
    icon: "icon-bianji",
    isDisabled: false,
  },
];

// 续打和区域打印模式下的右键菜单选项
export const printOption = [
  {
    value: "续打",
    key: "printContinue",
    icon: "icon-print",
    isDisabled: false,
  },
  {
    value: "区域打印",
    key: "area_print",
    icon: "icon-yijixitong_dayinlingyaodan",
    isDisabled: false, // 是否禁用
  },
  {
    value: "打印预览",
    key: "printView",
    icon: "icon-yijixitong_dayinlingyaodan",
    isDisabled: false, // 是否禁用
  },
  {
    value: "打印当前页",
    key: "printCurrentPage",
    icon: "icon-yijixitong_dayinlingyaodan",
    isDisabled: false, // 是否禁用
  },
  {
    value: "直接打印",
    key: "immediatelyPrint",
    icon: "icon-yijixitong_dayinlingyaodan",
    isDisabled: false, // 是否禁用
  },
  {
    value: "系统打印",
    key: "systemPrint",
    icon: "icon-yijixitong_dayinlingyaodan",
    isDisabled: false, // 是否禁用
  },
];

// 默认的右键菜单配置
export const defaultRightMenuConfig = [
  // 默认右键菜单配置
  // value: 自定义显示文字 icon: 自定义显示图标 line: 自定义是否加横线 children: 二级菜单数组 handler：添加自定义事件 key必须是加custom的
  { key: "ctrlZ" }, // 撤销
  { key: "save", value: "保存" }, // 保存
  { key: "redo" }, // 重做
  { key: "openFile", line: true }, // 打开文件
  { key: "printContinue" }, // value: "续打",
  { key: "area_print", value: "区域打印" }, // 打印预览
  { key: "printView", value: "打印预览" }, // 打印预览
  { key: "printCurrentPage", value: "打印当前页" }, // 打印当前页
  { key: "immediatelyPrint", value: "直接打印" }, // 直接打印
  { key: "printContinueImmediate", value: "立即续打" }, // 立即续打
  { key: "systemPrint", line: true }, // value: "系统打印",
  { key: "add_comment", value: "添加批注" }, // 添加批注
  { key: "water_mark", value: "开启水印模式" }, // 开启水印模式
  { key: "shape_mode", value: "开启图形模式" }, // 开启关闭图形编辑
  { key: "setPageConfig", value: "页面设置" }, //页面设置
  { key: "italicWaterMark", line: true }, // 倾斜水印

  { key: "copy" }, // 复制
  { key: "cut" }, // 剪切
  { key: "paste" }, // 粘贴
  { key: "deleteSelection" }, // 删除
  { key: "pasteText", line: true }, // 粘贴为纯文本
  { key: "mergeCell" }, // 合并单元格
  { key: "splitCell" }, // 拆分单元格
  { key: "modifyTableAttr" }, // 表格属性
  { key: "modifyCellAttr" }, // 单元格属性
  { key: "setCellsGroup" }, // 设置单元格成组
  { key: "cancelCellsGroup" }, // 取消单元格成组
  {
    value: "表格操作",
    icon: "icon-biaoge",
    children: [
      { key: "setFixedTableHeader" }, // 设置固定表头
      { key: "cancelFixedTableHeader" }, // 设置固定表头
      { key: "insertEmptyParaFromUp" }, // 上方插入空行
      { key: "insertEmptyParaFromDown" }, // 下方插入空行
      { key: "insertRowFromUp" }, // 从上方插入一行
      { key: "insertRowFromDown" }, // 从下方插入一行
      { key: "insertColFromLeft" }, // 从左侧插入一列
      { key: "insertColFromRight" }, // 从右侧插入一列
      { key: "delTblRow" }, // 删除行
      { key: "delTblCol" }, // 删除列
      { key: "deleteEmptyParaUp" }, // 删除表格上方的空行
      { key: "deleteEmptyParaDown" }, // 删除表格下方的空行
      // { key: "changeTableBgColor" }, // 选区修改表格背景色
      { key: "averageRowHeight" }, // 选区修改表格背景色
      { key: "averageColWidth" }, // 选区修改表格背景色
      { key: "deleteTbl" }, // 删除表格
      { key: "disabledDeleteTheTable" }, // 禁止删除该表格
      { key: "allowDeleteTheTable" }, // 允许删除该表格
    ],
  },
  { key: "tableLine", line: true }, // value: "表格线"
  { key: "searchAndReplace" }, // 查找替换
  { key: "fieldProp" }, // 文本域属性
  { key: "caliperProp" }, // 卡尺属性
  { key: "choiceProp" }, // 复选框属性
  { key: "convertPDF" }, // value: "下载PDF",
  { key: "restartListIndex" }, // 重新编号
  {
    value: "插入",
    key: "custom-insert",
    icon: "icon-docuAlign",
    children: [
      { key: "insertFraction" }, // 插入分数
      { key: "insert_button", value: "插入按钮" }, // 插入按钮
      { key: "insertField" }, // value: "插入文本域",
      { key: "insertRadio" }, // value: "插入单选框",
      { key: "insertCheckbox" }, //  value: "插入复选框",
      { key: "insertCaliper" }, //  value: "插入卡尺",
      { key: "choice" }, // 自定义多选框
      { key: "medicalCalcFormula" }, // 打开医学公式弹窗
      { key: "insertFormula" }, //  value: "插入医学表达式",
      { key: "insertLocalImage" }, /// value: "插入图片",
      { key: "insertTable" }, // value: "插入表格",
      { key: "insertGroup" }, // value: "插入分组",
      { key: "insertList" }, // value: "插入列表",
      { key: "insertHorizontalLine" }, //value: "插入水平线"
    ],
    line: true,
  },
  { key: "sortGroup" }, // value: "分组排序",
  { key: "lockGroup" }, // 锁定/解锁分组
  { key: "delGroup" }, // value: "删除分组",
  { key: "modifyProperty", line: true }, // value: "修改分组时间",
  { key: "paragraph" }, // 段落
  { key: "scanAndAnalyze" }, // 扫描解析 图像识别
  { key: "font" }, // 字体
  { key: "buttonEdit" }, // 编辑按钮
  { key: "insertPageNum" }, // 插入页码
];

// 表格线
export const tableLine = {
  value: "表格线",
  key: "tableLine",
  icon: "icon-wuxiankuang",
  isDisabled: false,
  children: [
    {
      value: "下框线",
      key: "tableLine_under_line",
      icon: "icon-xiakuangxian",
      isDisabled: false,
    },
    {
      value: "上框线",
      key: "tableLine_top_line",
      icon: "icon-shangkuangxian",
      isDisabled: false,
    },
    {
      value: "左框线",
      key: "tableLine_left_line",
      icon: "icon-zuokuangxian",
      isDisabled: false,
    },
    {
      value: "右框线",
      key: "tableLine_right_line",
      icon: "icon-youkuangxian",
      isDisabled: false,
    },
    {
      value: "无框线",
      key: "tableLine_no_line",
      icon: "icon-wuxiankuang",
      isDisabled: false,
    },
    {
      value: "所有框线",
      key: "tableLine_all_line",
      icon: "icon-suoyoukuangxian",
      isDisabled: false,
    },
    {
      value: "外侧框线",
      key: "tableLine_out_line",
      icon: "icon-waicekuangxian",
      isDisabled: false,
    },
    {
      value: "内部框线",
      key: "tableLine_inside_line",
      icon: "icon-neibukuangxian",
      isDisabled: false,
    },
    {
      value: "内部横框线",
      key: "tableLine_inside_flat_line",
      icon: "icon-neibuhengxiankuang",
      isDisabled: false,
    },
    {
      value: "内部竖框线",
      key: "tableLine_inside_vertical_line",
      icon: "icon-neibushuxiankuang",
      isDisabled: false,
    },
    {
      value: "内部斜上框线",
      key: "tableLine_inside_inclined_bottom_line",
      icon: "icon-xieshangkuangxian",
      isDisabled: false,
    },
    {
      value: "双斜上框线",
      key: "tableLine_inside_inclined_bottom_line2",
      icon: "icon-shuangshangxiexian",
      isDisabled: false,
    },
    {
      value: "内部斜下框线",
      key: "tableLine_inside_inclined_top_line",
      icon: "icon-xiexiakuangxian",
      isDisabled: false,
    },
    {
      value: "双斜下框线",
      key: "tableLine_inside_inclined_top_line2",
      icon: "icon-shuangxiaxiexian",
      isDisabled: false,
    },
  ],
};
