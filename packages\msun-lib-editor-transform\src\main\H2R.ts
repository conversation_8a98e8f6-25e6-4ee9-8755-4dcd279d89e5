/* eslint-disable no-labels */
import newRawInitModel from "../utils/Util";

/**
 * html数据转原始数据
 */

export const H2R = {
  config: {},
  // 用于记录font,过滤掉内容中不存在的样式或者重复的样式
  newFontMap: {},
  newFontArray: [],
  newImageMap: new Map(),
  /**
   * 处理fontMap数据
   * @param font
   * @return font_id
   */
  handleFontMapData (font: any): string {
    const res_font = this.newFontArray.find((item: any) => this.fontIsEqual(item, font));
    if (res_font) {
      return res_font.id;
    } else {
      if (!font.id) {
        font.id = this.uuid("font");
      }
      this.newFontArray.push(font);
      // @ts-ignore
      this.newFontMap[font.id] = font;
      // @ts-ignore
      return font.id;
    }
  },
  handleImageMapData (src: string, pointId?:string): string {
    const imageId = this.newImageMap.get(src);
    if (imageId) {
      return imageId;
    } else {
      const newImageId = pointId ?? this.uuid("image");
      this.newImageMap.set(src, newImageId);
      return newImageId;
    }
  },
  clearInitData () {
    this.newFontMap = {};
    this.newFontArray = [];
    this.newImageMap.clear();
  },
  // 用于记录font,过滤掉内容中不存在的样式或者重复的样式
  initExtOptions (instance: any) {
    this.editor = instance.editor;
    this.TypeJudgment = instance.TypeJudgment;
    this.uuid = instance.utils.getUUID;
    this.fontIsEqual = instance.utils.fontIsEqual;
    this.fontSizeList = instance.builtInVariable.fontSizeList;
    this.fontTypeList = instance.builtInVariable.fontTypeList;
    this.colorToHex = instance.utils.colorToHex;
  },

  html2RawData (htmlStr:string, source?: string) {
    const rawData:any = {
      header: [],
      footer: [],
      content: []
    };
    this.clearInitData(); // 清空字体样式和图片 map
    try {
      // // 解析 XML 数据
      const parser = new DOMParser();
      const xmlDoc = parser.parseFromString(htmlStr, "text/html"); // 将解析器参数改为 text/html
      this.convertTags(xmlDoc.body, source);
      this.handleBodyContent(xmlDoc.body, rawData, source === "Excel" ? htmlStr : null);
      if (this.editor.config.copyStyleMode === 0) {
        rawData.fontMap = {};
      } else {
        rawData.fontMap = this.newFontMap;
      }
      if (rawData.content.length) {
        return rawData;
      }
    } catch (e) {
      console.error(e);
    }
  },

  // 其实就是跟 html2RawData 一样 但是这个是识别图片的 markdown 数据 所以单独写一个 单独维护 后期合并跟 html2RawData 合并成一个 或者就只用这个
  // 逻辑： 我应该能循环到所有的 DOM 节点 找到所有的节点跟 rawData 中数据的对应方式 就可以了
  // 考虑到样式的问题 应该是深度优先遍历 因为父级的样式会影响到子集的样式
  // 所有函数名都先随便起 最后再完善 修改名字
  transformDOM2RawData (node: Node) {
    const rawData:any = {
      header: [],
      footer: [],
      content: []
    };
    this.clearInitData(); // 清空字体样式和图片 map
    this.handleNode(node, rawData);
    rawData.fontMap = this.newFontMap;
    return rawData;
  },

  // 在 transfoDOM2RawData 里边用 处理实际节点
  handleNode (node: Node, rawData: any) {
    if (node.nodeName === "TABLE") {
      // const table = this.handleTableMarkdown(node);
      const arr: any[] = [];
      this.handleTable(node, "", arr);
      rawData.content.push(arr[0]);
    } else {
      const childNodes = node.childNodes;
      if (childNodes && childNodes.length) {
        for (let i = 0; i < childNodes.length; i++) {
          const child = childNodes[i];
          if (child.nodeName === "#comment" || child.nodeName === "HEAD") {
            // 这是注释节点 或者 head 就直接忽略了
            continue;
          }
          this.handleNode(child, rawData);
        }
      } else {
        if (node.nodeName === "#text") {
          // 应该是段落
          const text = node.textContent;
          if (text) {
            const arr = text.split("\n\n");
            for (const t of arr) {
              if (t) {
                const para = newRawInitModel("p");
                para.id = this.uuid("para");
                const textRaw = newRawInitModel("text");

                // 寻找标题
                const matchTitle = t.match(/^(#{1,6})\s+(.+)$/);

                const regex = /\*\*([^*]+?)\*\*/g; // 匹配非贪婪模式的内容
                const matchesDoubleStar = [];
                let match;

                while ((match = regex.exec(t)) !== null) {
                  // match[1] 包含捕获组（星号之间的内容）
                  matchesDoubleStar.push(match[1]);
                }

                if (matchTitle) {
                  const fontId = this.handleSpanStyle({
                    "font-weight": "bold"
                  });
                  textRaw.font_id = fontId;
                }
                textRaw.value = matchTitle ? matchTitle[2] : t;
                para.children.push(textRaw);
                rawData.content.push(para);
              }
            }
          }
        }
      }
    }
  },

  convertTags (element:any, source: string) {
    const children = element.children;
    for (let i = 0; i < children.length; i++) {
      const child = children[i];
      const tagName = child.tagName.toLowerCase();
      if (tagName === "table" && source === "Excel") {
        // 表格不处理 因为我要单独进行处理
        continue;
      }
      if (tagName !== "table" &&
            tagName !== "tr" &&
            tagName !== "td" &&
            tagName !== "tbody" &&
            tagName !== "span") {
        // 创建一个新的<p>标签
        let newElement;
        if (this.checkElementParaBehavior(child)) {
          newElement = document.createElement("p");
        } else {
          newElement = document.createElement("span");
        }
        if (this.editor.config.copyStyleMode === 2) {
          // 复制原始标签的基本样式
          newElement.setAttribute("class", child.getAttribute("class"));
          newElement.style.cssText = child.style.cssText;
          this.handleTagFontStyle(child, newElement);
        }
        if (tagName === "br") {
          newElement.innerText = "\n";
        }
        // 将原始子元素的转换结果插入新建的<p>标签中
        newElement.innerHTML = this.convertTags(child).innerHTML;
        // 将新建的<p>标签替换原始标签
        child.parentNode.replaceChild(newElement, child);
      } else {
        // 递归处理子元素，并将转换结果插入到当前元素中
        const convertedChild = this.convertTags(child);
        if (convertedChild !== child) {
          element.replaceChild(convertedChild, child);
        }
      }
    }
    // 返回处理后的元素
    return element;
  },
  // 处理标签类样式
  handleTagFontStyle (oriEle:any, newEle:any) {
    const tagName = oriEle.tagName;
    const setNewByOri = () => {
      if (oriEle.parentElement && oriEle.parentElement.textDecoration) {
        newEle.style.textDecoration += " " + oriEle.parentElement.textDecoration;
      }
      if (oriEle.parentElement && oriEle.parentElement.fontStyle) {
        newEle.style.fontStyle += " " + oriEle.parentElement.fontStyle;
      }
      if (oriEle.parentElement && oriEle.parentElement.fontWeight) {
        newEle.style.fontWeight = oriEle.parentElement.fontWeight;
      }
    };

    if (tagName === "S") {
      newEle.style.textDecoration += " line-through "; // 设置删除线样式
      oriEle.textDecoration = newEle.style.textDecoration; // 设置删除线样式
      setNewByOri();
    }
    if (tagName === "U") {
      newEle.style.textDecoration += " underline "; // 添加下划线样式
      oriEle.textDecoration = newEle.style.textDecoration; // 添加下划线样式
      setNewByOri();
    }
    if (tagName === "B") {
      newEle.style.fontWeight = "bold"; // 添加下划线样式
      oriEle.fontWeight = newEle.style.fontWeight;
      setNewByOri();
    }
    if (tagName === "I") {
      newEle.style.fontStyle += " italic "; // 添加下划线样式
      oriEle.fontStyle = newEle.style.fontStyle; // 添加下划线样式
      setNewByOri();
    }
  },
  // 处理嵌套标签并合并样式的函数
  getDefaultDisplay (tagName:any) {
    const tempElement = document.createElement(tagName);
    document.body.appendChild(tempElement);
    const display = window.getComputedStyle(tempElement).getPropertyValue("display");
    document.body.removeChild(tempElement);
    return display;
  },
  // 判断元素的display属性来确定其行为
  checkElementParaBehavior (element:any) {
    const display = this.getDefaultDisplay(element.tagName);
    return display === "block";
  },
  handleBodyContent (body:any, rawData:any, htmlStr?: any) {
    const content:any = [];
    rawData.content = content;
    const children = body.children;
    if (!children.length) {
      return false;
    }
    // 获取段落和表格的集合
    if (children[0].tagName === "P" || children[0].tagName === "TABLE") {
      for (let i = 0, len = children.length; i < len; i++) {
        const p = children[i];
        this.handlePara(p, content, htmlStr);
      }
    } else {
      this.handleParaChildren(body, content);
    }
  },
  handleParaChildren (para:any, content:any) {
    const children = para.childNodes;
    if (!children) {
      return;
    }
    let paraRaw = newRawInitModel("p");
    paraRaw.id = this.uuid("para");
    paraRaw.children = [];
    if (this.editor.config.copyStyleMode === 2) {
      // 设置段落样式
      this.handleParaStyle(para.style, paraRaw);
    }
    for (let i = 0, len = children.length; i < len; i++) {
      const item = children[i];
      if (item.nodeName === "#comment") {
        continue;
      }
      let text = item.nodeValue ?? item.innerText ?? "";
      const split = text.split("\n");
      if (split.length > 1) {
        for (let j = 0; j < split.length; j++) {
          text = split[j];
          const textRaw = newRawInitModel("text");
          textRaw.value = text;
          paraRaw.children.push(textRaw);
          content.push(paraRaw);
          paraRaw = newRawInitModel("p");
          paraRaw.id = this.uuid("para");
          paraRaw.children = [];
        }
      } else {
        this.handleSpan(text, item, paraRaw);
      }
    }
    content.push(paraRaw);
  },
  handleParaStyle (style:any, paraRaw:any) {
    const align = style["text-align"];
    paraRaw.align = align;
  },

  handleTable (tableDOM: any, htmlStr: any, content:any) {
    // 处理样式问题 ↓
    const parser = new DOMParser();
    const doc = parser.parseFromString(htmlStr, "text/html");

    // 2. 提取所有 CSS 样式规则
    const styleSheets = doc.querySelectorAll("style");
    const cssRules: any = [];
    styleSheets.forEach(style => {
      const cssContent = style.innerHTML.replace(/<!--|-->/g, ""); // 清理注释
      const styleElement = document.createElement("style");
      styleElement.textContent = cssContent;
      document.head.appendChild(styleElement); // 临时注入样式 如果不执行这一步 下边的 styleEment.sheet 就是 null

      // 提取规则
      const sheet = styleElement.sheet;
      if (sheet) {
        try {
          Array.from(sheet.cssRules).forEach(rule => {
            cssRules.push({
              // @ts-ignore
              selector: rule.selectorText,
              // @ts-ignore
              styles: rule.style
            });
          });
        } catch {}
      }
      document.head.removeChild(styleElement); // 清理临时样式
    });

    // 处理样式问题 ↑
    if (!tableDOM) return;

    const arr = []; // 二维数组 记录所有最小化的单元格的占位情况

    const table = newRawInitModel("table");
    table.cells = [];
    // TODO 暂时先不管 THEAD 还有 COLGROUP 只管 TBODY 先能够正常粘贴再进行后续的完善
    const { totalRowSpan, totalColSpan } = this.getTotalSpanByTableDOM(tableDOM);
    for (let i = 0; i < totalRowSpan; i++) {
      const tr = [];
      for (let j = 0; j < totalColSpan; j++) {
        tr.push(0);
      }
      arr.push(tr);
    }

    for (let i = 0, len = tableDOM.children.length; i < len; i++) {
      const item = tableDOM.children[i];
      if (item.nodeName === "TBODY") {
        for (let j = 0; j < totalRowSpan; j++) { // 行数是没有问题的 就是这么多行 是 row_size 的数量
          const row = item.children[j];
          if (row.nodeName === "TR") {
            for (let k = 0; k < row.children.length; k++) { // 跟 k 还真有关系
              const td = row.children[k]; // 每个 td 就是一个 cell
              const cell = this.getCellRawByTd(td, cssRules);

              // 每个 cell 我都要循环 arr 找到能正好放进该 cell 的位置
              // 只要列能放下也就是说 arr[r] 里边的 0 能装得下 td.colSpan 就能确定当前 cell 的位置 不用管
              const tdColSpan = td.colSpan;
              out: for (let r = 0; r < arr.length; r++) {
                const tds = arr[r];
                for (let d = 0; d < tds.length; d++) {
                  const tinyTd = tds[d];
                  if (tinyTd === 0) {
                    // 等于 0 就有能放进该 cell 的潜力 也就是说有空位
                    let total = 0; // 我要拿这个 total 跟 tdColSpan 去比
                    let n = d;
                    for (; n < tds.length; n++) {
                      if (tds[n] === 0) {
                        total++;
                      } else {
                        break;
                      }
                    }
                    if (total >= tdColSpan) {
                      // 这就说明能放的下 就要设置 cell.pos 并且修改 arr 填充上 1
                      cell.pos = [r, d];
                      for (let s = r; s < r + td.rowSpan; s++) {
                        for (let m = d; m < d + td.colSpan; m++) {
                          arr[s][m] = 1;
                        }
                      }
                      break out;
                    }
                  }
                }
              }

              this.handleCellNotAllowDrawLine(cell, table);
              table.cells.push(cell);
            }
          }
        }
      }
    }
    table.row_size = new Array(totalRowSpan).fill(10);
    table.min_row_size = new Array(totalRowSpan).fill(10);
    table.col_size = new Array(totalColSpan).fill(10);
    content.push(table);
  },
  getTotalSpanByTableDOM (tableDOM: any) {
    let totalRowSpan = 0;
    let totalColSpan = 0;
    for (let i = 0, len = tableDOM.children.length; i < len; i++) {
      const tbody = tableDOM.children[i];
      if (tbody.nodeName === "TBODY") {
        totalRowSpan = tbody.children.length;
        const tr = tbody.children[0];
        for (let k = 0; k < tr.children.length; k++) {
          const td = tr.children[k];
          totalColSpan += td.colSpan;
        }
        break;
      }
    }
    return {
      totalRowSpan,
      totalColSpan
    };
  },
  handleCellNotAllowDrawLine (cell: any, table: any) {
    for (let c = 0; c < cell.colspan; c++) {
      for (let r = 1; r < cell.rowspan; r++) {
        table.notAllowDrawLine.row.push([r + cell.pos[0], c + cell.pos[1]]);
      }
    }
    for (let r = 0; r < cell.rowspan; r++) {
      for (let c = 1; c < cell.colspan; c++) {
        table.notAllowDrawLine.col.push([c + cell.pos[1], r + cell.pos[0]]);
      }
    }
  },
  getCellRawByTd (td: any, cssRules: any) {
    const cell = newRawInitModel("cell");
    cell.id = this.uuid("cell");

    cell.colspan = td.colSpan;
    cell.rowspan = td.rowSpan;
    cell.children = this.getParasByTd(td, cssRules);
    return cell;
  },
  getParasByTd (td: any, cssRules: any) {
    const tdStyle = cssRules.find((it: any) => it.selector === "td")?.styles; // td 上如果有 class 就用 class 的样式 如果没有 class 就用 td 的样式
    const tdClassStyle = cssRules.find((it: any) => it.selector === "." + td.classList.value)?.styles; // td 上如果有 class 就用 class 的样式 如果没有 class 就用 td 的样式
    const tdVerticalAlign = tdClassStyle?.verticalAlign || tdStyle?.verticalAlign;
    const tdTextAlign = tdClassStyle?.textAlign || tdStyle?.textAlign;
    let verticalAlign = "top";
    let align = "left";
    if (tdStyle) {
      if (tdVerticalAlign === "middle") {
        verticalAlign = "center";
      } else if (tdVerticalAlign === "bottom") {
        verticalAlign = "bottom";
      }
      if (tdTextAlign === "center") {
        align = "center";
      } else if (tdTextAlign === "right") {
        align = "right";
      }
    }
    const paras: any = [];
    // td 里边一定要有个空行的 所以上来就要先创建个空段
    let p = newRawInitModel("p");
    p.id = this.uuid("para");
    const text = newRawInitModel("text");
    text.value = "";
    text.font_id = this.handleSpanStyle({
      height: 16,
      family: "宋体",
      bold: false,
      italic: false,
      underline: false,
      dblUnderLine: false,
      strikethrough: false,
      script: 3,
      color: "#000",
      bgColor: null,
      highLight: null,
      characterSpacing: 0
    });
    p.children.push(text);
    const that = this;
    function handleMySpan (span: any) {
      const style = cssRules.find((it: any) => it.selector === "." + span.classList.value)?.styles;
      for (const it of span.childNodes) {
        if (it.nodeName === "#text") {
          const text = newRawInitModel("text");
          text.value = it.textContent;
          text.font_id = that.handleSpanStyle(style || {
            height: 16,
            family: "宋体",
            bold: false,
            italic: false,
            underline: false,
            dblUnderLine: false,
            strikethrough: false,
            script: 3,
            color: style?.color || "#000",
            bgColor: null,
            highLight: null,
            characterSpacing: 0
          });
          p.children.push(text);
        } else if (it.nodeName === "BR") {
          // 遇到换行就先将原来的段落塞进去 再创建新的段落
          paras.push(p);
          p = newRawInitModel("p");
          p.id = that.uuid("para");
          const text = newRawInitModel("text");
          text.value = "";
          text.font_id = that.handleSpanStyle({
            height: 16,
            family: "宋体",
            bold: false,
            italic: false,
            underline: false,
            dblUnderLine: false,
            strikethrough: false,
            script: 3,
            color: "#000",
            bgColor: null,
            highLight: null,
            characterSpacing: 0
          });
          p.children.push(text);
        } else if (it.nodeName === "FONT") {
          handleMySpan(it);
        } else if (it.nodeName === "SPAN") {
          handleMySpan(it);
        }
      }
    }

    for (const item of td.childNodes) {
      if (item.nodeName === "#text") {
        const style = cssRules.find((it: any) => it.selector === "." + td.classList.value)?.styles;
        // td 里边的纯文本 是没有样式的 所以就用默认的样式就可以
        const text = newRawInitModel("text");
        text.value = item.textContent;
        text.font_id = this.handleSpanStyle(style || {
          height: 16,
          family: "宋体",
          bold: false,
          italic: false,
          underline: false,
          dblUnderLine: false,
          strikethrough: false,
          script: 3,
          color: style?.color || "#000",
          bgColor: null,
          highLight: null,
          characterSpacing: 0
        });
        p.children.push(text);
      } else if (item.nodeName === "BR") {
        // 遇到换行就先将原来的段落塞进去 再创建新的段落
        paras.push(p);
        p = newRawInitModel("p");
        p.id = this.uuid("para");
        const text = newRawInitModel("text");
        text.value = "";
        text.font_id = this.handleSpanStyle({
          height: 16,
          family: "宋体",
          bold: false,
          italic: false,
          underline: false,
          dblUnderLine: false,
          strikethrough: false,
          script: 3,
          color: "#000",
          bgColor: null,
          highLight: null,
          characterSpacing: 0
        });
        p.children.push(text);
      } else if (item.nodeName === "SPAN") {
        handleMySpan(item);
      } else if (item.nodeName === "P") {
        function handleP (para: any) {
          for (const span of para.childNodes) {
            if (span.nodeName === "SPAN") {
              handleMySpan(span);
            }
          }
        }
        handleP(item);
      }
    }
    paras.push(p);
    paras.forEach((p: any) => {
      p.vertical_align = verticalAlign;
      p.align = align;
    });
    return paras;
  },
  handlePara (para:any, content:any, htmlStr?: any) {
    if (para.tagName === "TABLE") {
      if (htmlStr) {
        this.handleTable(para, htmlStr, content);
      } else {
        const tableRaw = newRawInitModel("table");
        tableRaw.id = this.uuid("table");
        tableRaw.children = [];
        this.handleTableBody(para, tableRaw);
        content.push(tableRaw);
      }
    } else if (para.tagName === "P") {
      this.handleParaChildren(para, content);
    } else {
      console.warn(para);
    }
  },
  handleTableBody (table:any, tableRaw:any) {
    const bodies = table.children;
    let maxColLen = 0;
    // table分三部分， header、footer、和body
    for (let i = 0, len = bodies.length; i < len; i++) {
      const body = bodies[i];
      for (let j = 0, len2 = body.children.length; j < len2; j++) {
        const tr = body.children[j];
        tableRaw.row_size.push(20);
        tableRaw.min_row_size.push(20);
        maxColLen = Math.max(maxColLen, tr.children.length);
        for (let k = 0, len3 = tr.children.length; k < len3; k++) {
          const td = tr.children[k];
          this.handleCell([j, k], td, tableRaw);
        }
      }
    }
    tableRaw.col_size = new Array(maxColLen).fill(800 / maxColLen);
  },
  handleCell (pos:number[], cell:any, tableRaw:any) {
    const cellRaw = newRawInitModel("cell");
    cellRaw.id = this.uuid("cell");
    cellRaw.pos = pos;
    const children = cell.children;
    for (let i = 0, len = children.length; i < len; i++) {
      this.handlePara(children[i], cellRaw.children);
    }
    if (!cellRaw.children.length) {
      const paraRaw = newRawInitModel("p");
      paraRaw.id = this.uuid("para");
      cellRaw.children.push(paraRaw);
    }
    tableRaw.cells.push(cellRaw);
  },
  getEffectiveStyle (element:any, propertyName:any, isInit:boolean) {
    if (isInit) {
      const style = this.getStyleAttributes(element);
      if (style[propertyName] && style[propertyName] !== "inherit") {
        return style[propertyName];
      }
    }
    if (element.style && element.style[propertyName] && element.style[propertyName] !== "inherit") {
      return element.style[propertyName];
    }
    const computedStyle = window.getComputedStyle(element);
    const propertyValue = computedStyle.getPropertyValue(propertyName);
    if (propertyValue === "inherit" || !propertyValue || propertyValue === "none") {
      // 如果属性值为 'inherit'，则递归获取父元素的样式
      const parentElement = element.parentElement;
      if (parentElement) {
        return this.getEffectiveStyle(parentElement, propertyName, false);
      }
    }
    return propertyValue;
  },
  handleSpan (text:string, node:any, para:any) {
    if (text) {
      if (node.childNodes && node.childNodes.length) {
        for (let i = 0, len = node.childNodes.length; i < len; i++) {
          const nn = node.childNodes[i];
          const text = nn.nodeValue ?? nn.innerText ?? "";
          this.handleSpan(text, nn, para);
        }
      } else {
        const textRaw = newRawInitModel("text");
        para.children.push(textRaw);
        textRaw.value = text;
        let element:any = node;
        if (!(node instanceof HTMLElement)) {
          element = node.parentNode;
        }
        if (this.editor.config.copyStyleMode === 2) {
          const computedStyle:any = {};
          const allProp:any = ["font-family", "font-style", "font-weight", "color",
            "background-color", "background", "text-decoration", "border-bottom", "vertical-align", "font-size"];
          for (let i = 0; i < allProp.length; i++) {
            computedStyle[allProp[i]] = this.getEffectiveStyle(element, allProp[i], true);
          }
          // console.log(computedStyle);
          const fontId = this.handleSpanStyle(computedStyle);
          textRaw.font_id = fontId;
        }
      }
    }
  },
  // 定义一个函数，用于获取节点的样式
  getStyleAttributes (node:any) {
    if (!node.innerHTML || !node.innerHTML.startsWith("<")) {
      return {};
    }
    const str = node.innerHTML.replace(/\n/g, "");
    // 定义正则表达式来匹配样式字符串
    const styleRegex = /style="([^"]*)"/;
    // 匹配样式字符串
    const styleMatch = str.match(styleRegex);
    // 初始化一个空对象来存储样式属性
    const styleObj:any = {};

    // 如果匹配到了样式字符串
    if (styleMatch && styleMatch.length > 1) {
      // 将样式字符串按分号分隔成数组
      const styleArray = styleMatch[1].split(";");

      // 遍历样式数组
      styleArray.forEach(function (style:any) {
        // 将每个样式属性按冒号分隔成键值对
        const pair = style.split(":");
        if (pair.length === 2) {
          // 去除属性名和属性值两端的空格，并存储到样式对象中
          if (!styleObj[pair[0].trim()]) {
            styleObj[pair[0].trim()] = pair[1].trim();
          }
        }
      });
    }

    return styleObj;
  },
  findClosestNumber (arr: number[], n: number) {
    if (arr.length === 0) throw new Error("数组不能为空");

    let closest = arr[0];
    let minDiff = Math.abs(closest - n);

    for (let i = 1; i < arr.length; i++) {
      const current = arr[i];
      const currentDiff = Math.abs(current - n);

      if (currentDiff <= minDiff) {
        closest = current;
        minDiff = currentDiff;
      }
    }

    return closest;
  },
  handleSpanStyle (style:any) {
    const fontStyle = newRawInitModel("style");
    if (style["font-family"]) {
      const family = style["font-family"];
      const newSet = new Set(this.fontTypeList || []);
      if (newSet.has(family)) {
        fontStyle.family = family;
      } else {
        fontStyle.family = this.editor.config.default_font_style?.family || "仿宋";
      }
    }
    if (style["font-style"]?.indexOf("italic") > -1) {
      fontStyle.italic = true;
    }
    if (style["font-weight"]?.indexOf("bold") > -1) {
      fontStyle.bold = true;
    }
    if (style.color) {
      fontStyle.color = this.colorToHex(style.color);
    }
    if (style["background-color"]) {
      fontStyle.bgColor = this.colorToHex(style["background-color"]);
    }
    if (style.background) {
      fontStyle.bgColor = this.colorToHex(style.background);
    }
    if (style["text-decoration"]?.indexOf("underline") > -1) {
      fontStyle.underline = true;
    }
    if (style["text-decoration"]?.indexOf("line-through") > -1) {
      fontStyle.strikethrough = true;
    }
    if (style["border-bottom"]?.indexOf("double") > -1) {
      fontStyle.dblUnderLine = true;
    }
    if (style["vertical-align"]?.indexOf("super") > -1) {
      fontStyle.script = 1;
    }
    if (style["vertical-align"]?.indexOf("sub") > -1) {
      fontStyle.script = 2;
    }
    const fontSizeStr = style["font-size"];
    const heightSet = new Set(this.fontSizeList || []);
    if (fontSizeStr) {
      const fontSize = parseFloat(fontSizeStr).toString();
      if (fontSizeStr.endsWith("pt")) {
        const find = this.fontSizeList.find((item:any) => item.option === fontSize);
        if (find) {
          if (heightSet.has(find.value)) {
            fontStyle.height = find.value;
          } else {
            fontStyle.height = this.editor.config.default_font_style?.heighit || 16;
          }
        }
      } else if (fontSizeStr.endsWith("rem")) {
        const remToPxRatio = this.getRootEleFontSize();
        // 计算 rem 字号值对应的像素值
        const height = Number(fontSize) * remToPxRatio;
        fontStyle.height = this.findClosestNumber(this.fontSizeList, height);
      } else {
        fontStyle.height = this.findClosestNumber(this.fontSizeList, Number(fontSize));
      }
    }
    // console.log(style["font-family"],
    //   style["font-style"],
    //   style["font-weight"],
    //   style.color,
    //   style["background-color"],
    //   style.background,
    //   style["text-decoration"],
    //   style["border-bottom"],
    //   style["vertical-align"], style["vertical-align"], fontSizeStr);
    const fontId = this.handleFontMapData(fontStyle);
    return fontId;
  },
  getRootEleFontSize () {
    // 获取根元素
    const rootElement = document.documentElement;

    // 获取根元素的样式信息
    const rootStyles = window.getComputedStyle(rootElement);

    // 提取根元素的字号大小
    const rootFontSize = rootStyles.fontSize;

    return parseFloat(rootFontSize);
  }
};
