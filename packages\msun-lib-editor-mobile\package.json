{"name": "msun-lib-editor-mobile", "version": "10.12.38", "type": "module", "files": ["dist"], "module": "./dist/msunEditorMobile.esm.js", "exports": {".": "./src/App.vue"}, "publishConfig": {"exports": {".": "./dist/msunEditorMobile.esm.js"}}, "scripts": {"dev": "vite", "serve": "vite", "build": "vite build", "lib": "vite build"}, "projectType": "library", "dependencies": {"core-js": "3.31.0", "moment": "^2.30.1", "msun-editor": "workspace:*", "vant": "2.5.9"}, "devDependencies": {"@babel/eslint-parser": "^7.22.9", "@originjs/vite-plugin-commonjs": "^1.0.3", "@vitejs/plugin-vue2": "^2.2.0", "@vue/cli-plugin-babel": "^5.0.8", "@vue/eslint-config-prettier": "^7.1.0", "babel-eslint": "^10.1.0", "babel-plugin-import": "^1.13.6", "eslint": "^7.28.0", "eslint-config-prettier": "^8.3.0", "eslint-config-standard": "^16.0.3", "eslint-plugin-import": "^2.23.4", "eslint-plugin-node": "^11.1.0", "eslint-plugin-prettier": "^3.4.0", "eslint-plugin-promise": "^5.1.0", "eslint-plugin-vue": "^9.15.1", "less": "^4.1.3", "less-loader": "^11.1.3", "prettier": "^2.8.8", "rollup-plugin-visualizer": "^5.9.2", "vite": "^4.4.7", "vite-plugin-antdv-fix": "^1.0.3", "vite-plugin-css-injected-by-js": "^3.2.0", "vue": "2.7.14", "vue-template-compiler": "2.7.14"}, "peerDependencies": {"vue": "2.7.14"}, "realVersion": "10.12.38"}