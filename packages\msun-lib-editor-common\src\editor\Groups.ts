import Cell from "./Cell";
import Character from "./Character";
import Editor from "./Editor";
import EditorHelper from "./EditorHelper";
import Paragraph from "./Paragraph";
import Row from "./Row";
import Table from "./Table";
import { getRawDataByConfig, isParagraph, isTable, root_node, uuid } from "./Utils";
import XField from "./XField";
import { Direction } from "./Definition";
import { ViewMode } from "./Constant";

export default class Group {
  id: string;

  date: string | number;

  name: string = "";

  lock: boolean = false;

  content_para_id: string[] = []; // 分组内含有的数组id

  header_info: any; // 对象数组 ,key为文本域编号，value为需要替换的值

  replace: boolean = false;

  page_break: boolean = false; // 该属性的意思为从当前分组分页，该分组下方的内容会另起一页

  new_page: boolean = false; // 该属性为true的分组将在新的一页开始

  is_form: boolean = false; // 当前分组是否为表单模式

  meta: any = {};

  cell: Cell;

  static insert (editor: Editor, direction: Direction = Direction.down, is_form:boolean = false, date?: string, id?: string, referenceGroup?: Group) {
    if (id) {
      const repeatGroup = editor.selection.getGroupByGroupId(id);
      if (repeatGroup) {
        editor.event.emit("message", { type: "warning", msg: "存在相同ID分组！" });
        return;
      }
    }
    editor.selection.clearSelectedInfo(); // 插入分组要清空选区 否则再插入文本域设置对齐方式会设置原来的选区对齐
    // 光标所在位置的分组
    const focus_group = referenceGroup ?? editor.selection.getFocusGroup();
    // 新new出来一个分组
    const new_group = new Group(id ?? uuid("group"), editor.current_cell, date);
    new_group.is_form = is_form;
    // 光标处有分组
    if (focus_group) {
      // 获取光标所在分组的index
      let focus_group_index = focus_group.cell.groups!.findIndex((item) => {
        return item.id === focus_group.id;
      });
      if (direction === Direction.up) {
        // 第一个分组的话 就在放到最前面
        focus_group_index = focus_group_index === 0 ? 0 : focus_group_index;
        focus_group.cell.groups!.splice(focus_group_index, 0, new_group);
        editor.insertGroupHelper(focus_group, new_group, direction);
      }
      if (direction === Direction.down) { // 下面的话就正常加1
        focus_group.cell.groups!.splice(focus_group_index + 1, 0, new_group);
        editor.insertGroupHelper(focus_group, new_group, direction);
      }
    } else {
      // 光标处没有分组
      const focus_para = editor.selection.getFocusParagraph();
      const sectionParameter = focus_para.cell.parent ? focus_para.cell.parent.para_index : focus_para.para_index;
      // 光标位置 上一个分组 在 所有分组中的下标位置
      const previous_group_index = editor.current_cell.groupIndexSection(sectionParameter);
      editor.current_cell.groups.splice(previous_group_index ?? 0, 0, new_group);
      editor.insertGroupHelper(focus_group, new_group, direction);
    }
    return new_group;
  }

  static insertHelper (editor: Editor, focus_group: Group | null, new_group: Group, direction: Direction = Direction.down) {
    const focus_para_path = editor.selection.para_focus;
    // 插入功能  需要new出一个新的段落插入对的位置
    const new_para = new Paragraph(uuid("para"), editor.current_cell, new_group.id); // 段落中需要的各个样式信息均为最原始的状态
    // 空段中的默认回车符
    const font = editor.fontMap.add(editor.config.default_font_style);
    const new_character = new Character(font, "\n");
    new_group.content_para_id.push(new_para.id);
    // 插入位置计算
    let splice_start: number = focus_para_path[0];
    if (focus_group) {
      splice_start = focus_group.end_para_index!; // 下方插入的时候用
    }
    new_para.characters.push(new_character);
    if (direction === Direction.up) {
      splice_start = focus_group!.start_para_index!;
      editor.current_cell.paragraph.splice(splice_start, 0, new_para);
      editor.current_cell.updateParaIndex();
      if (!splice_start) {
        new_para.updateChildren(0);
      } else {
        // 段落更新需要的参数 代表模型数据中目标段的前一段最后一行cell_index
        const pre_para_tab_cellindex = new_para.previousParagraph ? isTable(new_para.previousParagraph) ? new_para.previousParagraph.cell_index : new_para.previousParagraph.lastRow.cell_index : 0;
        // 更新位置
        // 更新参数
        const parameter = pre_para_tab_cellindex + 1;
        new_para.updateChildren(parameter);
      }
    } else {
      if (focus_para_path[0] === 0 && editor.root_cell.children[0].children.length === 0 && !focus_group) {
        editor.current_cell.paragraph.splice(0, 1, new_para);
        editor.current_cell.children.shift();
        editor.current_cell.updateParaIndex();
        new_para.updateChildren(0);
      } else {
        editor.current_cell.paragraph.splice(splice_start + 1, 0, new_para);
        editor.current_cell.updateParaIndex();
        // 段落更新需要的参数 代表模型数据中目标段的前一段最后一行cell_index
        const pre_para_tab_cellindex = new_para.previousParagraph ? isTable(new_para.previousParagraph) ? new_para.previousParagraph.cell_index : new_para.previousParagraph.lastRow.cell_index : 0;
        // 更新位置
        // 更新参数
        const parameter = pre_para_tab_cellindex + 1;
        new_para.updateChildren(parameter);
      }
    }

    editor.selection.setCursorPosition(editor.paraPath2ModelPath([new_para.para_index, 0]));
    editor.update(...editor.getUpdateParamsByContainer(editor.current_cell));
    editor.scroll_by_focus();
    editor.render();
  }

  static insertWithDateSort (editor: Editor, date: string, id?: string) {
    if (id) {
      const repeatGroup = editor.selection.getGroupByGroupId(id);
      if (repeatGroup) {
        editor.event.emit("message", { type: "warning", msg: "存在相同ID分组！" });
        return;
      }
    }
    editor.selection.clearSelectedInfo(); // 清空选区
    if (!editor.current_cell.groups.length) {
      editor.insertGroup(Direction.down, date, id ?? uuid("group"));
      return true;
    }
    // 新new出来一个分组
    const new_group = new Group(id ?? uuid("group"), editor.current_cell, date);
    // 符合业务逻辑 在添加完设置时间的文本域后自动排序
    editor.current_cell.groups.push(new_group);
    // 插入功能  需要new出一个新的段落插入对的位置
    const new_para = new Paragraph(uuid("para"), editor.current_cell, new_group.id); // 段落中需要的各个样式信息均为最原始的状态
    // 空段中的默认回车符
    const font = editor.fontMap.add(editor.config.default_font_style);
    const new_character = new Character(font, "\n");
    new_para.characters.push(new_character);
    // 新分组内容
    new_group.content_para_id.push(new_para.id);
    // 新段落放到cell中
    editor.current_cell.paragraph.push(new_para);
    // 更新段落的index
    editor.current_cell.updateParaIndex();
    // 对应行生成并放到相应位置
    new_para.updateChildren(editor.current_cell.children[editor.current_cell.children.length - 1].cell_index + 1);
    // 设置光标位置
    editor.selection.setCursorPosition(editor.paraPath2ModelPath([new_para.para_index, 0]));
    editor.update(...editor.getUpdateParamsByContainer(editor.current_cell));
    editor.scroll_by_focus();
    editor.render();
    return true;
  }

  static delete (editor: Editor, target_group?: Group) {
    // 光标所在分组
    const focus_group = target_group || editor.selection.getFocusGroup();
    // 分组如果锁定 则不能删除
    if (!focus_group || focus_group.lock) return false;
    const id = focus_group.id;
    const group_index = focus_group.cell.groups.findIndex((item) => item.id === focus_group.id);
    let caertPara: Paragraph | Table | null = null;
    // 当前分组不是最后一个分组
    if (focus_group.cell.groups.length !== 0 && group_index < focus_group.cell.groups.length - 1) {
      caertPara = focus_group.cell.paragraph[focus_group!.end_para_index! + 1];
    } else if (focus_group.cell.groups.length !== 0 && group_index === focus_group.cell.groups.length - 1) {
      caertPara = focus_group.cell.paragraph[focus_group!.start_para_index! - 1];
    }
    // 删除对应的分组
    for (let i = 0; i < editor.current_cell.groups.length; i++) {
      const group = editor.current_cell.groups![i];
      if (focus_group.id === group.id) {
        group.remove();
        break;
      }
    }

    if (focus_group.cell.groups.length === 0) {
      // position_date = position_date - 1;
      editor.selection.setCursorByRootCell("start"); // 定位到开头的时候有问题
    } else {
      editor.selection.setCursorPosition(editor.locationCaret(caertPara!));
    }

    delete editor.document_meta?.cusCommentsIDSet?.[id];
    delete editor.document_meta?.commentsIDSet?.[id];
    
    editor.update(...editor.getUpdateParamsByContainer(editor.current_cell));
    editor.scroll_by_focus();
    editor.render();
    return true;
  }

  static setFormMode (editor:Editor, isForm:boolean, group:Group|null = null) {
    if (!group) {
      group = editor.selection.getFocusGroup();
    }
    if (!group) return;
    // 只要使用该接口就先将模式编辑器模式重置为普通模式，否则取消最后一个分组表单时可能无法回归成普通模式
    if (editor.view_mode === ViewMode.FORM) {
      editor.view_mode = ViewMode.NORMAL;
    }
    group.is_form = isForm;
    editor.updateCaret();
  }

  static getRawData (editor: Editor, group: Group) {
    const newRootCell = new Cell(
      editor,
      root_node.pos,
      root_node.colspan,
      root_node.rowspan,
      null,
      "groups"
    );
    newRootCell.paragraph = group.paragraph;
    // const rawData = rawDataTrans.modelDataToRawData(
    //   editor.header_cell,
    //   newRootCell,
    //   editor.footer_cell,
    //   editor.packSaveDocumentInfo()
    // );
    const rawData = editor.event.emit("modelData2RawData",
      editor.header_cell,
      newRootCell,
      editor.footer_cell,
      editor.packSaveDocumentInfo()
    );
    return getRawDataByConfig(editor, rawData);
  }

  static setPaging (editor: Editor, target_group?: Group) {
    const focus_group = target_group || editor.selection.getFocusGroup();
    if (!focus_group) {
      return false;
    }
    // if (focus_group.pre_group) {
    //   focus_group.pre_group.page_break = !focus_group.pre_group.page_break;
    // }
    focus_group.new_page = !focus_group.new_page;
    editor.update();
    editor.scroll_by_focus();
    editor.render();
  }

  static lock (editor: Editor, locked?: boolean) {
    const group = editor.selection.getFocusGroup();
    if (group) {
      const isLock = locked ?? !group.lock;
      group.setGroupLock(isLock);
    }
    editor.update(...editor.getUpdateParamsByContainer(editor.current_cell));
    editor.render();
  }

  static sort (editor: Editor, sortParameter: string = "date") {
    if (!editor.root_cell.groups.length) return false;
    const current_group = editor.selection.getFocusGroup();
    editor.root_cell.groups[0].sortGroup(sortParameter);
    if (current_group) {
      const group_start_para_index = current_group.start_para_index!;
      if (isTable(editor.current_cell.paragraph[group_start_para_index])) {
        editor.selection.setCursorPosition(editor.paraPath2ModelPath([group_start_para_index, 0, 0, 0]));
      } else {
        editor.selection.setCursorPosition(editor.paraPath2ModelPath([group_start_para_index, 0]));
      }
    }
    editor.update();
    editor.scroll_by_focus();
    editor.render();
    return true;
  }

  static clear (editor: Editor, groups: Group[]) {
    for (let i = 0; i < groups.length; i++) {
      const group = groups[i];
      if (group.lock) continue;
      group.clear();
    }
    const para_path = editor.paraPath2ModelPath([groups[0]!.start_para_index!, 0]);
    editor.selection.setCursorPosition(para_path);
    editor.update();
    editor.scroll_by_focus();
    editor.render();
    return true;
  }

  static groupFormModeResetEditorViewMode (editor:Editor, useGroupFormMode:boolean) {
    if (useGroupFormMode) {
      if (editor.view_mode === ViewMode.FORM) {
        editor.view_mode = ViewMode.NORMAL;
      }
    }
  }

  static navigateToGroupByID (editor: Editor, id: string) {
    const useGroupFormMode = editor.config.useGroupFormMode;
    editor.config.useGroupFormMode = false;
    this.groupFormModeResetEditorViewMode(editor, useGroupFormMode);
    EditorHelper.scrollTo(editor, "bottom"); // 先滚动页面最底部 然后下边逻辑再往上滚动 是为了更多的暴露分组在视口内
    const groups = editor.root_cell.groups;
    for (let i = 0; i < groups.length; i++) {
      const group = groups[i];
      if (id === group.id) {
        const paragraph = editor.root_cell.paragraph.find((para) => group.content_para_id[0] === para.id);
        if (!paragraph) {
          // 有人操作json直接删除了分组对应的p标签会报这个提示
          editor.event.emit("message", { type: "warning", msg: "未检索到分组对应的段落信息！" });
          return false;
        }
        if (isTable(paragraph)) {
          editor.selection.setCursorPosition([paragraph.cell_index, 0, 0, 0]);
        } else {
          editor.selection.setCursorPosition([(paragraph as Paragraph).children[0].cell_index, 0]);
        }
        editor.updateCaret();
        editor.scroll_by_focus();
        editor.render();
        editor.config.useGroupFormMode = useGroupFormMode;
        if (useGroupFormMode) {
          editor.updateCaret();
        }
        return true;
      }
    }
    editor.config.useGroupFormMode = useGroupFormMode;
    if (useGroupFormMode) {
      editor.updateCaret();
    }
    editor.event.emit("message", { type: "warning", msg: "未检索到对应的分组！" });
  }

  static modifyDate (editor: Editor, date: string) {
    const focus_group = editor.selection.getFocusGroup();
    if (focus_group && !focus_group.lock) {
      focus_group.date = date;
    }
  }

  static modifyHeaderInfo (editor: Editor, obj: any) {
    const groups = editor.getAllGroup();
    for (const group of groups) {
      for (let  name in obj) {
        group.header_info[name] = obj[name]
      }
    }
    editor.refreshDocument();
    return true;
  }

  /**
   * 恢复数据
   * @param editor Editor
   * @param needRecoverGroupID 需要恢复数据的分组 id
   * @param needRecoverGroupRawContent 需要恢复数据的分组 rawData
   * @returns
   */
  static recoverData (editor: Editor, needRecoverGroupID: string, needRecoverGroupRawContent: any) {
    const need_recover_group = editor.selection.getGroupByGroupId(needRecoverGroupID);
    if (!need_recover_group) return;
    const modelCell = editor.getCellByRawNodeList(needRecoverGroupRawContent);
    if (!modelCell) return;

    // 找到需要恢复分组内的所有文本域 id 不需要管表格内的文本域
    const need_recover_group_paragraph_table_set: (Paragraph | Table)[] = need_recover_group.paragraph;
    const need_recover_group_all_fields_id: string[] = [];
    for (const paragraphOrTable of need_recover_group_paragraph_table_set) {
      if (isParagraph(paragraphOrTable)) {
        need_recover_group_all_fields_id.push(...paragraphOrTable.fieldsId);
      }
    }
    for (const id of need_recover_group_all_fields_id) {
      editor.root_cell.fields.splice(editor.root_cell.fields.findIndex((field) => field.id === id), 1);
    }
    const record_fields = modelCell.fields;
    record_fields.forEach(f => { f.cell = editor.root_cell; });
    editor.root_cell.fields.push(...record_fields);

    const record_paragraph_table_set = modelCell.paragraph;
    need_recover_group.content_para_id = [];
    record_paragraph_table_set.forEach(paragraphOrTable => {
      paragraphOrTable.group_id = need_recover_group.id;
      need_recover_group.content_para_id.push(paragraphOrTable.id);
      if (isParagraph(paragraphOrTable)) {
        paragraphOrTable.cell = editor.root_cell;
      } else {
        paragraphOrTable.parent = editor.root_cell;
      }
    });
    const first_paragraph_or_table = need_recover_group_paragraph_table_set[0];
    const index = editor.root_cell.paragraph.findIndex(paraOrTable => paraOrTable.id === first_paragraph_or_table.id);
    editor.root_cell.paragraph.splice(index, need_recover_group_paragraph_table_set.length, ...record_paragraph_table_set);

    const record_rows = modelCell.children;
    const need_recover_group_row_table_set: (Row | Table)[] = [];
    for (const paragraphOrTable of need_recover_group_paragraph_table_set) {
      if (isParagraph(paragraphOrTable)) {
        need_recover_group_row_table_set.push(...paragraphOrTable.children);
      } else {
        need_recover_group_row_table_set.push(paragraphOrTable);
      }
    }
    const splice_rows_length = need_recover_group_row_table_set.length;
    editor.root_cell.children.splice(isParagraph(first_paragraph_or_table) ? first_paragraph_or_table?.children[0].cell_index : first_paragraph_or_table?.cell_index, splice_rows_length, ...record_rows);

    editor.root_cell.children.forEach((r, i) => { r.cell_index = i; });
    editor.root_cell.updateParaIndex();
  }

  constructor (id: string, cell: Cell, date?: string) {
    this.id = id;

    this.cell = cell;

    this.date = date ?? (new Date().valueOf() + "");
  }

  get paragraph () {
    // return this.cell.paragraph.filter(paragraph_table => paragraph_table.group_id === this.id);
    const id = this.id;
    const paragraph = this.cell.paragraph;
    const filteredParagraphs = [];
    for (let i = 0; i < paragraph.length; i++) {
      const nextPara = paragraph[i+1]
      if (paragraph[i].group_id === id) {
        filteredParagraphs.push(paragraph[i]);
        // 因为分组中的段落一定是连续的，此处做一个简单的优化
        if(!nextPara || nextPara.group_id !== id){
          break
        }
      }
    }
    return filteredParagraphs;
  }

  get tables (): Table[] {
    const tables: Table[] = [];
    const id = this.id;
    const paragraph_table_arr = this.cell.paragraph;
    for (let i = 0; i < paragraph_table_arr.length; i++) {
      const nextContent = paragraph_table_arr[i+1]
      const content = paragraph_table_arr[i];
      if (content.group_id === id && isTable(content)) {
        tables.push(content);
        // 因为分组中的段落一定是连续的，此处做一个简单的优化
        if(!nextContent || nextContent.group_id !== id){
          break
        }
      }
    }
    return tables;
  }

  get pureParas () {
    return this.cell.paragraph.filter(paragraph_table => paragraph_table.group_id === this.id && (isParagraph(paragraph_table)));
  }

  get groupStr (): string {
    let groupStr = "";
    for (const para of this.paragraph) {
      groupStr += para.getStr(true);
    }
    return groupStr;
  }

  /**
   * 获取分组开始段落index
   */
  public get start_para_index () {
    let para: number | null = null;
    for (let i = 0; i < this.cell.paragraph.length; i++) {
      const element = this.cell.paragraph[i];
      if (this.content_para_id.length && element.id === this.content_para_id[0]) {
        para = element.para_index;
      }
    }
    return para;
  }

  /**
   * 获取分组结束段落index
   */
  public get end_para_index () {
    let para: number | null = null;
    for (let i = 0; i < this.cell.paragraph.length; i++) {
      const element = this.cell.paragraph[i];
      if (this.content_para_id.length && element.id === this.content_para_id[this.content_para_id.length - 1]) {
        para = element.para_index;
      }
    }
    return para;
  }

  public get pre_group (): Group | undefined {
    let pre_group = null;
    for (let i = 0; i < this.cell.groups.length; i++) {
      const group = this.cell.groups[i];
      if (group.id === this.id && i !== 0) {
        pre_group = this.cell.groups[i - 1];
        return pre_group;
      }
    }
  }

  public get next_group (): Group | undefined {
    let next_group = null;
    for (let i = 0; i < this.cell.groups.length; i++) {
      const group = this.cell.groups[i];
      if (group.id === this.id) {
        next_group = this.cell.groups[i + 1];
        return next_group;
      }
    }
  }

  /**
   * 修改分组id
   * @param id 分组id
   */
  modifyId (id: string) {
    const repeatGroup = this.cell.editor.selection.getGroupByGroupId(id);
    if (repeatGroup && repeatGroup !== this) {
      this.cell.editor.event.emit("message", { type: "warning", msg: "存在相同ID分组！" });
      return;
    }

    this.cell.editor.history.clear();
    this.cell.paragraph.forEach((p) => {
      if (p.group_id === this.id) {
        p.group_id = id;
      }
    });
    this.id = id;
  }

  /**
   * 设置是否锁定该分组
   * @param islock 锁
   */
  setGroupLock (islock: boolean) {
    this.lock = islock;
  }

  /**
   * 段落排序
   * @param parameter 排序的擦参照依据
   */
  sortGroup (parameter: string) {
    // 如果分组数小于2 则不需要排序
    if (this.cell.groups.length < 2) return false;
    // 获取所有的paragraphs
    const paragraphs = this.cell.paragraph;
    // 获取到所有的分组
    const groups: Group[] = this.cell.groups;
    // 正确的分组排序
    groups.sort((group: any, next_group: any) => group[parameter] - next_group[parameter]);
    // 排序后的段落数组
    const sort_result_para: (Paragraph | Table)[] = [];
    // 定义一个用作确定分组的自变量
    let group_index: number = 0;
    for (let i = 0; i < paragraphs.length;) {
      const element = paragraphs[i];
      // 普通段落
      if (!element.group_id) {
        sort_result_para.push(element);
        i += 1;
      } else {
        const groupParas = groups[group_index].paragraph;
        if (!groupParas.length) {
          group_index += 1; // 自变量加以 表示下一个
          continue;
        }
        // 分组组内的数组
        sort_result_para.push(...groupParas);
        // 找到当前元素 element所在的分组
        const ele_group = groups.find((group) => group.id === element.group_id);
        group_index += 1; // 自变量加以 表示下一个
        i += (ele_group?.paragraph.length ?? 0);
      }
    }
    this.cell.paragraph = sort_result_para;
    // 至此排序完成 将排序后的结果数据扁平化处理 赋值给cell的children
    this.cell.updateParaToCell();
    return true;
  }

  /**
   * 删除分组
   * @returns 删除成功
   */
  remove () {
    const fields = this.getGroupFields();
    for (let i = 0; i < fields.length; i++) {
      const field = fields[i];
      field.remove();
    }

    for (let j = 0; j < this.cell.paragraph.length;) {
      const para = this.cell.paragraph[j];
      // 删除对应段落
      if (para.group_id === this.id) {
        para.remove();
      } else {
        j++;
      }
    }
    // 删除分组信息
    for (let i = 0; i < this.cell.groups.length; i++) {
      const group = this.cell.groups[i];
      if (this.id === group.id) {
        // 删除当前分组
        this.cell.groups.splice(i, 1);
      }
    }

    return true;
  }

  /**
   * 清空分组内容
   */
  clear () {
    // 先获取当前分组内的所有文本域进行删除
    const fields = this.getGroupFields();
    for (let i = 0; i < fields.length; i++) {
      const field = fields[i];
      field.remove();
    }
    for (let i = 0; i < this.content_para_id.length;) {
      const para_id = this.content_para_id[i];
      const para_table = this.cell.paragraph.find((item) => para_id === item.id);
      if (i === 0 && isTable(para_table)) {
        para_table.remove();
      } else {
        if (i === 0 && isParagraph(para_table)) {
          para_table.clear();
          i++;
        } else {
          para_table!.remove();
        }
      }
    }
  }

  /**
   * 根据name或者id获取当前分组内的文本域,默认不传则获取所有文本域
   * @param type
   * @param value
   * @return 查询到的文本域数组
   */
  getGroupFields (type?: "name" | "id", value?: string): XField[] {
    let result_fields: XField[] = [];
    // 拿到文本域id
    const result_field_ids: string[] = [];
    // 根据该分组内所有段落获取所有文本域id
    this.paragraph.forEach(paragraphOrTable => {
      if (isTable(paragraphOrTable)) {
        paragraphOrTable.children.forEach(cell => {
          result_fields.push(...cell.fields);
        });
      } else {
        result_field_ids.push(...paragraphOrTable.fieldsId);
      }
    });
    result_field_ids.forEach(id => {
      const field = this.cell.getFieldById(id);
      if (field) {
        result_fields.push(field);
      }
    });
    if (type && value) {
      result_fields = result_fields.filter(field => field[type] === value);
    }
    return result_fields;
  }

  getCells() {
    const tables = this.tables;
    const cells: Cell[] = [];
    tables.forEach(table => {
      cells.push(...table.children);
    });
    return cells;
  }

  /**
   * 刷新分组中段落id
   */
  refreshContentParaId () {
    this.content_para_id = this.cell.paragraph.filter(child => child.group_id === this.id).map(child => child.id);
  }
}
