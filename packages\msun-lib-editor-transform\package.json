{"name": "msun-lib-editor-transform", "version": "10.24.6", "description": "Editor Data Trans", "module": "./dist/editorDataTrans.esm.js", "exports": {".": "./src/index.ts"}, "publishConfig": {"exports": {".": "./dist/editorDataTrans.esm.js"}}, "files": ["dist", "lib"], "projectType": "library", "scripts": {"start": "vite", "build": "vite build", "debug": "tsc && vite build --mode=debug", "preview": "vite preview", "pack": "npm run build && npm pack --force"}, "keywords": [], "author": "张超，薛再强", "license": "ISC", "type": "module", "devDependencies": {"@babel/core": "^7.14.3", "@babel/preset-env": "7.14.4", "@typescript-eslint/eslint-plugin": "^5.56.0", "@typescript-eslint/parser": "^5.0.0", "eslint": "^7.28.0", "eslint-config-prettier": "^8.3.0", "eslint-config-standard": "^16.0.3", "eslint-plugin-import": "^2.23.4", "eslint-plugin-node": "^11.1.0", "eslint-plugin-prettier": "^3.4.0", "eslint-plugin-promise": "^5.1.0", "prettier": "^2.3.1", "rimraf": "^3.0.2", "rollup-plugin-visualizer": "^5.9.2", "tslib": "^2.2.0", "typescript": "^4.9.5", "vite": "^4.4.5", "vite-plugin-babel": "^1.1.3", "vite-plugin-dts": "^1.7.3"}, "repository": {"type": "git", "url": "https://gitlab.msunhis.com/msunClound/emr-editor-group/msun-editor-data-transform.git"}, "dependencies": {"msun-lib-editor-common": "workspace:*"}, "realVersion": "10.24.6"}