import {
  horizontal_page_size_config,
  old_vertical_page_size_config,
  vertical_page_size_config,
  font_size_config,
  fontHeightAsc
} from "./Config";
import Editor from "./Editor";
import { deepClone, serializeCopy } from "./Utils";
import { CommentConfig, RowLineType, RowLineTypeExplain, pageDirection, pageType } from "./Definition";
import { ScriptType } from "./Constant";
import { FontStyle } from "./Font";

// 表格插入行 携带的数据类型
export enum InsertRowCarryDataType {
  empty,
  all,
  clearField,
}

export enum GetDataType {
  rawData,
  compressedBase64,
}

export enum FieldShowMode {
  normal, // 原默认展示方式：边框会占用一个字符的宽度。
  borderless, // 无边框模式，但会保留文本域背景文本
  overShowBgColor, // 不显示边框，鼠标经过时展示背景色
  mergeSymbol, // 边框合并模式，开始或结束连续边框会合并，开始与结束边框连续会隐藏。  边框采用不占位方式展示，最好与文本域边框颜色配置搭配使用。
  showBgColor, // 使用浅蓝色代表文本域位置，然后结合鼠标经过时的颜色变化。 （文本域只读、分组锁定等不可编辑时不显示浅蓝背景色）
  shadowBorder, // 边框可见，但是不占位，鼠标悬停没有高亮
}

export enum InsertTemplateStyleType {
  origin, // 使用模板中的样式
  familyAndSize, // 使用默认的字号与字体
}

export default class EditorConfig {
  source: string = ""; // server 代表服务器端
  // 页面方向
  page_direction: pageDirection = "vertical";

  // 纸张类型
  page_size_type: pageType = "A4";

  //  纸张颜色
  page_color: string = "#ffffff"; // 改成 #FFF 或者 #FFFFFF 的话,页眉页脚即便不是编辑模式颜色也不会变浅 会一直跟正文一样

  // 光标闪烁
  cursor_blinks: boolean = false;

  history_limit: number = 50; // 历史堆栈的最大保存数量

  row_ratio: number = 1.6; // 默认行倍距 勿动

  background_color: string = "#ececec"; // 编辑器背景色

  editor_padding_top: number = 20; // 第一页距离canvas最顶部边界的距离(也可以说是页面上方的灰色区域高度)

  page_margin_bottom: number = 20; // 每一页 底部边界距离下一页顶部边界的距离或者最后一页距离canvas底部边界的距离

  page_padding_left: number = 80; // 页面内边距

  page_padding_top: number = 80; // 页面内容(包括页眉内容)到页面顶部的最小 top 值 也是角标横线距离页面边界的距离 改为 0 的时候角标绘制位置改了 但是有内容就不会看到内容位置变化

  page_padding_right: number = 80;

  page_padding_bottom: number = 80; // 页面内容(包括页脚内容)到页面底部的最小 bottom 值 也是角标横线距离页面边界的距离

  header_margin_top: number = 40; // 页眉边距距顶部 可以理解为 页眉中的首行内容顶部 距离当前页顶部边界的距离

  footer_margin_bottom: number = 42.5; // 页脚边距距底部 也可以理解为：页脚中 最后一样底部 距离当前页底部边界的距离

  content_margin_header: number = 5; // 内容距页眉底部边界的距离 改成0 目前效果是不生效的 因为还有 page_padding_top 这个值是在那个的基础上增加的值 都改为 0 或者将该值改大 就能看出来了

  content_margin_footer: number = 5; // 正文该页可编写的最后一行 底部 距页脚顶部的距离 也是跟 page_padding_bottom 有取舍优先级之类的

  show_header_line: boolean = true; // 是否显示页眉水平线

  show_footer_line: boolean = false; // 是否显示页脚水平线

  isShowCellGroupSign: boolean = true; // 是否展示单元格里边的成组标识

  table_padding_horizontal: number = 5; // 表格内字的边距

  insert_row_carry_data: InsertRowCarryDataType = InsertRowCarryDataType.empty; // 表格中插入行携带数据

  field_symbol_color: string = "blue";

  field_input_symbol_color: string = "black";

  field_start_symbol: string = "[";

  field_end_symbol: string = "]";

  field_select_start_symbol: string = "(";

  field_select_end_symbol: string = ")";

  show_field_symbol: boolean = true;

  imageSmoothingEnabled: boolean = true; // 使图片平滑

  placeholderColor: string = "gray";

  fieldAsteriskNames: string[] = [];// 需要匿名化展示的文本域名称

  fieldAsteriskRule: any = {}; // 匿名化规则

  keepSymbolWidthWhenPrint: boolean = false; // 当打印时保留文本域边框字符宽度

  fieldShowMode: FieldShowMode = FieldShowMode.normal; // 文本域展示模式

  fieldBgColor: string = ""; // 文本域背景色 跟 fieldShowMode 没有关系

  useGroupFormMode: boolean = false;// 使用分组表单模式

  group_relate_header_type: string = "after"; // 分组关联页眉方式,该配置主要用于病程记录中转科或者换床后页眉信息的变化，before 前关联，即分组所在页即会关联， after 后关联，可理解为使用每页最开始分组中的页眉信息。

  version: any = "";

  useBeforeVersion: boolean = false;

  widgetSelectColor: string = "#000000";

  contentBorder: Boolean = false; //page边框

  customPageSize: any = { // 自定义纸张大小
    width: 0,
    height: 0
  }

  show_corner_line: boolean = true;

  fastEntryMode: boolean = false;

  rowLineType: RowLineType = RowLineType.VOID; // 控制是否绘制行与行之间的线 目前是页眉页脚和单元格里边不绘制的 后期需求多的话 可以改 SOLID 的名字 叫详细准确点 然后再加其他的

  startPageNumber: number = 1;

  listItemIndent: number | undefined; // 缩进距离

  rowLineTypeExplain: RowLineTypeExplain = {
    includesTable: true, // 是否包括表格(表格里边绘制不绘制线)
    excludesEmpty: true // 是否包括下半页内容的空白(下半页是否绘制线)
  }; // rowLineType 的补充说明,比如空白的地方不要行线

  // 默认字体样式 // 注意：请务必保持默认字体样式属性与样式对比(font.isEqual)中一致，否则会重复往FontMap添加引起性能问题(每次对比都发现不一致都继续添加 fontMqp 导致 fontMap 太大)
  default_font_style: FontStyle = {
    family: "宋体", // 默认字体 宋体 勿动
    height: 16,
    bold: false,
    italic: false,
    underline: false,
    strikethrough: false,
    dblUnderLine: false,
    script: ScriptType.NORMAL,
    characterSpacing: 0,
    color: "#000",
    bgColor: null,
    highLight: null
  };

  rawData: any = {
    header: [{
      type: "p",
      align: "center",
      deepNum: 0,
      isList: false,
      isOrder: false,
      indentation: 0,
      before_paragraph_spacing: 0,
      row_ratio: this.row_ratio,
      children: []
    }],
    footer: [{
      type: "p",
      align: "center",
      deepNum: 0,
      isList: false,
      isOrder: false,
      indentation: 0,
      before_paragraph_spacing: 0,
      row_ratio: this.row_ratio,
      children: []
    }],
    content: [{
      type: "p",
      align: "left",
      deepNum: 0,
      isList: false,
      isOrder: false,
      indentation: 0,
      before_paragraph_spacing: 0,
      row_ratio: this.row_ratio,
      children: []
    }],
    groups: [],
    fontMap: {},
    config: { direction: "vertical" }
  };

  getDataType: GetDataType = GetDataType.rawData;

  // 需要校验数字类型的配置项
  checkConfigTypeList = ["height", "printRatio", "row_ratio", "editor_padding_top", "page_margin_bottom", "page_padding_left", "page_padding_right", "page_padding_top", "page_padding_bottom", "header_margin_top", "footer_margin_bottom", "content_margin_header", "content_margin_footer", "table_padding_horizontal", "localImageMaxSize"];

  /** 对编辑器提升无用的现场特殊需求配置 ******start***************/
  font_weight_normal: number = 400; // 普通字体宽度

  font_weight_bold: number = 700; // 加粗字体宽度

  old_page_size: boolean = false;

  editor_show_family: string = ""; // 编辑器展示时字体，一旦配置后编辑器中文本无论设置的什么字体都会展示成该字体，主要用于解决大石桥屏幕显示编辑器内容不清晰问题

  useLetterPlaceholder: boolean = false;// 输入时使用字母占位

  devicePixelRatio: number = (typeof window !== "undefined" && window.devicePixelRatio) || 2; // nodejs服务端没有window,需使用typeof window判断

  printRatio: number = 4;

  list_max_level: number = 6; // list列表的最大级别

  group_line_color: string = "blue";

  group_normal_color: string = "black";

  group_lock_bg_color: string = ""; //rgba(105,105,105,0.05)

  // 将批注分成两部分 一部分是文档中的文字 用 word 表示 一部分是批注列表 那部分 选项用 item 表示(card 好像更合适 但就用 item)
  comment: CommentConfig = {
    wordUnselectedBgColor: "#fff8df", // 不想要 要配置成 rgba(0, 0, 0, 0) 不能配置成白色 因为有护眼色等的时候 也是白色不好
    wordSelectedBgColor: "#FFD700",
    listWidth: 300,
    listBgColor: "#ffffff",
    listTitleColor: "#5E5F5F",
    listTitleNumColor: "gray",
    listTitleCrossColor: "#454A50",
    listTitleSwitchColor: "#000",
    listItemBgColor: "rgb(250,250,250)",
    hideCloseBtn: false,
    hideSwitch: true,
    hideDeleteBtn: false,
    hideReplaceBtn: false,
    hideDate: false,
    title: "批注列表",
    defaultAllOpen: false,
  };

  localImageMaxSize: number | undefined; // 单位是M


  cellLockBgColor: string = "#ffffff";

  copyStyleMode: number = 0; // 复制样式的模式 0 就是什么样式都不带 1 就是只带表格的样式 2 就是带着所有的样式

  insertTemplateUseDefaultSetting: any = {
    family: 0, // 传1使用编辑器字体
    size: 0, // 传1使用编辑器字号
    rowRatio: 0// 传1使用编辑器行间距
  };

  /** ************************end***************************/

  copy() {
    const cloneObj = deepClone(this);
    const newEditorConfig = Object.assign(new EditorConfig(), cloneObj);
    return newEditorConfig;
  }

  // 处理配置 是否使用 rawData 中的配置
  handleConfig(editor: Editor, rawData: any, configItemUsedInRawData: string[],isTemplate:boolean=false) {
    if (rawData.config && configItemUsedInRawData.length > 0) {
      let configItemInfo
      if(!isTemplate){
        configItemInfo = rawData.config.page_info;
      }else{
        configItemInfo = rawData.config;
        configItemUsedInRawData.forEach((option:any)=>{
          if(option==="show_header_line"){
            if(rawData.config.header_horizontal!==undefined){
              editor.config.show_header_line=!!rawData.config.header_horizontal
            }
          }else if(option==="show_footer_line"){
            if(rawData.config.footer_horizontal!==undefined){
              editor.config.show_footer_line=!!rawData.config.footer_horizontal
              editor.raw.config.footer_horizontal=!!rawData.config.footer_horizontal
            }
          }else if(option==="useComments"){
            if(!rawData.meta) rawData.meta={}
            if(editor.document_meta&&editor.document_meta.commentsIDSet){
              editor.document_meta.commentsIDSet=Object.assign({},editor.document_meta.commentsIDSet,rawData.meta.commentsIDSet)
            }else if(editor.document_meta&&!editor.document_meta.commentsIDSet){
              editor.document_meta.commentsIDSet={}
              editor.document_meta.commentsIDSet=Object.assign({},editor.document_meta.commentsIDSet,rawData.meta.commentsIDSet)
            }
          }
        })
      }
      if (configItemUsedInRawData[0] === "all") {
        for (const item in configItemInfo) {
          (editor.config as any)[item] = configItemInfo[item];
        }
      } else {
        for (const item in configItemInfo) {
          if (configItemUsedInRawData.find(config => config === item)) {
            (editor.config as any)[item] = configItemInfo[item];
          }
        }
      }
    }
    if (rawData.config) {
      // 设置页面大小和方向
      editor.config.setPageSize(editor.config.page_size_type, rawData.config.direction, rawData.config.page_info?.page_size);
    } else if (editor.config.page_direction !== "vertical") {
      // 如果文档中没有配置的横向纵向信息，且当前页面方向与默认方向不同，则还原页面方向为纵向
      editor.config.setPageSize(editor.config.page_size_type, "vertical", rawData.config.page_info?.page_size);
    }
  }

  /**
   * 校验config配置
   * @param custom_config 外部传入的config
   * @returns 校验后的config
   */
  checkCustomConfig(custom_config: any) {
    this.checkConfigTypeList.forEach((e) => {
      let element = custom_config[e];
      if (e === "height") {
        element = custom_config.default_font_style?.height;
      }
      if (e === "localImageMaxSize" && element < 0.1) {
        alert( e + " 配置项必须大于 0.1");
        throw new Error(e + " 配置项必须大于 0.1");
      }
      if (element) {
        if (!Number(element)) {
          const msg = "【" + e + "】  配置数据类型错误，请联系系统配置管理员";
          alert(msg);
          throw new Error(msg);
        } else {
          if (e === "height" && custom_config.default_font_style && custom_config.default_font_style.height) {
            custom_config.default_font_style.height = Number(element);
          } else {
            custom_config[e] = Number(element);
          }
        }
      }
    });

    if (custom_config.page_size_type && custom_config.page_size_type !== "custom") {
      if ((vertical_page_size_config as any)[(custom_config.page_size_type).toLocaleUpperCase()]) {
        custom_config.page_size_type = (custom_config.page_size_type).toLocaleUpperCase();
      } else {
        const msg = "页面纸张类型配置错误，请联系系统配置管理员";
        alert(msg);
        throw new Error(msg);
      }
    }
    if (custom_config.default_font_style) {
      if (custom_config.default_font_style.height) {
        if (!fontHeightAsc.find(n => n === custom_config.default_font_style.height)) {
          const msg = "字体配置不符合要求，可配置的字体高度为：" + fontHeightAsc.join(", ");
          alert(msg);
          throw new Error(msg);
        }
      }
      // custom_config.default_font_style = Object.assign(default_font_style_1, custom_config.default_font_style);
    }
    if (custom_config.printRatio && custom_config.printRatio > 6) {
      const msg = "打印系数超出配置，最大打印系数为6，请联系系统配置管理员";
      alert(msg);
      throw new Error(msg);
    }
    if (custom_config.row_ratio && custom_config.row_ratio > 6) {
      const msg = "行倍距超出配置，最大行倍距为6，请联系系统配置管理员";
      alert(msg);
      throw new Error(msg);
    }
    if ((custom_config.content_margin_header && custom_config.content_margin_header > 200) || (custom_config.content_margin_footer && custom_config.content_margin_footer > 200)) {
      const msg = "内容距页眉或页脚距离超出配置，最大距离为200，请联系系统配置管理员";
      alert(msg);
      throw new Error(msg);
    }
    if (custom_config.table_padding_horizontal && custom_config.table_padding_horizontal > 100) {
      const msg = "表格内字边距超出配置，最大行倍距为100，请联系系统配置管理员";
      alert(msg);
      throw new Error(msg);
    }
    return custom_config;
  }

  /**
     * 合并传入的配置到编辑器实例
     * @param custom_config 传入的自定义配置，请勿修改传入的值，会影响产品继续使用
     */
  mergeCustomConfig(custom_config: any) {
    custom_config = serializeCopy(custom_config);

    if (custom_config.insertTemplateUseDefaultFont === 1) {
      custom_config.insertTemplateUseDefaultSetting = {
        family: 1, // 传1使用编辑器字体
        size: 1, // 传1使用编辑器字号
        rowRatio: 0// 传1使用编辑器行间距
      };
    }
    this.checkCustomConfig(custom_config);
    custom_config.default_font_style && Object.assign(this.default_font_style, custom_config.default_font_style);
    delete custom_config.default_font_style; // 如果不删除的话 下边就又都合并进去了

    custom_config.comment && Object.assign(this.comment, custom_config.comment);
    delete custom_config.comment;

    if (custom_config.page_size_type && custom_config.page_size_type === "custom") {
      this.customPageSize = custom_config.page_size;
    }
    Object.assign(this, custom_config);
    // const checked_custom_config = this.checkCustomConfig(custom_config);
    // Object.assign(this, checked_custom_config);
    // custom_config.default_font_style && Object.assign(this.default_font_style, custom_config.default_font_style);

    // if (custom_config.page_size_type) {
    //   this.page_size_type = custom_config.page_size_type;
    // }
    // if (custom_config.cursor_blinks) {
    //   this.cursor_blinks = custom_config.cursor_blinks;
    // }
    if (custom_config.font_family) {
      this.default_font_style.family = custom_config.font_family;
    }
    // 如果传入了font_size,那么该属性设置的优先级最高
    if (custom_config.font_size) {
      for (let i = 0; i < font_size_config.length; i++) {
        const font_size = font_size_config[i];
        if (custom_config.font_size === font_size.option) {
          this.default_font_style.height = font_size.value;
          break;
        }
      }
    }
  }

  /**
     * 初始化编辑器配置
     * @param custom_config
     */
  init(custom_config: any) {
    this.mergeCustomConfig(custom_config);
    this.rawData.header[0].row_ratio = this.row_ratio;
    this.rawData.content[0].row_ratio = this.row_ratio;
    this.rawData.footer[0].row_ratio = this.row_ratio;
  }

  setPageSize(page_size_type: pageType, page_direction: pageDirection, page_size: any) {
    this.page_size_type = page_size_type;
    if (page_direction) {
      this.page_direction = page_direction;
    }

    if (page_size_type === "custom" && page_size && page_size.width) {
      if (page_direction === "vertical") {
        this.customPageSize = {
          width: page_size.width,
          height: page_size.height
        };
      } else {
        this.customPageSize = {
          width: page_size.height,
          height: page_size.width
        };
      }
    }
  }

  getPageType() {
    return this.page_size_type;
  }

  getDirection() {
    return this.page_direction;
  }

  setCustomPageSize(width: number, height: number, direction: pageDirection) {
    const screenWidth = window.screen.width;
    const screenHeight = window.screen.height;
    const screenResolution = 96; // 假设默认的DPI为96
    this.page_size_type = "custom";
    this.page_direction = direction;
    const diagonalInches = Math.sqrt(screenWidth ** 2 + screenHeight ** 2) / screenResolution;
    const dpi = Math.sqrt(screenWidth ** 2 + screenHeight ** 2) / diagonalInches;
    const widthPx = Math.round(width * dpi / 2.54);
    const heightPx = Math.round(height * dpi / 2.54);
    this.customPageSize = {
      width: widthPx,
      height: heightPx
    };
    return  this.customPageSize
  }

  getPageSize() {
    let page_size;
    if (this.page_size_type === "custom") {
      if (this.page_direction === "horizontal") {
        page_size = { width: this.customPageSize.height, height: this.customPageSize.width };
      } else {
        page_size = this.customPageSize;
      }
    } else {
      if (this.page_direction === "horizontal") {
        page_size = horizontal_page_size_config[this.page_size_type];
      } else {
        if (this.old_page_size) {
          page_size = old_vertical_page_size_config[this.page_size_type];
        } else {
          page_size = vertical_page_size_config[this.page_size_type];
        }
      }
    }
    if (page_size) {
      return { ...page_size };
    } else {
      throw new Error("页面纸张类型配置错误!");
    }
  }
}
