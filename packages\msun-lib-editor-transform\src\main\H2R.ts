import newRawInitModel from "../utils/Util";

/**
 * html数据转原始数据
 */

export const H2R = {
  config: {},
  // 用于记录font,过滤掉内容中不存在的样式或者重复的样式
  newFontMap: {},
  newFontArray: [],
  newImageMap: new Map(),
  /**
   * 处理fontMap数据
   * @param font
   * @return font_id
   */
  handleFontMapData (font: any): string {
    const res_font = this.newFontArray.find((item: any) => this.fontIsEqual(item, font));
    if (res_font) {
      return res_font.id;
    } else {
      font.id = this.uuid("font");
      this.newFontArray.push(font);
      // @ts-ignore
      this.newFontMap[font.id] = font;
      // @ts-ignore
      return font.id;
    }
  },
  handleImageMapData (src: string, pointId?:string): string {
    const imageId = this.newImageMap.get(src);
    if (imageId) {
      return imageId;
    } else {
      const newImageId = pointId ?? this.uuid("image");
      this.newImageMap.set(src, newImageId);
      return newImageId;
    }
  },
  clearInitData () {
    this.newFontMap = {};
    this.newFontArray = [];
    this.newImageMap.clear();
  },
  // 用于记录font,过滤掉内容中不存在的样式或者重复的样式
  initExtOptions (instance: any) {
    this.editor = instance.editor;
    this.TypeJudgment = instance.TypeJudgment;
    this.uuid = instance.utils.getUUID;
    this.fontIsEqual = instance.utils.fontIsEqual;
    this.fontSizeList = instance.builtInVariable.fontSizeList;
    this.colorToHex = instance.utils.colorToHex;
  },

  html2RawData (htmlStr:string) {
    const rawData:any = {
      header: [],
      footer: [],
      content: []
    };
    this.clearInitData();
    try {
      // // 解析 XML 数据
      const parser = new DOMParser();
      const xmlDoc = parser.parseFromString(htmlStr, "text/html"); // 将解析器参数改为 text/html
      this.convertTags(xmlDoc.body);
      this.handleBodyContent(xmlDoc.body, rawData);
      rawData.fontMap = this.newFontMap;
      if (rawData.content.length) {
        return rawData;
      }
    } catch (e) {
      console.error(e);
    }
  },
  convertTags (element:any) {
    const children = element.children;
    for (let i = 0; i < children.length; i++) {
      const child = children[i];
      const tagName = child.tagName.toLowerCase();
      if (tagName !== "table" &&
            tagName !== "tr" &&
            tagName !== "td" &&
            tagName !== "tbody" &&
            tagName !== "span") {
        // 创建一个新的<p>标签
        let newElement;
        if (this.checkElementParaBehavior(child)) {
          newElement = document.createElement("p");
        } else {
          newElement = document.createElement("span");
        }
        // 复制原始标签的基本样式
        newElement.setAttribute("class", child.getAttribute("class"));
        newElement.style.cssText = child.style.cssText;
        this.handleTagFontStyle(child, newElement);
        if (tagName === "br") {
          newElement.innerText = "\n";
        }
        // 将原始子元素的转换结果插入新建的<p>标签中
        newElement.innerHTML = this.convertTags(child).innerHTML;
        // 将新建的<p>标签替换原始标签
        child.parentNode.replaceChild(newElement, child);
      } else {
        // 递归处理子元素，并将转换结果插入到当前元素中
        const convertedChild = this.convertTags(child);
        if (convertedChild !== child) {
          element.replaceChild(convertedChild, child);
        }
      }
    }
    // 返回处理后的元素
    return element;
  },
  // 处理标签类样式
  handleTagFontStyle (oriEle:any, newEle:any) {
    const tagName = oriEle.tagName;
    const setNewByOri = () => {
      if (oriEle.parentElement && oriEle.parentElement.textDecoration) {
        newEle.style.textDecoration += " " + oriEle.parentElement.textDecoration;
      }
      if (oriEle.parentElement && oriEle.parentElement.fontStyle) {
        newEle.style.fontStyle += " " + oriEle.parentElement.fontStyle;
      }
      if (oriEle.parentElement && oriEle.parentElement.fontWeight) {
        newEle.style.fontWeight = oriEle.parentElement.fontWeight;
      }
    };

    if (tagName === "S") {
      newEle.style.textDecoration += " line-through "; // 设置删除线样式
      oriEle.textDecoration = newEle.style.textDecoration; // 设置删除线样式
      setNewByOri();
    }
    if (tagName === "U") {
      newEle.style.textDecoration += " underline "; // 添加下划线样式
      oriEle.textDecoration = newEle.style.textDecoration; // 添加下划线样式
      setNewByOri();
    }
    if (tagName === "B") {
      newEle.style.fontWeight = "bold"; // 添加下划线样式
      oriEle.fontWeight = newEle.style.fontWeight;
      setNewByOri();
    }
    if (tagName === "I") {
      newEle.style.fontStyle += " italic "; // 添加下划线样式
      oriEle.fontStyle = newEle.style.fontStyle; // 添加下划线样式
      setNewByOri();
    }
  },
  // 处理嵌套标签并合并样式的函数
  getDefaultDisplay (tagName:any) {
    const tempElement = document.createElement(tagName);
    document.body.appendChild(tempElement);
    const display = window.getComputedStyle(tempElement).getPropertyValue("display");
    document.body.removeChild(tempElement);
    return display;
  },
  // 判断元素的display属性来确定其行为
  checkElementParaBehavior (element:any) {
    const display = this.getDefaultDisplay(element.tagName);
    return display === "block";
  },
  handleBodyContent (body:any, rawData:any) {
    const content:any = [];
    rawData.content = content;
    const children = body.children;
    if (!children.length) {
      return false;
    }
    // 获取段落和表格的集合
    if (children[0].tagName === "P" || children[0].tagName === "TABLE") {
      for (let i = 0, len = children.length; i < len; i++) {
        const p = children[i];
        this.handlePara(p, content);
      }
    } else {
      this.handleParaChildren(body, content);
    }
  },
  handleParaChildren (para:any, content:any) {
    const children = para.childNodes;
    if (!children) {
      return;
    }
    let paraRaw = newRawInitModel("p");
    paraRaw.id = this.uuid("para");
    paraRaw.children = [];
    // 设置段落样式
    this.handleParaStyle(para.style, paraRaw);
    for (let i = 0, len = children.length; i < len; i++) {
      const item = children[i];
      if (item.nodeName === "#comment") {
        continue;
      }
      let text = item.nodeValue ?? item.innerText ?? "";
      const split = text.split("\n");
      if (split.length > 1) {
        for (let j = 0; j < split.length; j++) {
          text = split[j];
          this.handleSpan(text, item, paraRaw);
          content.push(paraRaw);
          paraRaw = newRawInitModel("p");
          paraRaw.id = this.uuid("para");
          paraRaw.children = [];
        }
      } else {
        this.handleSpan(text, item, paraRaw);
      }
    }
    content.push(paraRaw);
  },
  handleParaStyle (style:any, paraRaw:any) {
    const align = style["text-align"];
    paraRaw.align = align;
  },
  handlePara (para:any, content:any) {
    if (para.tagName === "TABLE") {
      const tableRaw = newRawInitModel("table");
      tableRaw.id = this.uuid("table");
      tableRaw.children = [];
      this.handleTableBody(para, tableRaw);
      content.push(tableRaw);
    } else if (para.tagName === "P") {
      this.handleParaChildren(para, content);
    } else {
      console.warn(para);
    }
  },
  handleTableBody (table:any, tableRaw:any) {
    const bodies = table.children;
    let maxColLen = 0;
    // table分三部分， header、footer、和body
    for (let i = 0, len = bodies.length; i < len; i++) {
      const body = bodies[i];
      for (let j = 0, len2 = body.children.length; j < len2; j++) {
        const tr = body.children[j];
        tableRaw.row_size.push(20);
        tableRaw.min_row_size.push(20);
        maxColLen = Math.max(maxColLen, tr.children.length);
        for (let k = 0, len3 = tr.children.length; k < len3; k++) {
          const td = tr.children[k];
          this.handleCell([j, k], td, tableRaw);
        }
      }
    }
    tableRaw.col_size = new Array(maxColLen).fill(800 / maxColLen);
  },
  handleCell (pos:number[], cell:any, tableRaw:any) {
    const cellRaw = newRawInitModel("cell");
    cellRaw.id = this.uuid("cell");
    cellRaw.pos = pos;
    const children = cell.children;
    for (let i = 0, len = children.length; i < len; i++) {
      this.handlePara(children[i], cellRaw.children);
    }
    if (!cellRaw.children.length) {
      const paraRaw = newRawInitModel("p");
      paraRaw.id = this.uuid("para");
      cellRaw.children.push(paraRaw);
    }
    tableRaw.cells.push(cellRaw);
  },
  getEffectiveStyle (element:any, propertyName:any, isInit:boolean) {
    if (isInit) {
      const style = this.getStyleAttributes(element);
      if (style[propertyName] && style[propertyName] !== "inherit") {
        return style[propertyName];
      }
    }
    if (element.style && element.style[propertyName] && element.style[propertyName] !== "inherit") {
      return element.style[propertyName];
    }
    const computedStyle = window.getComputedStyle(element);
    const propertyValue = computedStyle.getPropertyValue(propertyName);
    if (propertyValue === "inherit" || !propertyValue || propertyValue === "none") {
      // 如果属性值为 'inherit'，则递归获取父元素的样式
      const parentElement = element.parentElement;
      if (parentElement) {
        return this.getEffectiveStyle(parentElement, propertyName, false);
      }
    }
    return propertyValue;
  },
  handleSpan (text:string, node:any, para:any) {
    if (text) {
      if (node.childNodes && node.childNodes.length) {
        for (let i = 0, len = node.childNodes.length; i < len; i++) {
          const nn = node.childNodes[i];
          const text = nn.nodeValue ?? nn.innerText ?? "";
          this.handleSpan(text, nn, para);
        }
      } else {
        const textRaw = newRawInitModel("text");
        para.children.push(textRaw);
        textRaw.value = text;
        let element:any = node;
        if (!(node instanceof HTMLElement)) {
          element = node.parentNode;
        }
        const computedStyle:any = {};
        const allProp:any = ["font-family", "font-style", "font-weight", "color",
          "background-color", "background", "text-decoration", "border-bottom", "vertical-align", "font-size"];
        for (let i = 0; i < allProp.length; i++) {
          computedStyle[allProp[i]] = this.getEffectiveStyle(element, allProp[i], true);
        }
        // console.log(computedStyle);
        const fontId = this.handleSpanStyle(computedStyle);
        textRaw.font_id = fontId;
      }
    }
  },
  // 定义一个函数，用于获取节点的样式
  getStyleAttributes (node:any) {
    if (!node.innerHTML || !node.innerHTML.startsWith("<")) {
      return {};
    }
    const str = node.innerHTML.replace(/\n/g, "");
    // 定义正则表达式来匹配样式字符串
    const styleRegex = /style="([^"]*)"/;
    // 匹配样式字符串
    const styleMatch = str.match(styleRegex);
    // 初始化一个空对象来存储样式属性
    const styleObj:any = {};

    // 如果匹配到了样式字符串
    if (styleMatch && styleMatch.length > 1) {
      // 将样式字符串按分号分隔成数组
      const styleArray = styleMatch[1].split(";");

      // 遍历样式数组
      styleArray.forEach(function (style:any) {
        // 将每个样式属性按冒号分隔成键值对
        const pair = style.split(":");
        if (pair.length === 2) {
          // 去除属性名和属性值两端的空格，并存储到样式对象中
          if (!styleObj[pair[0].trim()]) {
            styleObj[pair[0].trim()] = pair[1].trim();
          }
        }
      });
    }

    return styleObj;
  },
  handleSpanStyle (style:any) {
    const fontStyle = newRawInitModel("style");
    if (style["font-family"]) {
      fontStyle.family = style["font-family"];
    }
    if (style["font-style"]?.indexOf("italic") > -1) {
      fontStyle.italic = true;
    }
    if (style["font-weight"]?.indexOf("bold") > -1) {
      fontStyle.bold = true;
    }
    if (style.color) {
      fontStyle.color = this.colorToHex(style.color);
    }
    if (style["background-color"]) {
      fontStyle.bgColor = this.colorToHex(style["background-color"]);
    }
    if (style.background) {
      fontStyle.bgColor = this.colorToHex(style.background);
    }
    if (style["text-decoration"]?.indexOf("underline") > -1) {
      fontStyle.underline = true;
    }
    if (style["text-decoration"]?.indexOf("line-through") > -1) {
      fontStyle.strikethrough = true;
    }
    if (style["border-bottom"]?.indexOf("double") > -1) {
      fontStyle.dblUnderLine = true;
    }
    if (style["vertical-align"]?.indexOf("super") > -1) {
      fontStyle.script = 1;
    }
    if (style["vertical-align"]?.indexOf("sub") > -1) {
      fontStyle.script = 2;
    }
    const fontSizeStr = style["font-size"];
    if (fontSizeStr) {
      const fontSize = parseFloat(fontSizeStr).toString();
      if (fontSizeStr.endsWith("pt")) {
        const find = this.fontSizeList.find((item:any) => item.option === fontSize);
        if (find) {
          fontStyle.height = find.value;
        }
      } else if (fontSizeStr.endsWith("rem")) {
        const remToPxRatio = this.getRootEleFontSize();
        // 计算 rem 字号值对应的像素值
        fontStyle.height = Number(fontSize) * remToPxRatio;
      } else {
        fontStyle.height = Number(fontSize);
      }
    }
    // console.log(style["font-family"],
    //   style["font-style"],
    //   style["font-weight"],
    //   style.color,
    //   style["background-color"],
    //   style.background,
    //   style["text-decoration"],
    //   style["border-bottom"],
    //   style["vertical-align"], style["vertical-align"], fontSizeStr);
    const fontId = this.handleFontMapData(fontStyle);
    return fontId;
  },
  getRootEleFontSize () {
    // 获取根元素
    const rootElement = document.documentElement;

    // 获取根元素的样式信息
    const rootStyles = window.getComputedStyle(rootElement);

    // 提取根元素的字号大小
    const rootFontSize = rootStyles.fontSize;

    return parseFloat(rootFontSize);
  }
};
