## 2.30.16

## 10.24.6

### Patch Changes

-
- Updated dependencies
  - msun-lib-editor-transform@10.24.6

## 10.24.5

### Patch Changes

-
- Updated dependencies
  - msun-lib-editor-transform@10.24.5

## 10.24.4

### Patch Changes

-
- Updated dependencies
  - msun-lib-editor-transform@10.24.4

## 10.24.3

### Patch Changes

-
- Updated dependencies
  - msun-lib-editor-transform@10.24.3

## 10.24.2

### Patch Changes

-
- Updated dependencies
  - msun-lib-editor-transform@10.24.2

## 10.24.1

### Patch Changes

-
- Updated dependencies
  - msun-lib-editor-transform@10.24.1

## 10.24.0

### Minor Changes

-

### Patch Changes

- Updated dependencies
  - msun-lib-editor-transform@10.24.0

## 10.23.2

### Patch Changes

-
- Updated dependencies
  - msun-lib-editor-transform@10.23.2

## 10.23.1

### Patch Changes

-
- Updated dependencies
  - msun-lib-editor-transform@10.23.1

## 10.23.0

### Minor Changes

-

### Patch Changes

- Updated dependencies
  - msun-lib-editor-transform@10.23.0

## 10.22.2

### Patch Changes

-
- Updated dependencies
  - msun-lib-editor-transform@10.22.2

## 10.22.1

### Patch Changes

-
- Updated dependencies
  - msun-lib-editor-transform@10.22.1

## 10.22.0

### Minor Changes

-

### Patch Changes

- Updated dependencies
  - msun-lib-editor-transform@10.22.0

## 10.21.11

### Patch Changes

-
- Updated dependencies
  - msun-lib-editor-transform@10.21.11

## 10.21.10

### Patch Changes

-
- Updated dependencies
  - msun-lib-editor-transform@10.21.10

## 10.21.9

### Patch Changes

-
- Updated dependencies
  - msun-lib-editor-transform@10.21.9

## 10.21.8

### Patch Changes

-
- Updated dependencies
  - msun-lib-editor-transform@10.21.8

## 10.21.7

### Patch Changes

-
- Updated dependencies
  - msun-lib-editor-transform@10.21.7

## 10.21.6

### Patch Changes

-
- Updated dependencies
  - msun-lib-editor-transform@10.21.6

## 10.21.5

### Patch Changes

-
- Updated dependencies
  - msun-lib-editor-transform@10.21.5

## 10.21.4

### Patch Changes

-
- Updated dependencies
  - msun-lib-editor-transform@10.21.4

## 10.21.3

### Patch Changes

-
- Updated dependencies
  - msun-lib-editor-transform@10.21.3

## 10.21.2

### Patch Changes

-
- Updated dependencies
  - msun-lib-editor-transform@10.21.2

## 10.21.1

### Patch Changes

-
- Updated dependencies
  - msun-lib-editor-transform@10.21.1

## 10.21.0

### Minor Changes

-

### Patch Changes

- Updated dependencies
  - msun-lib-editor-transform@10.21.0

## 10.20.1

### Patch Changes

-
- Updated dependencies
  - msun-lib-editor-transform@10.20.1

## 10.20.0

### Minor Changes

-

### Patch Changes

- Updated dependencies
  - msun-lib-editor-transform@10.20.0

## 10.19.2

### Patch Changes

-
- Updated dependencies
  - msun-lib-editor-transform@10.19.2

## 10.19.1

### Patch Changes

-
- Updated dependencies
  - msun-lib-editor-transform@10.19.1

## 10.19.0

### Minor Changes

-

### Patch Changes

- Updated dependencies
  - msun-lib-editor-transform@10.19.0

## 10.18.0

### Minor Changes

-

### Patch Changes

- Updated dependencies
  - msun-lib-editor-transform@10.18.0

## 10.17.6

### Patch Changes

-
- Updated dependencies
  - msun-lib-editor-transform@10.17.6

## 10.17.5

### Patch Changes

-
- Updated dependencies
  - msun-lib-editor-transform@10.17.5

## 10.17.4

### Patch Changes

-
- Updated dependencies
  - msun-lib-editor-transform@10.17.4

## 10.17.3

### Patch Changes

-
- Updated dependencies
  - msun-lib-editor-transform@10.17.3

## 10.17.2

### Patch Changes

-
- Updated dependencies
  - msun-lib-editor-transform@10.17.2

## 10.17.1

### Patch Changes

-
- Updated dependencies
  - msun-lib-editor-transform@10.17.1

## 10.17.0

### Minor Changes

-

### Patch Changes

- Updated dependencies
  - msun-lib-editor-transform@10.17.0

## 10.16.5

### Patch Changes

-
- Updated dependencies
  - msun-lib-editor-transform@10.16.5

## 10.16.4

### Patch Changes

-
- Updated dependencies
  - msun-lib-editor-transform@10.16.4

## 10.16.3

### Patch Changes

-
- Updated dependencies
  - msun-lib-editor-transform@10.16.3

## 10.16.2

### Patch Changes

-
- Updated dependencies
  - msun-lib-editor-transform@10.16.2

## 10.16.1

### Patch Changes

-
- Updated dependencies
  - msun-lib-editor-transform@10.16.1

## 10.16.0

### Minor Changes

-

### Patch Changes

- Updated dependencies
  - msun-lib-editor-transform@10.16.0

## 10.15.6

### Patch Changes

-
- Updated dependencies
  - msun-lib-editor-transform@10.15.6

## 10.15.5

### Patch Changes

-
- Updated dependencies
  - msun-lib-editor-transform@10.15.5

## 10.15.4

### Patch Changes

-
- Updated dependencies
  - msun-lib-editor-transform@10.15.4

## 10.15.3

### Patch Changes

-
- Updated dependencies
  - msun-lib-editor-transform@10.15.3

## 10.15.2

### Patch Changes

-
- Updated dependencies
  - msun-lib-editor-transform@10.15.2

## 10.15.1

### Patch Changes

-
- Updated dependencies
  - msun-lib-editor-transform@10.15.1

## 10.15.0

### Minor Changes

-

### Patch Changes

- Updated dependencies
  - msun-lib-editor-transform@10.15.0

## 10.14.5

### Patch Changes

-
- Updated dependencies
  - msun-lib-editor-transform@10.14.5

## 10.14.4

### Patch Changes

-
- Updated dependencies
  - msun-lib-editor-transform@10.14.4

## 10.14.3

### Patch Changes

-
- Updated dependencies
  - msun-lib-editor-transform@10.14.3

## 10.14.2

## 10.13.5

### Patch Changes

-
- Updated dependencies
  - msun-lib-editor-transform@10.14.2

## 10.14.1

- msun-lib-editor-transform@10.13.5

## 10.13.4

### Patch Changes

-
- Updated dependencies
  - msun-lib-editor-transform@10.14.1

## 10.14.0

### Minor Changes

-

### Patch Changes

- Updated dependencies
  - msun-lib-editor-transform@10.14.0
  - msun-lib-editor-transform@10.13.4

## 10.13.3

### Patch Changes

-
- Updated dependencies
  - msun-lib-editor-transform@10.13.3

## 10.13.2

### Patch Changes

-
- Updated dependencies
  - msun-lib-editor-transform@10.13.2

## 10.13.1

### Patch Changes

-
- Updated dependencies
  - msun-lib-editor-transform@10.13.1

## 10.13.0

### Minor Changes

-

## 10.12.10

### Patch Changes

-
- Updated dependencies
  - msun-lib-editor-transform@10.12.10

## 10.12.9

### Patch Changes

-
- Updated dependencies
  - msun-lib-editor-transform@10.12.9

## 10.12.8

### Patch Changes

- Updated dependencies
  - msun-lib-editor-transform@10.13.0

## 10.12.5

### Patch Changes

-
- Updated dependencies
  - msun-lib-editor-transform@10.12.5

## 10.12.4

### Patch Changes

-
- Updated dependencies
  - msun-lib-editor-transform@10.12.4

## 10.12.3

### Patch Changes

-
- Updated dependencies
  - msun-lib-editor-transform@10.12.3

## 10.12.2

### Patch Changes

-
- Updated dependencies
  - msun-lib-editor-transform@10.12.2

## 10.12.1

### Patch Changes

-
- Updated dependencies
  - msun-lib-editor-transform@10.12.1

## 10.12.0

### Minor Changes

-

### Patch Changes

- Updated dependencies
  - msun-lib-editor-transform@10.12.0

## 10.11.9

### Patch Changes

-
- Updated dependencies
  - msun-lib-editor-transform@10.11.9

## 10.11.8

### Patch Changes

-
- Updated dependencies
  - msun-lib-editor-transform@10.11.8

## 10.11.7

### Patch Changes

-
- Updated dependencies
  - msun-lib-editor-transform@10.11.7

## 10.11.6

### Patch Changes

-
- Updated dependencies
  - msun-lib-editor-transform@10.11.6

## 10.10.17

### Patch Changes

-
- Updated dependencies

  - msun-lib-editor-transform@10.11.5

## 10.11.4

-
- Updated dependencies
  - msun-lib-editor-transform@10.10.16

## 10.10.15

### Patch Changes

-
- Updated dependencies
  - msun-lib-editor-transform@10.10.15

## 10.10.14

### Patch Changes

-
- Updated dependencies
  - msun-lib-editor-transform@10.10.14

## 10.10.13

### Patch Changes

-
- Updated dependencies
  - msun-lib-editor-transform@10.10.13

## 10.10.12

### Patch Changes

-
- Updated dependencies
  - msun-lib-editor-transform@10.10.12

## 10.10.11

### Patch Changes

-
- Updated dependencies
  - msun-lib-editor-transform@10.10.11

## 10.10.10

### Patch Changes

-
- Updated dependencies
  - msun-lib-editor-transform@10.10.10

## 10.10.9

### Patch Changes

-
- Updated dependencies
  - msun-lib-editor-transform@10.10.9

## 10.10.8

### Patch Changes

-
- Updated dependencies
  - msun-lib-editor-transform@10.10.8

## 10.10.7

### Patch Changes

-
- Updated dependencies
  - msun-lib-editor-transform@10.10.7

## 10.10.6

### Patch Changes

-
- Updated dependencies
  - msun-lib-editor-transform@10.10.6

## 10.9.27

### Patch Changes

-
- Updated dependencies
  - msun-lib-editor-transform@10.9.27

## 10.9.26

### Patch Changes

-
- Updated dependencies
  - msun-lib-editor-transform@10.10.5

## 10.9.26

### Patch Changes

-
- Updated dependencies
  - msun-lib-editor-transform@10.9.25

## 10.9.24

### Patch Changes

-
- Updated dependencies
  - msun-lib-editor-transform@10.9.24

### Patch Changes

-
- Updated dependencies
  - msun-lib-editor-transform@10.9.23

## 10.9.22

### Patch Changes

-
- Updated dependencies
  - msun-lib-editor-transform@10.9.22

## 10.9.21

-
- Updated dependencies
  - msun-lib-editor-transform@10.9.21

## 10.9.20

### Patch Changes

-
- Updated dependencies
  - msun-lib-editor-transform@10.9.20

## 10.9.19

### Patch Changes

-
- Updated dependencies
  - msun-lib-editor-transform@10.9.19

## 10.9.18

### Patch Changes

-
- Updated dependencies
  - msun-lib-editor-transform@10.9.18

## 10.9.17

### Patch Changes

-
- Updated dependencies
  - msun-lib-editor-transform@10.9.17

## 10.9.16

### Patch Changes

-
- Updated dependencies
  - msun-lib-editor-transform@10.9.16

## 10.9.15

### Patch Changes

-
- Updated dependencies
  - msun-lib-editor-transform@10.9.15

## 10.9.14

### Patch Changes

-
- Updated dependencies
  - msun-lib-editor-transform@10.9.14

## 10.9.13

### Patch Changes

-
- Updated dependencies
  - msun-lib-editor-transform@10.9.13

## 10.9.12

### Patch Changes

-
- Updated dependencies
  - msun-lib-editor-transform@10.9.12

## 10.8.23

### Patch Changes

-
- Updated dependencies

  - msun-lib-editor-transform@10.9.11
  - msun-lib-editor-transform@10.8.23

-
- Updated dependencies
  - msun-lib-editor-transform@10.8.22

## 10.8.21

### Patch Changes

-
- Updated dependencies
  - msun-lib-editor-transform@10.9.10

## 10.9.9

### Patch Changes

-
- Updated dependencies
  - msun-lib-editor-transform@10.9.9

## 10.9.8

### Patch Changes

-
- Updated dependencies
  - msun-lib-editor-transform@10.9.8

## 10.9.7

### Patch Changes

-
- Updated dependencies
  - msun-lib-editor-transform@10.9.7

## 10.8.21

### Patch Changes

-
- Updated dependencies
  - msun-lib-editor-transform@10.8.20

## 10.8.19

### Patch Changes

-
- Updated dependencies
  - msun-lib-editor-transform@10.8.19

## 10.8.18

### Patch Changes

-
- Updated dependencies
  - msun-lib-editor-transform@10.8.18

## 10.8.17

### Patch Changes

-
- Updated dependencies
  - msun-lib-editor-transform@10.8.17

## 10.8.16

### Patch Changes

-
- Updated dependencies
  - msun-lib-editor-transform@10.8.16

## 10.8.15

### Patch Changes

-
- Updated dependencies
  - msun-lib-editor-transform@10.8.15

## 10.8.14

### Patch Changes

-
- Updated dependencies
  - msun-lib-editor-transform@10.8.14

## 10.8.13

### Patch Changes

-
- Updated dependencies
  - msun-lib-editor-transform@10.8.13

## 10.8.12

### Patch Changes

-
- Updated dependencies
  - msun-lib-editor-transform@10.8.12

## 10.8.11

### Patch Changes

-
- Updated dependencies
  - msun-lib-editor-transform@10.8.11

## 10.8.10

### Patch Changes

-
- Updated dependencies
  - msun-lib-editor-transform@10.8.10

## 10.8.9

### Patch Changes

-
- Updated dependencies
  - msun-lib-editor-transform@10.8.9

## 10.8.8

### Patch Changes

-
- Updated dependencies
  - msun-lib-editor-transform@10.8.8

## 10.8.7

### Patch Changes

-
- Updated dependencies
  - msun-lib-editor-transform@10.8.7

## 10.7.12

### Patch Changes

-
- Updated dependencies
  - msun-lib-editor-transform@10.7.12

## 10.7.11

### Patch Changes

-
- Updated dependencies
  - msun-lib-editor-transform@10.7.11

## 10.7.10

### Patch Changes

-
- Updated dependencies
  - msun-lib-editor-transform@10.7.10

## 10.7.9

### Patch Changes

-
- Updated dependencies
  - msun-lib-editor-transform@10.7.9

## 10.6.12

### Patch Changes

-
- Updated dependencies
  - msun-lib-editor-transform@10.6.12

## 10.6.11

### Patch Changes

-
- Updated dependencies
  - msun-lib-editor-transform@10.6.11

## 10.6.10

### Patch Changes

-
- Updated dependencies
  - msun-lib-editor-transform@10.6.10

## 10.6.9

### Patch Changes

-
- Updated dependencies
  - msun-lib-editor-transform@10.6.9

## 10.6.8

### Patch Changes

-
- Updated dependencies
  - msun-lib-editor-transform@10.6.8

## 10.6.7

### Patch Changes

-
- Updated dependencies

  - msun-lib-editor-transform@10.6.7

## 10.6.6

- msun-lib-editor-transform@10.5.15

## 10.5.14

- Updated dependencies

  - msun-lib-editor-transform@10.6.6

## 10.6.5

### Patch Changes

-
- Updated dependencies
  - msun-lib-editor-transform@10.6.5

## 10.6.4

### Patch Changes

-
- Updated dependencies
  - # msun-lib-editor-transform@10.6.4
  - msun-lib-editor-transform@10.5.14

## 10.5.13

### Patch Changes

-
- Updated dependencies
  - msun-lib-editor-transform@10.5.13

## 10.5.12

### Patch Changes

-
- Updated dependencies
  - msun-lib-editor-transform@10.5.12

## 10.5.11

### Patch Changes

-
- Updated dependencies
  - msun-lib-editor-transform@10.5.11

## 10.5.10

### Patch Changes

-
- Updated dependencies
  - msun-lib-editor-transform@10.5.10

## 10.5.9

### Patch Changes

-
- Updated dependencies
  - msun-lib-editor-transform@10.5.9

## 10.5.8

### Patch Changes

-
- Updated dependencies
  - msun-lib-editor-transform@10.5.8

## 10.5.7

### Patch Changes

-
- Updated dependencies
  - msun-lib-editor-transform@10.5.7

## 10.5.6

### Patch Changes

-
- Updated dependencies
  - msun-lib-editor-transform@10.5.6

## 10.5.5

### Patch Changes

-
- Updated dependencies
  - msun-lib-editor-transform@10.5.5

## 10.4.25

### Patch Changes

- 表格撑满一页的问题
- Updated dependencies
  - msun-lib-editor-transform@10.4.25

## 10.4.24

### Patch Changes

- 边框问题
- Updated dependencies
  - msun-lib-editor-transform@10.4.24

## 10.4.23

### Patch Changes

- 表格相关
- Updated dependencies
  - msun-lib-editor-transform@10.4.23

## 10.4.22

### Patch Changes

- 时间框修改
- Updated dependencies
  - msun-lib-editor-transform@10.4.22

## 10.4.21

### Patch Changes

- 表格固定样式不允许双击表格线
- Updated dependencies
  - msun-lib-editor-transform@10.4.21

## 10.4.20

### Patch Changes

- 重新发版
- Updated dependencies
  - msun-lib-editor-transform@10.4.20

## 10.4.19

### Patch Changes

- contentborder 版本兼容
- Updated dependencies
  - msun-lib-editor-transform@10.4.19

## 10.4.18

### Patch Changes

- 边框修改
- Updated dependencies
  - msun-lib-editor-transform@10.4.18

## 10.4.17

### Patch Changes

- 表格属性问题修改
- Updated dependencies
  - msun-lib-editor-transform@10.4.17

## 10.4.16

### Patch Changes

- 新增 page 边框
- Updated dependencies
  - msun-lib-editor-transform@10.4.16

## 10.4.15

### Patch Changes

- 表格撑满整页修改
- Updated dependencies
  - msun-lib-editor-transform@10.4.15

## 10.4.14

### Patch Changes

- 优化代码
- Updated dependencies
  - msun-lib-editor-transform@10.4.14

## 10.4.13

### Patch Changes

- 最终发版
- Updated dependencies
  - msun-lib-editor-transform@10.4.13

## 10.4.12

### Patch Changes

-
- Updated dependencies
  - msun-lib-editor-transform@10.5.4

## 10.5.3

### Patch Changes

-
- Updated dependencies
  - msun-lib-editor-transform@10.5.3

## 10.5.2

### Patch Changes

-
- Updated dependencies
  - msun-lib-editor-transform@10.5.2

## 10.5.1

### Patch Changes

-
- Updated dependencies
  - msun-lib-editor-transform@10.5.1

## 10.5.0

### Minor Changes

-

### Patch Changes

- Updated dependencies
  - msun-lib-editor-transform@10.5.0

## 10.4.11

### Patch Changes

- trans 服务端带公式病历记录转换失败问题
- Updated dependencies
  - msun-lib-editor-transform@10.4.11

## 10.4.10

### Patch Changes

- 修复文本域自动化选区取消问题
- Updated dependencies
  - msun-lib-editor-transform@10.4.10

## 10.4.9

### Patch Changes

- 文本域自动化 bug 修改
- Updated dependencies
  - msun-lib-editor-transform@10.4.9

## 10.4.8

### Patch Changes

- 解决移动端滚动问题
- Updated dependencies
  - msun-lib-editor-transform@10.4.8

## 10.4.7

### Patch Changes

- 默认打印报错处理
- Updated dependencies
  - msun-lib-editor-transform@10.4.7

## 10.4.6

### Patch Changes

- 移动端缩放之后点击位置不准确的问题
- Updated dependencies
  - msun-lib-editor-transform@10.4.6

## 10.4.5

### Patch Changes

- a
- Updated dependencies
  - msun-lib-editor-transform@10.4.5

## 10.4.4

### Patch Changes

- y
- Updated dependencies
  - msun-lib-editor-transform@10.4.4

## 10.4.3

### Patch Changes

- o
- Updated dependencies
  - msun-lib-editor-transform@10.4.3

## 10.4.2

### Patch Changes

- design 文档设计器常用语检索字段时滚动条问题
- Updated dependencies
  - msun-lib-editor-transform@10.4.2

## 10.4.1

### Patch Changes

- a
- Updated dependencies
  - msun-lib-editor-transform@10.4.1

## 10.4.0

### Minor Changes

- 52 发版

### Patch Changes

- Updated dependencies
  - msun-lib-editor-transform@10.4.0

## 10.3.18

### Patch Changes

- msun-lib-editor-transform@10.3.18

## 10.3.17

### Patch Changes

- 死循环问题修改
- Updated dependencies
  - msun-lib-editor-transform@10.3.17

## 10.3.16

### Patch Changes

- 筏板错误修改
- Updated dependencies
  - msun-lib-editor-transform@10.3.16

## 10.3.15

### Patch Changes

- vant 修改
- Updated dependencies
  - msun-lib-editor-transform@10.3.15

## 10.3.14

### Patch Changes

- 测试
- Updated dependencies
  - msun-lib-editor-transform@10.3.14

## 10.3.13

### Patch Changes

- bug 测试
- Updated dependencies
  - msun-lib-editor-transform@10.3.13

## 10.3.12

### Patch Changes

- 双面打印报错处理
- Updated dependencies
  - msun-lib-editor-transform@10.3.12

## 10.3.11

### Patch Changes

- 表格自动替换排版问题
- Updated dependencies
  - msun-lib-editor-transform@10.3.11

## 10.3.10

### Patch Changes

- 重新发
- Updated dependencies
  - msun-lib-editor-transform@10.3.10

## 10.3.9

### Patch Changes

- tab 跳转兼容老数据
- Updated dependencies
  - msun-lib-editor-transform@10.3.9

## 10.3.8

### Patch Changes

- a
- Updated dependencies
  - msun-lib-editor-transform@10.3.8

## 10.3.7

### Patch Changes

- vant 相关
- Updated dependencies
  - msun-lib-editor-transform@10.3.7

## 10.3.6

### Patch Changes

- vant 测试
- Updated dependencies
  - msun-lib-editor-transform@10.3.6

## 10.3.5

### Patch Changes

- 重新发版
- Updated dependencies
  - msun-lib-editor-transform@10.3.5

## 10.3.4

### Patch Changes

- vant 降低版本
- Updated dependencies
  - msun-lib-editor-transform@10.3.4

## 10.3.3

### Patch Changes

- 文本域占位打印问题
- Updated dependencies
  - msun-lib-editor-transform@10.3.3

## 10.3.2

### Patch Changes

- 字段树检索卡顿问题优化
- Updated dependencies
  - msun-lib-editor-transform@10.3.2

## 10.3.1

### Patch Changes

- 代码走查后发版
- Updated dependencies
  - msun-lib-editor-transform@10.3.1

## 10.3.0

### Minor Changes

- 51 第一次发版

### Patch Changes

- Updated dependencies
  - msun-lib-editor-transform@10.3.0

## 10.2.27

### Patch Changes

- 文本域下拉列表确定按钮位置调整
- Updated dependencies
  - msun-lib-editor-transform@10.2.27

## 10.2.26

### Patch Changes

- 双面打印报错处理
- Updated dependencies
  - msun-lib-editor-transform@10.2.26

## 10.1.32

### Patch Changes

- 双面打印报错解决
- Updated dependencies
  - msun-lib-editor-transform@10.1.32

## 10.1.31

### Patch Changes

- 解决分组上边的空行
- Updated dependencies
  - msun-lib-editor-transform@10.2.25

## 10.2.24

### Patch Changes

- 1
- Updated dependencies
  - msun-lib-editor-transform@10.2.24

## 10.2.23

### Patch Changes

- 中草药修改
- Updated dependencies
  - msun-lib-editor-transform@10.2.23

## 10.2.22

### Patch Changes

- a
- Updated dependencies
  - msun-lib-editor-transform@10.2.22

## 10.2.21

### Patch Changes

- a
- Updated dependencies
  - msun-lib-editor-transform@10.2.21

## 10.2.20

### Patch Changes

- a
- Updated dependencies
  - msun-lib-editor-transform@10.2.20

## 10.2.19

### Patch Changes

- 发版测试
- Updated dependencies
  - msun-lib-editor-transform@10.2.19

## 10.2.18

### Patch Changes

- 发版测试
- Updated dependencies
  - msun-lib-editor-transform@10.2.18

## 10.2.17

### Patch Changes

- exports 问题解决
- Updated dependencies
  - msun-lib-editor-transform@10.2.17

## 10.2.16

### Patch Changes

- base 还原
- Updated dependencies
  - msun-lib-editor-transform@10.2.16

## 10.2.15

### Patch Changes

- base 增加构建 cjs
- Updated dependencies
  - msun-lib-editor-transform@10.2.15

## 10.2.14

### Patch Changes

- 表格自动填充增加文本域自动填充图片
- Updated dependencies
  - msun-lib-editor-transform@10.2.14

## 10.2.13

### Patch Changes

- 一些 bug 解决
- Updated dependencies
  - msun-lib-editor-transform@10.2.13

## 10.2.12

### Patch Changes

- 表单模式下，上下键进入表格光标位置不正确问题及解决
- Updated dependencies
  - msun-lib-editor-transform@10.2.12

## 10.2.11

### Patch Changes

- 快速录入模式和表格跳转冲突问题解决
- Updated dependencies
  - msun-lib-editor-transform@10.2.11

## 10.2.10

### Patch Changes

- skipmode 保存相关
- Updated dependencies
  - msun-lib-editor-transform@10.2.10

## 10.2.9

### Patch Changes

- tab 键相关功能
- Updated dependencies
  - msun-lib-editor-transform@10.2.9

## 10.2.8

### Patch Changes

- 点击图片出发 contentchanged 的 bug 修改
- Updated dependencies
  - msun-lib-editor-transform@10.2.8

## 10.2.7

### Patch Changes

- 修改说明图标颜色
- Updated dependencies
  - msun-lib-editor-transform@10.2.7

## 10.2.6

### Patch Changes

- 50 发版测试
- Updated dependencies
  - msun-lib-editor-transform@10.2.6

## 10.2.5

### Patch Changes

- 50 编译 bug
- Updated dependencies
  - msun-lib-editor-transform@10.2.5

## 10.2.4

### Patch Changes

- exports 导致的发版错误修改
- Updated dependencies
  - msun-lib-editor-transform@10.2.4

## 10.2.3

### Patch Changes

- modelPath 没有转 paraPath 导致的 tab 键跳转报错处理
- Updated dependencies
  - msun-lib-editor-transform@10.2.3

## 10.2.2

### Patch Changes

- 50 发版
- Updated dependencies
  - msun-lib-editor-transform@10.2.2

## 10.2.1

### Patch Changes

- 50 发版
- 50 发版
- Updated dependencies
- Updated dependencies
  - msun-lib-editor-transform@10.2.1

## 10.2.0

### Minor Changes

- 50 版本发版

### Patch Changes

- Updated dependencies
  - msun-lib-editor-transform@10.2.0

## 10.1.27

### Patch Changes

- design 被误还原代码修正
- Updated dependencies
  - msun-lib-editor-transform@10.1.27

## 10.1.26

### Patch Changes

- design 相关
- Updated dependencies
  - msun-lib-editor-transform@10.1.26

## 10.1.25

### Patch Changes

- 痕迹对比修复
- Updated dependencies
  - msun-lib-editor-transform@10.1.25

## 10.1.24

### Patch Changes

- 解决开拼音之后插入红色文字再改回黑色，输入中文为红色的问题
- Updated dependencies
  - msun-lib-editor-transform@10.1.24

## 10.1.23

### Patch Changes

- 智能模版引用报错
- Updated dependencies
  - msun-lib-editor-transform@10.1.23

## 10.1.22

### Patch Changes

- 四个一行，放不下整体往下移动
- Updated dependencies
  - msun-lib-editor-transform@10.1.22

## 10.1.21

### Patch Changes

- 分组痕迹对比
- Updated dependencies
  - msun-lib-editor-transform@10.1.21

## 10.1.20

### Patch Changes

- design 将 vue 打到包里
- Updated dependencies
  - msun-lib-editor-transform@10.1.20

## 10.1.19

### Patch Changes

- 发版测试
- Updated dependencies
  - msun-lib-editor-transform@10.1.19

## 10.1.18

### Patch Changes

- 干掉 vue 的 debgger
  - msun-lib-editor-transform@10.1.18

## 10.1.17

### Patch Changes

- 去掉 Babel/preser-env 的尖括号
- Updated dependencies
  - msun-lib-editor-transform@10.1.17

## 10.1.16

### Patch Changes

- 分组痕迹对比两个分组都不存在的情况处理
- Updated dependencies
  - msun-lib-editor-transform@10.1.16

## 10.1.15

### Patch Changes

- a
- Updated dependencies
  - msun-lib-editor-transform@10.1.15

## 10.1.14

### Patch Changes

- 四个一行增加返回值
  - msun-lib-editor-transform@10.1.14

## 10.1.13

### Patch Changes

- a
- Updated dependencies
  - msun-lib-editor-transform@10.1.13

## 10.1.12

### Patch Changes

- a
- Updated dependencies
  - msun-lib-editor-transform@10.1.12

## 10.1.11

### Patch Changes

- a
- Updated dependencies
  - msun-lib-editor-transform@10.1.11

## 10.1.10

### Patch Changes

- 增加了插入页眉页脚模版接口
- Updated dependencies
  - msun-lib-editor-transform@10.1.10

## 10.1.9

### Patch Changes

- 暴露初始化数据集选项接口
- Updated dependencies
  - msun-lib-editor-transform@10.1.9

## 10.1.8

### Patch Changes

- msun-lib-editor-transform@10.1.8

## 10.1.7

### Patch Changes

- 保存数据错误解决
- Updated dependencies
  - msun-lib-editor-transform@10.1.7

## 10.1.6

### Patch Changes

- 发版测试
- Updated dependencies
  - msun-lib-editor-transform@10.1.6

## 10.1.5

### Patch Changes

- 发版 bug 修改
- Updated dependencies
  - msun-lib-editor-transform@10.1.5

## 10.1.4

### Patch Changes

- 自定义批注引用接口，自定义大小存储再打开配置重载报错
- Updated dependencies
  - msun-lib-editor-transform@10.1.4

## 10.1.3

### Patch Changes

- 相同的图片进入痕迹对比的 bug 修改
- Updated dependencies
  - msun-lib-editor-transform@10.1.3

## 10.1.2

### Patch Changes

- Updated dependencies
  - msun-lib-editor-transform@10.1.2

## 10.1.1

### Patch Changes

- 去掉了 js-base64 和 pako
- Updated dependencies
  - msun-lib-editor-transform@10.1.1

## 10.1.0

### Minor Changes

- 49 第一次发版

### Patch Changes

- Updated dependencies
  - msun-lib-editor-transform@10.1.0

## 10.0.20

### Patch Changes

- msun-lib-editor-transform@10.0.20

## 10.0.19

### Patch Changes

- 去掉所有尖括号
- Updated dependencies
  - msun-lib-editor-transform@10.0.19

## 10.0.18

### Patch Changes

- 去掉@types/pako 的尖括号
- Updated dependencies
  - msun-lib-editor-transform@10.0.18

## 10.0.17

### Patch Changes

- Updated dependencies
  - msun-lib-editor-transform@10.0.17

## 10.0.16

### Patch Changes

- 修改 calss 名
- Updated dependencies
  - msun-lib-editor-transform@10.0.16

## 10.0.15

### Patch Changes

- 修改 class 名
- Updated dependencies
  - msun-lib-editor-transform@10.0.15

## 10.0.14

### Patch Changes

- 解决没有 testrawdata vue 上展示数据报错的问题
- Updated dependencies
  - msun-lib-editor-transform@10.0.14

## 10.0.13

### Patch Changes

- msun-lib-editor-transform@10.0.13

## 10.0.12

### Patch Changes

- msun-lib-editor-transform@10.0.12

## 10.0.11

### Patch Changes

- msun-lib-editor-transform@10.0.11

## 10.0.10

### Patch Changes

- Updated dependencies
  - msun-lib-editor-transform@10.0.10

## 10.0.9

### Patch Changes

- Updated dependencies
  - msun-lib-editor-transform@10.0.9

## 10.0.8

### Patch Changes

- transform 不使用 editor.raw 使用传入的 rawData
- Updated dependencies
  - msun-lib-editor-transform@10.0.8

## 10.0.7

### Patch Changes

- 10.0.7 发版测试
- Updated dependencies
  - msun-lib-editor-transform@10.0.7

## 10.0.6

### Patch Changes

- 全部加上 exports
- Updated dependencies
  - msun-lib-editor-transform@10.0.6

## 10.0.5

### Patch Changes

- 去掉所有的 exports 给 pacs 用
- Updated dependencies
  - msun-lib-editor-transform@10.0.5

## 10.0.4

### Patch Changes

- Updated dependencies
  - msun-lib-editor-transform@10.0.4

## 10.0.3

### Patch Changes

- 选区删除 bug 需修改
- Updated dependencies
  - msun-lib-editor-transform@10.0.3

## 10.0.2

### Patch Changes

- 升级版本号
- Updated dependencies
  - msun-lib-editor-transform@10.0.2

## 10.0.1

### Patch Changes

- 48 版本新代码发版
- Updated dependencies
  - msun-lib-editor-transform@10.0.1

## 10.0.1

## 2.32.25

### Patch Changes

- 升级一下版本号

## 2.32.24

### Patch Changes

- 选区删除 bug 修改

## 2.32.23

### Patch Changes

- a
- Updated dependencies
  - # msun-lib-editor-transform@10.0.1

## 2.32.22

### Patch Changes

- 重新发版

## 2.32.21

### Patch Changes

- 重置参数位置和作用范围修改
  > > > > > > > release-V1.47.0

## 2.32.20

### Patch Changes

- a

## 2.32.19

### Patch Changes

- a

## 2.32.18

### Patch Changes

- a

## 2.32.16

### Patch Changes

- 光标不存在报错问题解决

## 2.32.15

### Patch Changes

- 升级版本

## 2.32.14

### Patch Changes

- 升级版本

## 2.32.13

### Patch Changes

- 自定义批注修改

## 2.32.12

### Patch Changes

- a

## 2.32.11

### Patch Changes

- 修复文本域最大值最小值设置问题，半个文本域自动修复

## 2.32.10

### Patch Changes

- 多选框选中颜色变化

## 2.32.8

### Patch Changes

- a

## 2.32.8

### Patch Changes

- 5

## 2.32.7

### Patch Changes

- a

## 2.32.6

### Patch Changes

- 1

## 2.32.5

### Patch Changes

- 解决了无边框模式

## 2.32.4

### Patch Changes

- a

## 2.32.3

### Patch Changes

- a

## 2.31.25

### Patch Changes

- 回推测试

## 2.31.24

### Patch Changes

- a

## 2.31.23

### Patch Changes

- 啊啊

## 2.31.22

### Patch Changes

- 修复横纵向问题

## 2.31.21

### Patch Changes

- 评定量表模板功能

## 2.31.20

### Patch Changes

- 撤回浏览器缩放比例相关代码

## 2.31.19

### Patch Changes

- 锚点文本域的若干修改，代码走查的错误修改

## 2.31.18

### Patch Changes

- 1.图片编辑的拖杆修改 2.修复文本域校验

## 2.31.17

### Patch Changes

- 1

## 2.31.16

### Patch Changes

- 1

## 2.31.15

### Patch Changes

- 尝试发一版新版批注的

## 2.31.14

### Patch Changes

- 1.段落重排 2.文本域时间计算公式 3.自定义页面默认大小

## 2.31.13

### Patch Changes

- 正序逆序保存问题修改以及单元格背景颜色问题修改

## 2.31.12

### Patch Changes

- 段落重排若干问题解决

## 2.31.11

### Patch Changes

- 更新了一个参数

## 2.31.10

### Patch Changes

- 更新了 c++打印的线条样式
  `2023-11-21`
- 行线 rowLineTypeExplain-增加配置,控制表格里边是否绘制行线

## 2.30.15

## 2.31.9

### Patch Changes

- a

## 2.31.8

### Patch Changes

- a

## 2.31.7

### Patch Changes

- 啊
- 啊

## 2.31.6

### Patch Changes

- a

## 2.31.5

### Patch Changes

- a

## 2.31.4

### Patch Changes

- sad

## 2.31.3

### Patch Changes

- 去掉 exports 之后的发版测试

## 2.31.2

### Patch Changes

-

## 2.31.1

### Patch Changes

- 46 monorepo 第一次发版
  `2023-11-20`
- 适配 vue

## 2.31.0

`2023-11-16`

- 46 分支发版

#

`2023-11-20`

- 适配 vue

## 2.30.13

`2023-11-10`

- 行线-默认不绘制没有 row 的那半页

## 2.30.12

## 2.30.13

### Patch Changes

- 11.16
  `2023-11-10`
- 修复有序列表从第一个选项开始回车汉字会变数字的问题

## 2.30.11

`2023-11-09`

- 修复分组表单模式不正确的问题
- 修复分组表单模式拖动一次之后变为表单模式的问题
- 数据存储增加 listNumStyle
- 有序列表支持汉字数字保存
- 有序列表-修改 changeListNumStyle 只改当前列表的段落,不再修改全部段落的 listNumStyle 属性
- 行线-修复最后一个单元格不绘制行线的问题

## 2.30.9

`2023-11-06`

- 修复上中下对齐打印上下标位置不正确的问题

## 2.30.8

`2023-11-05`

- bug 修改

## 2.30.7

`2023-11-03`

- 滚动条上面的三角恢复
- 将 internal 里边的部分方法挪到 editor 上,editor 上调用不对的加上 internal
- 修复上标下标打印位置错乱的问题
- 修复文本域边框打印位置不正确的问题

## 2.30.6

`2023-11-02`

- 浮动模型-保存数据和回显
- 修复痕迹对比图片删除线不正确的问题
- 修复文本域公式提示错误信息的问题
- 浮动模型-解决插入表格不绘制表格线的问题
- 浮动模型-解决点击在挨着线的地方,没在模型内时,报错的问题
- 浮动模型-解决点击表格光标在表格外部的问题
- 修复文本域公式异常提示报错信息的问题
- 文档开头空行，光标在开头按 delete 键删除，按两次才能删除空行，并且后面如果是文本域，按 delete 键再回车控制台报错修改
- 解决新版表格拆分不正确的问题,导致点击第二页就报错
- 新增文本域打印边框功能
- 滚动条样式改变
- 修复新版表格拆分,最后一列无限软分割报错的情况
- 新增文本域对齐方式功能
- 浮动模型-增加切换浮动模型模式的接口
- 锚点文本域（试验）

## 2.30.1

`2023-10-23`

- 修复 this.editor.internal.getShapes 问题

## 2.30.0

`2023-10-16`

- 升级版本号

## 2.29.16

`2023-10-16`

- 修复痕迹视图新增与删除线样式不正确的问题

## 2.29.15

`2023-10-16`

- 增加修改开始页数的接口
- 新增水平线显示折线功能
- 增加对分组异常数据的处理
- 修改痕迹视图新增与删除线样式调整
- 浏览器缩放比例之后视口中页面绘制不全的问题修复

## 2.29.23

`2023-11-03`

- pdf 上下标

## 2.29.21

`2023-10-27`

- 服务端调用 localStorage 报错

## 2.29.20

`2023-10-25`

- 适配 vue

## 2.29.19

`2023-10-20`

- 修改行线的判断,由不等于 undefined 或者 void 改为 相等于 solid
- 解决绑定了 beforePaste 事件,粘贴图片为图片未加载的问题

## 2.29.18

`2023-10-16`

- 和 45 进行分割的一个版本

## 2.29.17

`2023-10-16`

- 绘制表格内的行线时,增加 rowLineType 是否为 undefined 的判断

## 2.29.14

`2023-10-12`

- 将计算最多的行高计算放到 update 里边减少调用,同时避免因为 internal 里边值为 0,而导致服务端出现死循环
- 增加 internal.rowHeight 为假时的判断,避免死循环

## 2.29.13

`2023-10-12`

- 增加服务端接口暴露
- 修改行线绘制,没有内容的半页绘制行线依赖的高度问题

## 2.29.12

`2023-10-11`

- 修改是否在视口内的判断,去掉 editor.viewScale \* window.devicePixelRatio,解决打印预览分页处,缺少内容的问题

## 2.29.11

`2023-10-10`

- 血透需求 appendDataToTable 增加支持传入表格数组
- 去掉无用属性
- 去掉 EditorConfig.ts 文件中的 originRowLineType 配置,改为只读取 internal.originConfigItems 里边的

## 2.29.10

`2023-10-10`

- 修改切换病历时,行线显示与否状态混乱的问题

## 2.29.9

`2023-10-10`

- 改了一下新批注的判定范围
- 方法报错处理
- 页脚表格删除排版报错修改
- 去除字体列表中的微软雅黑
- 字体列表只保留"宋体", "黑体", "幼圆", "楷体", "隶书", "仿宋"

## 2.29.8

`2023-10-8`

- vue 老版本批注添加报错解决

## 2.29.7

`2023-10-8`

- 优化 button 居上的距离
- 部分样式调整，批注增加了从上往下弹的逻辑，增加了点击别处批注框取消高亮的功能
- 鼠标移动到批注关闭按钮时样式变化

## 2.29.6

`2023-10-7`

- 添加 font

## 2.29.5

`2023-10-7`

- vue 双击批注打开编辑弹窗的联动

## 2.29.4

`2023-10-7`

- 将隔离批注的 transUse 改为 useLocal,解决了范围重复时页面滚动条不能拖动的问题
- 修改模式切换问题

## 2.29.3

`2023-10-7`

- 字符和数字排版问题修复
- 修复模式切换不生效的问题
- 修复单选复选框打印上移的问题
- 1.添加批注之后高亮并且批注滚动到可视范围 2.快速拖动滚动条时，批注晃动的问题解决 3.鼠标样式问题解决

## 2.29.2

`2023-10-7`

- 解决文本域边框行内对齐不生效的问题
- 增加了批注列表的关闭按钮
- 阻止点击评论时的正文选区行为
- 批注滚动条完善

## 2.29.1

`2023-9-27`

- 解决粘贴一个表格,全选只有一个表格的数据中进行粘贴报错的 bug
- 修改表格线的绘制位置,解决某些情况下会穿过字的问题和左右边距不准确的问题
- 解决切换横纵向,没有滚动条的问题
- 新增文字行内上中下对齐
- 修复行内上中下排版位置不准确的问题
- 修改不允许行首行尾出现的字符配置到 config 中
- 新增图片单选复选框水平线等行内上中下对齐
- 新增批注相关的逻辑（包括批注的滚动）
- 修复特殊字符 pdf 预览不显示的问题

## 2.29.0

`2023-9-25`

- rowLine 绘制时有半页的情况下,将没有数据的部分补全行线
- 打印增加没有 row 的时候绘制行线,续打暂时先不改\
- 新增调试工具改变级联三角标识宽度的功能
- 文本域无边框模式 box 和 label 类型文本域为空时不自动替换背景文本为空
- 文本域无边框模式 box 和 label 类型文本域为空时不自动替换背景文本为 kong
- 解决拖动表格竖线导致居中对齐的单元格错乱的问题
- 代码位置错误调整
- 修复设置列表后段落如果是居中对齐或右对齐，打印效果不一致的问题及文本域级联方块宽度修改

## 2.28.11

`2023-10-16`

- 区分 44 分支,让行线在该分支上不生效

## 2.28.9

`2023-9-19`

- 修改表单模式下页眉页脚中没有文本域仍然能进入编辑模式的 bug

## 2.28.8

`2023-9-18`

- vue 改动

## 2.28.7

`2023-9-18`

- 修复光标在文本域中滚动条无法生效的问题

## 2.28.6

- 解决表单模式下删除报错的问题

## 2.28.5

`2023-9-14`

- 设置文本域字体缩放,空文本域的时候增加是否包含边框的判断

## 2.28.4

`2023-9-14`

- 适配 vue

## 2.28.3

`2023-9-14`

- 增加过渡期新逻辑开关
- 解决表格透明度的线绘制不正确的问题,增加二维数组去重的公共方法,优化新版表格拆分全是透明度线的大表格的性能
- 修复文本域边框为画线时，存在背景文本打印时缺失背景文本的宽度的问题
- 修复新版表格竖线透明度绘制不准的问题
- 调整测试按钮使用方式
- 补充还原按钮
- 解决是否在同一分组内的判断不准的问题,因为表格内的段落没有分组 id,分组 id 只在表格和外层的段落上
- 解决拖拽到下一行开头报错的问题

## 2.28.2

`2023-9-11`

- 修复插入线圈插等状态下可以点击其他图形的问题
- 设置字体缩放的方法 setCharacterSize 上增加装饰器也往历史堆栈中记录,触发 contentChanged 方法

## 2.28.1

`2023-9-7`

- 优化水印文字位置不准确及开启只读模式后不能修改水印文字的问题
- 修复绘制折线虚线位置不正确的问题
- 修复水印字体改变大小不生效的问题
- 修复水印文字滚动后位置不正确的问题
- 新增图形模式撤销后取消绘制状态及新增继续绘制折线首次点击选中的功能

## 2.28.0

`2023-9-5`

- 表单模式下,只选择一个表格内的单元格时,可编辑表格内容删除会清掉选择的单元格里边的内容
- 表格分页时,开启绘制行线报错的问题修复
- 将 rowLineType 保存到数据中,show_header_line 删除掉在 Header.ts 中的代码
- 修改表单模式下,可编辑表格删除的问题,修改是否在同一个段落的判断
- 批注替换的 bug 修改
- 解决表格外没有文本域,表单模式下选区表格内外删除的问题
- 新增 command undo redo 指令

## 2.27.22

`2023-9-4`

- 增加 toggleRowLineType 方法

## 2.27.21

`2023-9-1`

- 自定义批注背景颜色默认透明

## 2.27.20

`2023-8-31`

- 修改 rowLineType solid 的字母大小写
- 表格内根据 rowLine 和单元格下方线是否有透明度绘制行线
- 打印增加单元格内的行线

## 2.27.5

`2023-8-24`

- 修改行间距改变的时候,文本域边框位置不对的问题
- reInitRaw 内存问题
- 自定义批注和批注的接口合并

## 2.27.4

`2023-8-23`

- 表格选区性能优化
- 新版表格拆分某些情况下,硬分割的时候,有些下一页单元格分割 row_size 计算不准的问题修复
- 修复列表序号同一段多行时重复打印问题
- 修改能拖动表格线时,鼠标距离表格线的距离,避免单元格密集的时候,一直是拖线的样式,点不到单元格里边
- 修改拖动表格线的灵敏度
- 表格批注无法替换的 bug 修改

## 2.27.3

`2023-8-18`

- 发版测试

## 2.27.2

`2023-8-18`

- 插入分页符报错问题
- 新版表格拆分初步实现固定表头
- 新版表格拆分,带有透明度的线的初步处理
- 修改新版表格拆分线的透明度不准的问题
- 文本域无边框模式删除时报错问题
- 修复页眉激活状态下第一行是表格的正文右侧报错的问题
- 修复文本域公式小数点计算错误问题
- 新版表格拆分,用 slice 代替 shift 优化性能

## 2.27.1

`2023-8-16`

- 文本域显示模式光标经过背景色显示增加嵌套文本域背景色深浅变化
- 优化 shape
- 文本域显示模式光标经过背景色显示增加嵌套文本域背景色深浅变化
- 补充文本域展示模式的配置说明
- 梳理新版表格拆分,解决继续往下拆分报错,点击报错的问题
- 新增折线点击加入圈叉功能
- 新增折线跟随段落位移的功能
- 新版表格拆分完善,解决表格内单元格顺序不对的问题
- 图形模式开启式只读，继续绘制折线时只需要焦点的折线能继续绘制
- 修改图形模式颜色
- 修复绘制折线圆被线遮住的问题
- 修复插入折线时可以点击编辑的问题
- C++新增打印折线功能
- 增加多选框鼠标经过颜色判断
- 解决新版表格拆分 rowsize 计算不对的问题
- 修复切换文本域边框模式文本域公式图标不显示的问题

## 2.27.0

`2023-8-11`

- 新版本使用新版隐藏文本域边框逻辑
- 自定义批注相关
- 文本域边框绘制模式
- 表格批注不能替换的 bug
- 新增划线功能
- 新增折线编辑
- 新增折线拖动功能
- 文本域增加 get 属性 editable;增加新的文本域展示模式
- 修复折线不准确的问题
- 新增继续画折线功能，修复绘制 shape 空白的问题
- 解决页脚表格上下居中对齐不生效的问题
- 文本域增加显示模式

## 2.26.7

`2023-8-9`

- 新版表格拆分整个表格被拽到下一页去的情况处理
- 绘制折线及修复圆和叉不跟着段落走的问题

## 2.26.6

`2023-8-7`

- 单个单元格拆分撤销问题
- 修改单元格内斜线可以设置可以取消,斜上的跟斜下的可以同时存在,但是同类型的只能有一种

## 2.26.5

`2023-8-4`

- 分割单个单元格 bug 修改
- 软分割的时候处理合并单元格不绘制的线
- 选中整个表格在锁定分组只读模式,表单模式下不允许剪切删除
- 将 Ctrl+V 的判断挪到 paste 里边去
- 解决新版表格拆分一个单元格无限软分割的问题
- 修复 shape 报错问题

## 2.26.4

`2023-8-2`

- 增加拆分单个单元格的功能
- 解决编辑器克隆接口内存泄漏问题及性能优化
- 新版表格拆分基础班软分割逻辑完成
- 新版表格拆分硬分割时,row_size 不对的问题修复
- 修复敏感词检测返回敏感词数量跟真实存在的敏感词数量不一致的问题

## 2.26.3

`2023-7-31`

- 调整用户登录接口
- 新增文本域画线边框

## 2.26.2

`2023-7-28`

- 修复 shape 点击编辑角拉长后保存位置不正确的问题
- 新增 C++打印 shape 的画圈和画 X

## 2.26.1

`2023-7-26`

- 新增 shape 删除按钮
- 新版表格拆分,只有硬分割的情况处理

## 2.26.0

`2023-7-26`

- 页眉页脚替换内容报错
- 新增 c++打印 shape 画圈
- 编辑器设置为 display:none 时仍然会调动定时器不停调用 render 影响其他编辑器性能问题
- 处于选区状态时不再重复调用 render
- 根据 placeholder 获取文本域,可以指定文本域类型

## 2.25.23

`2023-8-2`

- 选中整个表格在锁定分组只读模式,表单模式下不允许剪切删除
- 将 Ctrl+V 的判断挪到 paste 里边去

## 2.25.22

`2023-7-28`

- 分组表单模式影响痕迹视图用户名展示问题

## 2.25.21

`2023-7-25`

- shape 绘制椭圆及叉提交
- 修复 shape 无法保存的问题
- 增加依赖分析工具
- shape 画组合线功能提交
- 修复 shape 判断问题
- 开启压缩配置后带有图片的数据越压越大的问题
- 开启压缩配置后带有图片的数据越压越大的问题
- 禁止编辑的图片不能拖动缩放大小
- 开启压缩配置后带有图片的数据越压越大的问题
- 修复 shape 画线无法更新的问题
- 修复 shape 更新位置不正确的问题
- 新增文本域画线功能
- 修复 shape 画线绘制多条的问题

## 2.25.20

`2023-7-14`

- 修复文本域公式不能二次修改 bug
- 都昌模板转换增加一些判断防止报错
- 修复文本域公式点击提示纯数字的问题

## 2.25.19

`2023-7-14`

- 修复文本域公式能打字的问题

## 2.25.18

`2023-7-14`

- 修复文本域公式能打字的问题

## 2.25.17

`2023-7-14`

- 修复文本域公式能打字的问题

## 2.25.16

`2023-7-14`

- 级联文本域被控制的文本域消失影响主文本域消失的 bug 修复,给个提示
- 选区删除错误修复

## 2.25.15

`2023-7-11`

- 增加内容改变事件耗时监控
- 文本域公式计算更新值逻辑优化
- 修改 Helper.ts 文件中 updateFieldTextHelper_RootCell 方法里边调用 insertText 改为 EditorHelper.insertText 调用,避免 updateFieldText 调用时,contentChanged 里边调用 updateFieldsFormula 引起装饰器调用,插入文内容,但是没有 update 导致 row 的下标不对,引起报错的 bug
- 修复嵌套文本域点击不上的问题

## 2.25.14

`2023-7-11`

- 文本域级联 field 的 meta 放入复制

## 2.25.13

`2023-7-10`

- 公式计算死循环
- 中文输入确认后,增加 contentChanged 函数调用,粘贴里边改为 EditorHelper 调用,减少 contentCHnaged 执行次数
- 报错后不再触发内容改变事件问题重新调整
- 添加提示限制

## 2.25.12

`2023-7-7`

- 打印数据生成时如果图片缺少 src 则会报错的问题
- 表单模式下，选区删除文本域内容导致文本域被清空的 bug 修改
- (之前发版一直报错，前几版都发失败了，2.25.11 是最近的一版)

## 2.25.7

- contentChanged 事件中报错一次导致以后不会再触发该事件的 bug
- 分组删除之后获取路径报错

## 2.25.6

`2023-7-5`

- focusElement 增加 page
- 修改装饰器的 ts 类型描述
- 根据字符串关键字坐标高亮,增加参数的 ts 类型
- 文本域公式逻辑从 pointerDown 挪到 pointerUp

## 2.25.5

`2023-7-4`

- 新增根据字符串下标高亮的方法
- 增加 monitor 打印
- 增加片段逻辑执行耗时打印
- 生僻字支持，暂先试用本地测试

## 2.25.4

`2023-6-30`

- 新增文本域共识模式开启时显示提示图标
- 新增文本域公式事件

## 2.25.3

`2023-6-28`

- 若干痕迹对比的弹窗处理

## 2.25.2

`2023-6-27`

- 修复文本域公式以；分割的多个公式不生效的问题

## 2.25.1

`2023-6-26`

- 优化更新文本域公式的方法
- 增加了数字类型位数无限制
- 修改了小数四舍五入的规则
- 新增判断大小文本域公式逻辑

## 2.25.0

`2023-6-20`

- 40 迭代需求

## 2.24.10

`2023-7-10`

- 表单模式下选区删除导致文本域清空的补丁

## 2.24.9

`2023-7-5`

- 解决监听 beforePaste 事件,使用 copyEditor,drawScrollBar 的时候,copyEditor 里边的 canvas 没有父级容器导致报错的 bug

## 2.24.8

`2023-7-5`

- 新增 getPrintEditor,initEditor 方法,让打印和隐藏的 editor 区分开,解决某些配置不对的问题
- 修正 getPrintEditor 里边初始化 editor 的方法调用,增加 iamgeMap 的赋值

## 2.24.7

`2023-7-3`

- 增加 internal 属性,transformData,在每次执行 rawData2ModelData 的时候,收集图片和文本域数据
- beforePaste 事件,返回数据改为对象,增加返回数据转换后的 transformData

## 2.24.6

`2023-6-27`

- 表单模式下选区删除错误修改
- 都昌模板转换图片大小问题修复
- 选择框点击文本选中时触发选区问题
- 增加路径有效性判断,解决 refreshDocument 强制更新时,数据变化导致路径转换时报错的问题

## 2.24.5

`2023-6-26`

- 增加图片信息异常提示
- 单元格复制样式问题
- shift 选区 bug 修改
- pdf 打印将图片转白色背景时直接使用 canvas
- 首行缩进和 tab 键缩进，在选区包含表格的情况下报错处理

## 2.24.4

- 选区单元格删除不能删除掉单元格中内容

## 2.24.3

`2023-6-20`

- 去掉了一个选区删除的判断
- 还原 ts 配置信息
- 解决调用 updateFieldText 找错文本域的 bug
- 解决修改字体样式,比如改成上下标之后调用取消高亮,光标绘制的位置不对的 bug
- 去掉表单模式下删除逻辑中加的本地测试
- 表单模式下，选区有表格的情况下删除 不能撤销的 bug 修改

## 2.24.2

`2023-6-19`

- 模板转换报错
- 显示隐藏按钮不生效问题
- 解决单元格软分割时,段落 children 为空,导致获取字符串位置为空的问题
- 去掉了一个选区删除的判断
- 解决调用 updateFieldText 找错文本域的 bug

## 2.24.1

`2023-6-14`

- shift 添加选区功能
- 切换构建工具为 vite
- ts 报错问题
- 使用 vite 后不能热更新的问题
- 使用 useLetterPlaceholder 属性将输入法输入时字母占位的新旧功能进行了隔离
- XField.ts 中,定位到文本域的方法,加是否在正文是否是编辑页眉页脚模式的判断,解决循环全文档文本域调用 editor.reviseFieldAttr 报错的问题

## 2.24.0

`2023-6-13`

- 39 迭代需求

## 2.23.22

`2023-6-15`

- 解决单元格软分割时,段落 children 为空,导致获取字符串位置为空的问题

## 2.23.21

`2023-6-13`

- 修改获取字符串位置,字符串在分页表格中位置不对的 bug
- 修改获取字符串位置中表格内的数据页码不对的 bug

## 2.23.20

`2023-6-12`

- 获取字符串相对于页面位置中干掉了测试的值
- 获取字符串相对位置乘以 0.749
- 修复获取字符串相对位置里边获取最后一个字符下标不对的问题

## 2.23.19

`2023-6-12`

- 修改获取字符串位置,改为获取最后一个字符的位置

## 2.23.18

`2023-6-12`

- 只有一个表格的模板打开报错问题

## 2.23.17

`2023-6-9`

- 绘制表格线时模糊
- 都昌分组数据转换判断写错了
- 增加分组转换，不限制是否为服务端

## 2.23.16

`2023-6-7`

- 修复水印位置不准确的问题

## 2.23.15

`2023-6-7`

- 增加获取字符串开头相对于当前页左下角的位置
- 修复获取字符串相对于页面左下角位置只能获取一个位置的问题
- 增加表格内和文本域内不能插入排版的判断条件

## 2.23.14

`2023-6-6`

- 修复滚动表格拉到最后删除滚动不正确的问题
- 还原 set_cell_height 命名

## 2.23.13

`2023-6-5`

- 服务端调用接口无需受只读模式影响及转打印数据时判断错误

## 2.23.12

`2023-6-5`

- 服务端调用接口无需受只读模式影响

## 2.23.11

`2023-6-5`

- 都昌模板转换图片数据增加判断

## 2.23.10

`2023-6-2`

- 修复绘制线条编辑框不显示的问题
- 增加部分接口性能监测
- 仅选择模式单独设置成了一个属性
- 新增图形模式
- 修复表格滚动滚动条不正确的问题
- 禁止复制粘贴签名图片的逻辑移动到了 EditorHelper
- 修复水印无法删除的问题
- 解决在只有标签的单元格内移动光标不好使的 bug

## 2.23.9

`2023-6-1`

- 增加图片排版 3,3,4 的情况

## 2.23.8

`2023-6-1`

- ImageTable.ts 中 updateSerialNum 更新时判断是否是序号再更新值,解决交换图片顺序之后全都改成序号的 bug
- 解决调用 insertImageTable 的时候表格没有创建成功报错的 bug
- insert_raw 里边给 ImageTable 属性赋值增加 imageList 解决撤销后再插入报错的 bug
- 文档对齐不同步 bug 修改
- 模板转换后级联失效问题

## 2.23.7

`2023-5-31`

- 解决单独为一段的嵌套文本域，只复制那一段嵌套文本域，粘贴出来多一个文本域边框的问题
- 增加文本域的输入方式属性，适配 vue 的点选模式
- 服务端中用到 window 报错问题

## 2.23.6

`2023-5-30`

- 增加管理员模式接口
- 优化滚动条透明逻辑
- 给表格增加 meta 属性
- 修复 cell 的滚动条 render 时拿到的滚动位置不准确的问题
- 给 ImageTable 设置线的透明度
- 修改分组表单模式下，光标方向键移动 bug

## 2.23.5

`2023-5-29`

- 解决不展示拼音输入内容换行时自动删除内容的 bug

## 2.23.4

`2023-5-26`

- 修复滚动单元格选区不正确的问题
- 滚动单元格打印提交
- 修复调整表格线后滚动单元格内容为空 的问题
- 修复固定单元格高度与 normal 切换时单元格不更新的问题
- getStr 报错修改
- 复制粘贴样式 bug 修改
- 同一个方法不能加两个装饰器 bug 修复
- 去掉 isField

## 2.23.3

`2023-5-26`

- 优化文本域嵌套表格时内容展示
- 行倍距接口调整
- 修复滚动后光标位置和选区位置不正确的问题
- 文档对齐的情况下重复首行缩进修改
- 修复光标位置在滚动时不更新的问题
- 修改格式化表格的方法中的 padding_left 和 right 取相反的，为了下方的文本域居中
- 新增滚动单元格打字时滚动的逻辑
- 绘制滚动单元格滚动条
- 初步完成排版功能，将原来的 insertTable 中的插入逻辑抽离出来
- 增加了中文输入的时候是否用字母占位的配置
- 增加性能监测类
- 修改 delete 空编辑器报错
- 修复滚动单元格选区时滚动选区会超过单元格的问题及点击在选区单元格外时仍会滚动的问题
- 修复光标超出可视范围内不滚动的问题
- 修复鼠标放在滚动条上无效果的问题

## 2.23.2

`2023-5-23`

- 实现图片排版中创建合并后表格的功能
- 固定单元格高度滚动功能
- removeParagraphElement 参数形式修改
- 多层嵌套级联文本域设置

## 2.23.1

`2023-5-19`

- 解决新版表格拆分逻辑 row_size 分配不对的问题和 cell.split 传参不对的问题，错误效果是有空白窜罗和分页单元格内容永远在上一页，没有被拆分
- 复制粘贴表格数据样式调整
- 新增指定文本域高亮功能
- 修复页眉中插入表格表格内无内容的问题
- 新增获取所有图片的接口
-

## 2.23.0

`2023-5-17`

- 增加支持手写输入
- 增加调整字符间距功能
- 增加拖拽事件
- 文本域高亮

## 2.22.14

`2023-6-7`

- 增加了字母占位的配置

## 2.22.13

`2023-5-29` -将文本域、表格、段落 replaceWith 方法中的模式设置挪到方法开头，解决 pacs 模板替换之前先插入表格报错的问题

## 2.22.12

`2023-5-15`

- 设置文本域最大宽度后压缩的文字使用图片打印出来
- 替换文本域表格段落的 replaceWith 方法中的 setViewMode 为直接属性赋值，避免调用 setViewMode 修改掉页眉页脚的编辑状态

## 2.22.11

`2023-5-15`

- 修改 font_style 的 ts 类型和上标下标的常量表示
- 干掉所有 ScriptEnum 替换成 ScriptType
- 临时解决 XField.toggleSymbol 卡顿问题，调用 refreshDocument(true)
- 给 cell.updateChildren 参数增加 ts 类型校验，优化逻辑代码，减少 get 属性的重复调用
- 表格 copy 增加 imageList 的 copy 解决 pacs 模板替换之后排版中的图片不能删除的问题
- 调整 json 对比接口，增加可传入忽略属性参数
- 解决模板替换，原来只读文本域插入后变成非只读的问题

## 2.22.10

`2023-5-12`

- 文本域最小宽度换行时处理
- 同一个分组内，外面内容不能往表格中拖拽的 bug
- 修改 Paragraph.ts 文件中的 updateChildren 方法，更简化
- 优化全选逻辑
- 取消高亮优化

## 2.22.9

`2023-5-10`

- 分组表单模式分组定位视图滚动不正确问题

## 2.22.8

`2023-5-9`

- 解决中文输入时，设置字体样式只闪一下，继续打字不生效的问题
- 优化设置文本域最小宽度问题，文本域换行时仍存在问题
- 分组表单模式光标问题

## 2.22.7

`2023-5-9`

- 分组表单模式开启后不允许通过光标移动跳分组
- 调整文本域最小与最大宽度设置逻辑
- 解决段落和文本域 replaceWith 中 cell 位置判断不准的 bug
- 插入模板增加参数是否替换成 rawData，判断是否有 hasLastLineBreak 和是否走插入模板逻辑，解决文本域替换时，段落不居中的问题
- 修改了 caret_move 方法使用的 direction 的 ts 类型，修改了参数名

## 2.22.6

`2023-5-8`

- 开启 keepSymbolWidthWhenPrint 配置后续打与区域打印不生效 bug

## 2.22.5

`2023-5-8`

- 修改 Cell.ts 文件中 insert_raw 方法里 new 图片排版表格的判断和将该表格的 name 值设置为常量
- 简洁模式或者文本域边框隐藏时不允许编辑，否则因文本域边框宽度为 0 时会导致一系列问题
- 修改文本域表格段落的 getRawData 和 replaeWith 方法
- 分组表单问题修复
- 修改 XField.ts 文件中的 replaceWith 中删除文本域的逻辑
- 文本域替换 rawData 增加非普通模式的判断
- 段落和表格也增加非普通模式的判断

## 2.22.4

`2023-5-5`

- 修复文本域边框置空后删除报错的问题
- 实现文本域的 getRawData 方法
- PDF 和 C++打印绘制斜线
- 在 Editor.ts 文件中暴露批次获取容器的 rawData 的方法和批量替换的方法
- 修改文本域，段落和表格的 replaceWith 方法中的插入模板，由 editor 调用改为 EditorHelper 调用，避免在历史堆栈中记录

## 2.22.3

`2023-5-4`

- 在 Utils 中增加抽离出的初始化 cell 的方法，修改了 Selection 中 getRawData 逻辑，并给获取文本域的 rawData 方法开个头
- 段落排版相关代码整理

## 2.22.2

`2023-4-28`

- 修复改变文本域边框边框位置不正确的问题

## 2.22.1

`2023-4-28`

- 优化文本域边框逻辑，高度跟随最高的元素
- 修复 input 文本域有时画不上的问题

## 2.22.0

`2023-4-27`

- 37 版本全部需求

## 2.21.13

`2023-5-16`

- 临时解决表格隐藏显示边文本域框卡顿问题

## 2.21.12

`2023-5-9`

- 解决稍微拖拽 label 标签，位置不变，getFocusField 不对的 bug
- 增加根据 base64 导出文件工具函数
- 设置单元格边距文字重叠 bug 修改
- 修复文本域边框置空后删除报错的问题
- 修复 c++打印报错问题

## 2.21.11

`2023-4-26`

- 修改 insertText 方法
- 修复 PDF 打印预览报错问题
- 嵌套文本域跨多段时数据转换结构错乱
- 微调模型数据转原始数据逻辑
- 修改了替换换行符的函数名
- 修改 Paragraph.ts 中的 insertElements 的参数名字等
- 按钮移入移出效果问题修复
- 页眉文本域 updateFieldText 结果为 undefined 的问题处理
- 对比 rawDatabug
- 对比 rawData 函数逻辑优化

## 2.21.10

`2023-4-24`

- 修复按钮打印报错问题
- 新增打印时 button 按钮隐藏的功能
- 修复按钮高度不正确的问题
- 修复图片在页眉中不编辑也能有虚影的问题

## 2.21.9

`2023-4-23`

- pdf 图片打印报错

## 2.21.8

`2023-4-23`

- 解决表格插入多行撤销问题
- pdf 打印时在线图片打印不出来问题

## 2.21.7

`2023-4-21`

- 解决嵌套文本域数据转换的 bug

## 2.21.6

`2023-4-21`

- 解决中文输入法的时候在锁定分组内仍然能够编辑的 bug
- 解决撤销错乱的问题
- 修复字符对齐问题
- 解决数据转换有引用，导致撤销时数据错乱的 bug
- 修复在表格内首行缩进报错的问题
- 修改替换特殊字符的方法
- 修改合并单元格和判断是否可以合并的函数名字

## 2.21.5

`2023-4-20`

- 增加字符对齐兼容性处理逻辑

## 2.21.4

`2023-4-20`

- 表单模式下删除报错
- 68 版本浏览器单元格内删除报错 bug 修复
- 修改设置最大宽度文本域，在行尾跨行后，输入中文，内容显示到文本域外边的 bug
- 都昌模板转换报错修复

## 2.21.3

`2023-4-19`

- 解决修改单元格内容，其他非上对齐单元格内容位置不变的 bug，解决新版表格拆分，第二页往后表格 top 值计算不对的 bug，去掉嵌套文本域数据转换原来的 userLocal
- 增加低版本浏览器复制粘贴纯文本
- 非 https 协议打开的数据复制，放进剪切板纯文本
- 复制粘贴时只有不是本地的时候才进行版本和 https 协议的判断

## 2.21.2

`2023-4-18`

- 文本域边框样式与其中文本样式一致时内容丢失 bug
- 嵌套文本域数据转换增加判断
- 插入模板单选框外框变蓝色问题修复
- 修改插入图片排版和删除控制序列号的逻辑

## 2.21.1

`2023-4-17`

- 复制粘贴不带样式，压缩数据部分恢复
- 增加打印前事件，用于修改打印配置
- 快速录入模式 bug 修复（getnextField 方法还没改）
- 解決跨段嵌套文本域 rawData 转 modelData 造成被嵌套的文本域跟外层平级的 bug，删除报 removeElement 的那个
- 修复文档对齐的复制问题
- 撤销不显示的 bug 修改

## 2.21.0

`2023-4-10`

- 36 版本全部需求

## 2.20.14

`2023-4-13`

- 修复文档对齐的复制问题
- 修复区域打印在表格内多打一行的问题

## 2.20.13

`2023-4-12`

- 修复字符对齐导致的报错问题

## 2.20.12

`2023-4-11`

- 增加打印前事件，用于修改打印配置
- 修复打印预览和结果对不齐的问题

## 2.20.11

`2023-4-10`

- 新增带秒的日期判断

## 2.20.10

`2023-4-10`

- 补充缺失的中文字体
- 日期选择框新增汉字秒类型

## 2.20.9

`2023-4-7`

- 修复字符对齐在简洁模式下对不齐的问题

## 2.20.8

`2023-4-6`

- 打印内容缺失问题修复
- 去掉 modelDataToRawData 中的序列化代码，解决 pacs 打字操作卡顿的问题
- 修复字符对齐删除时位置不正确的问题

## 2.20.7

`2023-4-4`

- 增加对比两个 json 是否一致接口，忽略一些 id 类属性

## 2.20.6

`2023-4-4`

- 修复 ImageTable 数据转换时部分属性未保存
- 文本域 style 属性中缺少 family 与 height 时打印众阳浏览器闪退问题

## 2.20.5

`2023-4-3`

- 检查分组 id 的方法提取
- 解决了一个根据 path 获取不到内容的报错
- 暂不使用图片优化,先使用本地测试

## 2.20.4

`2023-4-3`

- 增加分组内容转换
- 优化模板转换分组相关代码
- 移除新增加的修改图片 dpi 方法
- 插入图片排版测试按钮问题修复、modelData 转 Dom 逻辑优化
- 插入模板图片丢失问题修复

## 2.20.3

`2023-3-24`

- 判断分组路径 bug 修改

## 2.20.2

`2023-3-24`

- 复制 editor 接口增加 imageMap 处理
- 判断分组路径修改

## 2.20.1

`2023-3-23`

- 修复打印预览时没有图片的问题

## 2.20.0

`2023-3-22`

- 35 版本需求

## 2.19.17

`2023-4-7`

- 数据转换时序列化复制性能问题

## 2.19.16

`2023-3-28`

- 新增自适应窗口大小接口

## 2.19.15

`2023-3-24`

- 解决打字还没回车或者空格的时候就删除选区的 bug

## 2.19.14

`2023-3-21`

- recordVersionInfo 二次修改

## 2.19.13

`2023-3-21`

- recordVersionInfo 修改

## 2.19.12

`2023-3-21`

- 解决表单模式下，在可编辑表格内，点击空文本域，光标位置在开头的 bug
- 修改了文本域边框保留的版本号判定
- 表单模式下，在可编辑的表格内按方向键移动光标不受表单模式影响

## 2.19.11

`2023-3-20`

- 分组页眉信息替换功能 bug 修复
- 图片排版的表格内的图片不让放大缩小
- 解决选区末尾坐标在段落开头时删除会多删除一行的 bug，统一单元格内外的复制逻辑
- 都昌模板转换未保留单元格左右内边距问题

## 2.19.10

`2023-3-15`

- 修改表格属性，去掉单元格的最大高度设置时也更新

## 2.19.9

`2023-3-15`

- 表单模式下护理表单点击右边纸张外区域报错

## 2.19.8

`2023-3-14`

- 表格拆分时，处理透明度的线
- 修复单元格内选区到下一段开头时设置选区内容与复制粘贴内容错误 bug
- 表单模式中空文本域使用 delete 删除导致光标位置错误
- 修复续打时会多打印上一行的签名的问题
- 段落重排接口支持只清空空段

## 2.19.7

`2023-3-9`

- 查找替换接口调整，优化高亮逻辑，返回查找数量

## 2.19.6

`2023-3-7`

- drawCell 传入 page 时绘制列表接口报错
- 表格拆分逻辑，拆分成两页情况，基本实现
- 插入多段内容报错问题修复
- 修复表单模式下粘贴时偶发粘贴到文本域外的问题
- 修复字符对齐时换行导致对不齐的问题

## 2.19.5

`2023-3-3`

- 修复分组内段落重排时将所有段落全部移除 bug

## 2.19.4

`2023-3-3`

- 剪切板读取接口问题修复

## 2.19.3

`2023-3-2`

- 调整进入页眉页脚编辑状态接口，增加剪切板写入与读取接口
- 每次设置焦点元素时先清空焦点元素

## 2.19.2

`2023-3-2`

- 重写表格拆分逻辑
- 新增段落重排剩余最后一段时需要判断是否删除
- 修改内部使用接口名 focus->setSelectImageOrWidget
- 修改获取当前焦点元素方法并加入本地测试
- 增加内部使用变量及函数管理类

## 2.19.1

`2023-2-27`

- 修复整合后 pdf 打印水印偏移的问题
- 修复区域打印选区不准确的问题
- 修复整合后水印绘制顺序

## 2.19.0

`2023-2-23`
34 迭代全部需求，打印相关提到 base

## 2.18.14

`2023-3-9`

- 单元格内选区到下一段开头时设置选区内容与复制粘贴内容错误 bug

## 2.18.13

`2023-3-9` -将白色为#FFF 的值转为透明背景色，之后新设置的白色背景需要设置成#FFFFFF,目的是为了解决之前修改过字体样式后会自动设置白色背景色问题

## 2.18.12

`2023-3-6` -解决文本域内字体缩放的 bug

## 2.18.11

`2023-3-6`

- 文本域设置最大高度后，复制内容粘贴到选区内容时无法替换选区内容
- 打印带列表的内容时报错

## 2.18.10

`2023-2-21`

- 可编辑表格在表单模式下里边的图片是可以选中的
- 分组增加获取纯段落属性
- 获取文档内所有段落增加可获取纯段落

## 2.18.9

`2023-2-16`

- placeholder 颜色错误修改

## 2.18.8

`2023-2-15`

- 匿名化星号展示规则问题修复

## 2.18.7

`2023-2-14`

- 匿名化星号展示规则问题修复

## 2.18.6

`2023-2-13`

- 匿名化问题修复
- 增加生僻字转图片方法，暂不启用
- 段落重排传入表格报错

## 2.18.5

`2023-2-10`

- 匿名化页眉页脚不生效问题修复

## 2.18.4

`2023-2-10`

- 图片打印匿名

## 2.18.3

`2023-2-9`

- 都昌模板转换增加服务端兼容

## 2.18.2

`2023-2-9`

- 都昌模板转换增加服务端兼容

## 2.18.1

`2023-2-1`

- 增加设置当前分组新页展示属性
- 优化绘制临时 border 的方法，减少传参

## 2.18.0

`2023-1-31`

- 33 版本全部需求

## 2.17.10

`2023-2-15`

- 优化设置文本域内字体缩放的方法和文本域增加 rows 的计算属性

## 2.17.9

`2023-2-10`

- 修改文本域获取高度的方法，设置字体大小加基础高度 d 的参数
- 修改文本域字体缩放逻辑

## 2.17.8

`2023-2-10`

- 去掉 Editor.ts 文件中 setCharacterSize 中的 @undoStackRecord(commands.uncertainty)

## 2.17.7

`2023-2-10`

- 增加文本域设置字体缩放的接口
- 文本域和段落设置字体大小方法增加参数，可传最大高度
- 设置字体大小添加到历史堆栈中

## 2.17.6

`2023-2-08`

- 增加生僻字支持，修改弹窗方式

## 2.17.5

`2023-1-31`

- 新增配置可控制编辑器展示字体

## 2.17.4

`2023-1-29`

- 修复段落重排段落全是空行的情况

## 2.17.3

`2023-1-29`

- 修复分组表单模式点击切换分组时的问题

## 2.17.2

`2023-1-20`

- 新增分组表单模式

## 2.17.1

`2023-1-19`

- 新增表格指定行转换接口
- 新增表格转换原始数据时合并单元格的处理
- 修复文本域替换图片接口不支持字符串宽高的问题
- 新增按配置加载接口

## 2.17.0

`2023-1-18`

- 修复表格线在放大时位置不准确的问题
- 根据版本号判断文本域边框宽度是否保留

## 2.16.9

`2023-1-13`

- 修复插入模板时合并文本域样式时未加判断的问题

## 2.16.8

`2023-1-13`

- 插入模板时统一字体字号时兼容老模板

## 2.16.7

`2023-1-13`

- 新增服务端获取在线图片接口判断
- 新增插入模板是否使用默认字体字号配置

## 2.16.6

`2023-1-11`

- 新增服务端重置状态接口

## 2.16.5

`2023-1-9`

- 修改表单模式下文本域粘贴到文本域之外的 bug

## 2.16.4

`2023-1-6`

- 修复图片编辑报错的问题

## 2.16.3

`2023-1-5`

- 修复表格背景色复制换页问题
- imageMap 中图片加载无需等待图片加载完后在放置

## 2.16.2

`2023-1-4`

- cell 中的 bgcolor 放到 style 中

## 2.16.1

`2023-1-4`

- 修复页边距 40 时选中表格最后一列选区不对的问题
- 新增改变表格背景色的问题
- 新增授权码过期提示水印接口

## 2.16.0

`2023-1-3`

- 31 版本需求
- 增加表单只读属性，防止与编辑器只读混乱

## 2.15.7

`2022-12-30`

- 修复表格在表单模式下可编辑没有保存的问题
- 绘制表单模式下表格可编辑提示框
- 处理特殊字符，底层逻辑会将其转换成乱码问题
- 去掉 image.onload 中白色背景逻辑
- reInitRaw 中添加 watermark 逻辑

## 2.15.6

`2022-12-28`

- 修复背景绿色问题

## 2.15.5

`2022-12-28`

- 修复移动端编辑器页面偏移问题

## 2.15.4

`2022-12-27`

- 修复水印输入框拖动位置不准确的问题
- 修复 reInitRaw 之后打字撤销字体样式变粗的问题
- 表单模式下按住鼠标左键不允许粘贴

## 2.15.3

`2022-12-26`

- 修复水印图片改变图片大小位置不准确的问题

## 2.15.2

`2022-12-26`

- 优化获取单元格的判断
- 新增水印文字改变颜色
- 文档对齐问题修改
- 水印背景颜色问题修改

## 2.15.1

`2022-12-21`

- 修复先打字再调用 reInitRaw，再打字，再撤销字体样式文字
- 固定单元格高度，路径使用装饰器
- 水印文字提交

## 2.15.0

`2022-12-16`

- 30 迭代全部需求

## 2.14.11

`2022-12-14`

- 新增字体高度配置给出不符合要求的提示

## 2.14.10

`2022-12-14`

- 修复当特殊字符在字典文件中找不到报错的问题
- 新增水印图片删除的方法

## 2.14.9

`2022-12-13`

- 修复水印图片模式切换位置问题

## 2.14.8

`2022-12-13`

- 恢复文本域样式优化

## 2.14.7

`2022-12-13`

- 修复引入模板数据插入到文本域外部的问题

## 2.14.6

`2022-12-12`

- 修复固定单元格高度后，内容满了再删除，单元格高度变大的问题
- 修复字体缩放到一页后删除内容导致报错的问题

## 2.14.5

`2022-12-12`

- 解决绿色背景问题

## 2.14.4

`2022-12-9`

- 解决 message 提示信息的问题

## 2.14.3

`2022-12-9`

- 配置字体缩小到一页
- 修复移动端水印多出的 div 的问题
- 修复模板字符串报错的问题

## 2.14.2

`2022-12-6`
修复固定单元格高度，单元格内文本域没有缩小的问题

## 2.14.1

`2022-12-5`

- 增加自定义扩展信息属性
- 修复加密数据打开报错
- 增加压缩功能后，另存 json 接口调整
- 在插入分组并排序的方法中加上清空选区的操作

## 2.14.0

`2022-12-5`
先发一版

## 2.13.3

`2022-11-30`

- 修复都昌模板转换时设置级联文本域报错问题及下拉选择框激活模式问题
- 新增暴露压缩解压的接口

## 2.13.2

`2022-11-27`

- 修复王芬组内粘贴内容跑到分组外的问题
- 去掉判断解决粘贴段落内部分内容插入空行的问题
- 开启 dpi 修改后所有图片都进行转换，否则有部分图片不能正常展示
- 修复修改纸张类型与修改横纵向不生效的问题

## 2.13.1

`2022-11-25`

- 解决图片排版不能保存图片之间间距的问题
- 修改 getRawData 和 reInitRaw 可以获取压缩后的数据和加载后的数据
- 修复快速点击文本域紧接着输入内容回电到文本域外边的问题

## 2.13.0

`2022-11-21`

- 新增从外部复制图片到编辑器中
- 修复 C++打印有序列表序号不齐的问题
- 编辑器接口整理
- 新增选区复制某些单元格可直接粘贴到另一个表格的选区中
- 编辑器加载数据时可读取数据中的配置
- 编辑器数据结构优化
- 编辑器新增水印接口
- 编辑器表单模式下实现表格可编辑

## 2.12.4

`2022-11-23`

- 修复页面切换后调用 updateCanvasSize 方法会将页面恢复到光标位置的问题

## 2.12.3

`2022-11-15`

- 删除 package 打包报错的配置源

## 2.12.2

`2022-11-10`

- 修复表格分页时插入行携带数据报错的问题

## 2.12.1

`2022-11-7`

- 修复云病历转科后导致页眉与正文之间出现多行空白的问题
- 找回 editor.ts 文件中表格是否可以删除行和列的方法

## 2.12.0

`2022-11-4`

- 选区内容可以放大缩小
- 增加粘贴前事件，可对要粘贴的内容进行处理
- 表格新增行时可自动携带上一行内容
- 增加编辑器服务端原始 json 转 PDF Base64 接口(详细信息见编辑器在线 API 文档)
- ctrl+tab 能够在表格内设置缩进

## 2.11.7

`2022-11-7`

- 修复云病历转科后导致页眉与正文之间多出多行空白问题

## 2.11.6

`2022-11-1`

- 修改文本域属性接口，增加判断文本域是否已经被删除

## 2.11.5

`2022-11-1`

- 修复转科后页眉高度不一致导致的页眉与正文重合的问题

## 2.11.4

`2022-10-27`

- 修复修改页眉中文本域报错的问题
- 修复续打模式下页眉下多一个空行的问题

## 2.11.3

`2022-10-26`

- 新增服务端启动传入 rawData
- 修复插入图片不显示及透明背景图片打印绿色问题
- 修复段落重排有空字符串报错和表格内无反应问题
- 新增\r 字符宽度判断

## 2.11.2

`2022-10-24`

- 新增服务端启动传入配置项
- 图片背景改变还原

## 2.11.1

`2022-10-21`

- 修复段落重排首行不缩进情况不清除空格的问题

## 2.11.0

`2022-10-21`

- 调整段落重排接口，通过参数可控制是否清除段落所有的空格、是否需要设置段落首行缩进。
- 新增将光标定位到单元格接口
- 删除或者清空分组的时候，没有删除 cell 内对应的文本域
- 获取多个文本域，但是只给一个文本域赋新值，没有赋新值但是又调用 updateFieldText 的方法的文本域，值会变成 undefined
- 痕迹视图中的痕迹文本背景色不应该被复制

## 2.10.4

`2022-10-28`

- 修复续打模式下页眉下多一个空行的问题

## 2.10.3

`2022-10-19`

- 修复都昌模板转换后图片变大、下划线不显示、字体颜色与背景色未转换、表格线显示隐藏的问题
- 修复没有任何批注信息时切换批注模式报错的问题

## 2.10.2

`2022-10-10`

- 修复页眉分组关联导致页眉高度问题

## 2.10.1

`2022-10-10`

- 修复护眼模式下复选框背景颜色问题
- 修复背景色使用批注颜色的问题

## 2.10.0

`2022-10-10`

- 新增护眼模式
- 去除 pdf 打印所需字体文件请求时的版本号,避免每次升级后都重新请求字体文件
- 优化单元格复制、数据更新代码逻辑以提升编辑时性能
- 修复光标定位到文档末尾接口 bug
- 增加图片编辑功能，初步先实现可在图片上绘制线条及线条的编辑功能。
- 优化云病历病程记录多页时卡顿问题
- 修复清空内容按钮的 BUG
- 优化表格插入行列时边框线显示隐藏效果

## 2.9.0

`2022-09-23`

- 新增插入文本域接口中的更新增加增量更新参数
- 修复历史堆栈记录配置为 0 时不生效的问题
- 修复删除表格导致的删除表格上方 row 和 table 的 top 值不对导致的页面错乱的问题

## 2.8.2

`2022-09-14`

- 修复表格分页时，上下拖动表格线，重排部分单元格，对齐实时生效的问题
- 修复批注 commentsIDSet 为空值的问题
- 修复多个文本域粘贴报错的问题

## 2.8.1

`2022-09-8`

- 都昌模板转换增加首行缩进，多选下拉框，文本域边框隐藏功能
- 优化复制粘贴文本域保证正确顺序
- 优化都昌模板转换逻辑，增加级联文本域转换

## 2.8.0

`2022-09-7`

- 新增续打病历时可选择页码功能
- 新增单元格中 Tab 键跳至下一单元格功能以及追加一行功能
- 优化多页内容时拖动滚动条时体验
- 新增批注功能
- 新增单元格锁定功能
- 修复使用 Delete 键删除内容时偶发输入 null 字符的问题
- 新增插入线条功能

## 2.7.14

`2022-09-5`

- 合并都昌模板转换修改到 22 版本发布新版

## 2.7.13

`2022-09-5`

- 合并都昌模板转换修改到 22 版本发布新版

## 2.7.12

`2022-09-3`

- 都昌模板转换表格嵌套异常提示移除

## 2.7.11

`2022-09-3`

- 都昌模板转换逻辑只允许出现一次的节点未重新初始化

## 2.7.10

`2022-09-3`

- 优化都昌模板转换逻辑

## 2.7.9

`2022-09-3`

- 都昌模板转换优化

## 2.7.8

`2022-09-3`

- 修复表格线绘制的问题

## 2.7.7

`2022-09-3`

- 修复模板转换报错的问题

## 2.7.6

`2022-09-1`

- 修复替换文本域在有些情况下报错的问题

## 2.7.5

`2022-08-31`

- 修复滚动条不能拖动的问题

## 2.7.4

`2022-08-30`

- 修复移动端不能滑动的问题

## 2.7.3

`2022-08-30`

- 添加 reitRaw 的 emit 触发事件

## 2.7.2

`2022-08-26`

- 修复转换为 rawData 时 shapes 为空报错问题

## 2.7.1

`2022-08-25`

- 修复鼠标在编辑 shape 时的样式
- 修复 shape 无法撤回的问题

## 2.7.0

`2022-08-25`

- 新增文本域自定义校验功能
- 新增通过 delete 键可以删除表格上方空行
- 新增选择框只有一个对号的样式
- 修复配置默认行倍距不生效问题
- 新增增加自定义线条功能

## 2.6.2

`2022-08-12`

- 优化 Pacs 插入图片排版
- 替换文本域 raw 丢失字符问题
- 新增移除绑定事件接口

## 2.6.1

`2022-08-11`

- 修改复选框级联逻辑

## 2.6.0

`2022-08-11`

- 复制跨段文本域粘贴到其他文本域后保存再重新加载数据后排版错乱。
- 表格性能优化。
- 移动端多页内容展示不全问题修复。
- 表单模式下使用 delete 键删除时光标移动位置不正确。
- 双面打印机病程记录续打开始页为偶数页时打印内容覆盖。
- 增加文本域级联功能。
- 可根据名称获取图片排版表格。
- 优化 Pacs 插入图片排版。
- 表格固定表头。

## 2.5.5

`2022-08-02`

- 修复表单模式下移动光标输入会一直提示表单模式不可输入问题

## 2.5.4

`2022-08-01`

- 修复系统打印预览带有文本域边框的问题

## 2.5.3

`2022-08-01`

- 修复打印预览后加载模板失败的问题

## 2.5.2

`2022-08-01`

- 修复打印预览后加载模板失败的问题

## 2.5.1

`2022-07-29`

- 修复替换文本与内容的问题

## 2.5.0

`2022-07-28`

- 修复新版打印下标变上标的问题
- 修复在光标处右键改为宋体，插入输入助手中的诊断内容，此时插入的诊断内容还是仿宋体的问题
- 全局配置转为实例配置
- 修复段落重排丢失段首文字的问题
- 修复替换页眉文本域内容，若接口参数中未添加 isHF 的值，那么替换内容将会以文本的形式插入到文档开头的问题
- 修复文本域日期选择框与下拉框双击激活的问题

## 2.4.2

`2022-07-15`

- 修复文本域校验样式问题

## 2.4.1

`2022-07-14`

- 修复返回的敏感词重复问题
- 新增文本域校验返回值

## 2.4.0

`2022-07-13`

- 段落重排逻辑优化
- 修复文本域日期选择框日期正则表达式不正确的问题
- 移动端下不在页面上方 input 元素
- 新增敏感词检测接口
- 新增文本域校验接口
- 修复字符对齐段落自上而下选区设置左对齐报错问题
- 修复移除嵌套文本域报错问题

## 2.3.3

`2022-06-29`

- 修复插入只有一个段落的模板不能缩进的问题

## 2.3.2

`2022-06-29`

- 修复英文输入法时按住 shift 不能输入的问题
- 新增插入模板时判断模板开头是否为表格的逻辑

## 2.3.1

`2022-06-28`

- 修复字符对齐排版错位问题
- 修复非文档首插入模板（模板开头为表格）多出空行问题
- 修复表单模式下连续文本域删除报错问题

## 2.3.0

`2022-06-27`

- 修复光标定位到只读文本域开始边框后按 delete 再按 backspace 能将文本域删除问题
- 修复多个编辑器下默认字体受其他编辑器影响的问题
- 解决 editor 上增加是否执行 pointerDown 的属性
- 修复设置字体大小导致编辑器崩溃的问题
-

## 2.2.0

`2022-06-14`

- 新增插入文本域带样式的逻辑
- 修复单元格最小高度设置后在设置居中对齐或下对齐，一直回车时存在内容超出单元格的问题
- 修复页眉文本域根据分组内容替换内容不正确的问题
- 支持 home,end 快捷键操作
- 上下文样式更改为编辑器实例的属性
- 修复选一列选区会会选中多列的问题
- 修复打开多选框属性后不能在光标位置插入自定义多选框问题

## 2.1.1

`2022-06-02`

- 修复编辑器在移动端下不缩放的问题

## 2.1.0

`2022-05-31`

- 新增表单模式下拖拽功能
- 新增文本域时间框禁止时间参数
- 新增失焦后光标不闪烁配置
- 修复删除导致的文本域字符与段落字符不一致的问题
- 修复列表序号重排不保存的问题
- 修复表单模式下选中文本域开始边框删除光标位置跑到第一个文本域内的问题
- 修复点击页面外光标定位不准确的问题
- 修复页眉点击时光标不能正常定位的问题

## 2.0.3

`2022-05-24`

- 修复向文本域中复制粘贴带换行符内容时造成文本域对象中字符缺失，继续往其中粘贴内容会导致 field.text 获取内容乱序的问题

## 2.0.2

`2022-05-20`

- 回退文本域日期选择优化功能

## 2.0.1

`2022-05-18`

- 修改获取页眉页脚单元格的方法

## 2.0.0

`2022-05-18`

- 新增表格 copy 的时候增加 name 属性赋值
- 修复页脚表格线横向不能连续拖动的问题
- 正常显示隐藏页脚表格线
- 修复点击滚动条外部也能实现滚动条滚动的问题
- 定位到指定文本域开头位置

## 1.6.4

`2022-05-17`

- 修复区域打印位置不准确问题
- 新增页脚可以横向拖拽表格线
- 注释表格行高改变时居中对齐的排版逻辑

## 1.6.3

`2022-05-13`

- 新增单独控制行首字符及数字排版功能更改
- 新增插入图片排版可以固定表格高度
- 插入图片排版新增各种判断，避免文本域、页眉页脚内报错错乱问题
- 新增图片排版序号与文本域可控制是否展示
- 修复进入页眉页脚编辑模式时进入页脚编辑的问题
- 图片排版双击图片不能删除
-

## 1.6.2

`2022-05-11`

- 新增符号排序
- 添加段落构造函数里对排版的复制控制
- 新增 ctrl+shift+F10 切换行首字符排版功能

## 1.6.1

`2022-05-09`

- 修改数字排版兼容问题导致的字符排版问题初夏那多余空行 id 的情况

## 1.6.0

`2022-05-07`

- 新增图片排版下方第二个文本域可控制不传
- 修改单元格内容导致表格行高发生变化，设置为非上对齐的单元格内容位置不变的 bug 修复
- 修复页眉设置选中文本字号后文本宽度不能立即生效的 bug
- 修复复制单元格时里边的下拉文本域没有下拉选项的问题
- 新增设置是否启用数字排版的接口
- 修改数字排版新段落的生成逻辑
- 添加排版变化后刷新页面现有数据

## 1.5.6

`2022-04-25`

- 新增兼容性数字排版功能

## 1.5.5

`2022-04-25`

- 撤除数字英文排版逻辑

## 1.5.4

`2022-04-24`

- 修复下载打印 PDF 序号不显示问题
- 解决跨段嵌套文本域末尾边框不显示问题

## 1.5.3

`2022-04-22`

- 升级不成功重新发版

## 1.5.2

`2022-04-22`

- 放开数字排版功能

## 1.5.1

`2022-04-21`

- 给 input 增加 autocomplete 属性设置为 off
- 锁定分组不能清空分组内容
- 去掉数字排版功能

## 1.5.0

`2022-04-14`

- 发布大版本

## 1.4.5

`2022-04-13`

- 修复文本域替换图片后，数字排版报错，删除文本域失败的问题

## 1.4.4

`2022-04-12`

- 修复 16K 大小打印预览显示超宽问题

## 1.4.3

`2022-04-08`

- 修复含有文本域的单元格合并拆分报错问题
- 修复关闭 F12 后编辑器大片空白的问题

## 1.4.2

`2022-04-07`

- 修改无序列表图标样式
- 修复单个段落的段落重排
- 新增替换或追加文本域元素接口
- 修复分页情况下双击表格线导致 row_size 不正确的问题
- 修复无法置空 placeholder 的问题
- 修复文本域替换文本接口传入多个相同文本域对象会将内容替换为 undefined 的问题
- 优化模型数据转原始数据文本域会多生成空 text 的问题
- 优化配置报错提示

## 1.4.1

`2022-03-29`

- 修改插入图片排版自动排列序号
- 修复增加 editor 配置后导致插入模板报错问题

## 1.4.0

`2022-03-29`

- 新增编辑器独立配置
- 修复图片放大后调整横纵向图片超出页面的问题
- 新增影响编辑器加载的配置校验
- 新增段落重排功能
- 新增文本域替换原始 json 数据接口

## 1.3.32

`2022-03-21`

- 修复选区光标在下方空白处移动时不能选区最后一段换行符问题
- 修复放大后更新 canvas 大小不准确的问题
- 新增表格内沿用 rawData 的表格 id

## 1.3.31

`2022-03-17`

- 修复校验纸张大小的方法
- 修改删除线位置
- 修复最后一页滚动问题

## 1.3.30

`2022-03-15`

- 修复调用清空历史记录接口，未能清空‘取消撤销’操作记录的 bug。
- 插入表格时取消最大行列限制。

## 1.3.29

`2022-03-14`

- 修复插入横向模板时未正常转为横向的问题
- 修复编辑器隐藏状态下报错的问题
- 修复退出页眉页脚编辑模式后光标不准确的问题
- 优化区域打印遮罩颜色

## 1.3.28

`2022-03-02`

- 优化文本域修改提示文本域为空
- 修复打印预览图片偏移问题
- 修复插入图片排版撤销不生效问题
- 修改单元格对齐在初次渲染数据时不正确的问题
- 新增获取选区纯文本的方法

## 1.3.27

`2022-02-23`

- 修复表格硬分割判断问题
- 新增出现横向滚动条时调整纵向滚动条位置的方法

## 1.3.26

`2022-02-22`

- 修复滚筒条随页面宽度改变不显示的问题
- 修复当前有选区且选区中包含文本域时调用移除文本域接口报错问题
- 修复硬分割判断的问题
- 修复设置简介模式时光标位置问题

## 1.3.25

`2022-02-21`

- 修复设置简洁模式报错问题

## 1.3.24

`2022-02-18`

- 因编译报错升级版本号

## 1.3.23

`2022-02-18`

- 修复都昌数据页数较多时转化卡死问题
- 修改多倍屏下自动滚动不准确的问题
- 新增页眉页脚编辑模式接口

## 1.3.22

`2022-02-17`

- 模板转换 label 没有 ID 问题解决

## 1.3.21

`2022-02-17`

- 修复都昌模板数据为空字符串时的处理方式
- 补充转换都昌模板样式缺失的情况

## 1.3.20

`2022-02-17`

- 修复都昌模板转换不成功问题

## 1.3.19

`2022-02-17`

- 修复都昌模板转换不成功问题
- 优化鼠标移动到文本区域外时的展示状态

## 1.3.18

`2022-02-16`

- 修复都昌新老版本数据接口不一致导致的模板转换报错问题

## 1.3.17

`2022-02-16`

- 修复撤销堆栈信息时将储存的上一次状态属性内容清空的问题
- 修复文本域包含两个连续文本域，光标点击在两个文本域中间时获取不到外层文本域的问题

## 1.3.16

`2022-02-11`

- 修复续打和区域打印模式下仍能弹出下拉框问题
- 处理表格合并单元格时绘制超出页面与回车时表格 rowSize 设置错误问题
- 优化页眉页脚复制逻辑和文本域边框显示隐藏逻辑
- 优化连续选中复选框情况下触发选区问题
- 修复多次撤销时撤销未初始化导致的撤销结果不对问题
- 修复页眉页脚修改后分散对齐导致的位置计算错误的问题
- 修复分散对齐时跨文本选区删除报错的问题
- 修复单选/复选框在表单模式下无法被选中的问题

## 1.3.15

`2022-02-08`

- 修复图片编辑框不消失问题
- 修复光标移动时三连击从页眉到正文报错及复选框连续选中问题
- 新增 pdf 立即续打功能

## 1.3.14

`2022-02-07`

- 因测试环境编译问题升级，代码无变动

## 1.3.13

`2022-02-07`

- 修复简单单选框/复选框无法被选中问题
- 修复从后往前选中多行删除报错问题
- 新增判断避免页眉信息重复替换

## 1.3.12

`2022-01-26`

- 修复单选框在跨页表格内选中成多选效果的问题
- 修改自定义多选框没有分组名的情况下处理逻辑

## 1.3.11

`2022-01-25`

- 修改插入图片排版，可传入图片宽高设置比例
- 修复表格内有文本域时跨页报错的问题
- 新增段落复制增加字符对齐属性复制

## 1.3.10

`2022-01-24`

- 新增插入 pacs 需求排版图片的方法

## 1.3.9

`2022-01-18`

- 新增调整页面大小后图片自适应的方法
- 新增在文本域后插入特定表格图像集合
- 修复表格间无空行情况下全选报错问题
- 修复分散对齐对于嵌套表格最后字符错乱的问题
- 修复格式刷导致的排版错乱问题
- 修改都昌模板转换逻辑，增加固定宽度文本域转换

## 1.3.8

`2022-01-13`

- 修复剪切板为空时拖拽报错问题
- 修复点击其他图片时原带编辑框图片编辑框不消失问题
- 修复无序列表有序列表混用时，有序列表序号出现混乱的问题

## 1.3.7

`2022-01-12`

- 修改光标在锁定文本域或分组内不能正常替换图片的问题

## 1.3.6

`2022-01-12`

- 新增 ctrl+c 在编辑期内粘贴为带格式粘贴，在编辑器外粘贴为纯文本粘贴，删除 ctrl+shift+c 和 ctrl+shift+v 快捷键，用 ctrl+c 和 ctrl+v 替代
- 新增分组关联页眉兼容老数据

## 1.3.5

`2022-01-10`

- 修复鼠标下拉超界滚动不生效问题

## 1.3.4

`2022-01-10`

- 修复序号列表错乱问题
- 新增分组关联页眉信息方式

## 1.3.3

`2022-01-07`

- 修复分组锁定情况下表格上下方能插入空行的问题
- 修改续打和区域打印禁止右键点击功能
- 修复页眉删除表格下方空行报错的问题
- 删除浏览器打印相关方法
- 修复区域打印模式下超越边界自动滚动问题

## 1.3.2

`2022-01-05`

- 修复页眉中表格操作问题修复

## 1.3.1

`2022-01-04`

- 修复光标闪烁问题

## 1.3.0

`2022-01-04`

- 优化页眉表格线拖动，没编辑页眉不展示表格线
- 新增 copyEditor 接口
- 修复右键触发 pointer_down 的问题
- 优化续打遮罩表格线问题

## 1.2.5

`2021-12-27`

- 新增超过边界滚动选区功能
- 优化水平线实现逻辑
- 修复一倍状态下预览图片偏移问题
- 优化获取 blob 数组的调用和解决获取 blob 的问题
- ## 1.2.4

`2021-12-27`

- 护理版本兼容
- 新增退出页眉页脚编辑模式接口
- 优化还原水平线相关内容

## 1.2.3

`2021-12-25`

- 修复续打刷新位置问题
- 重写打印方法

## 1.2.2

`2021-12-23`

- 新增插入模板参数控制是否替换页眉页脚内容
- 新增 canvas 转图片工具函数
- 还原数据压缩逻辑，兼容新产生的数据
- 打印重置

## 1.2.1

`2021-12-21`

- 新增退出编辑页眉页脚接口
- 修复删除选区开始为表格时删除后排版错乱问题
- 修复续打模式下整页不打印页眉页脚的问题
- 修复表格下方文字盖住表格线的问题
- 优化打印方法位置
- 优化组装原始段落信息逻辑

## 1.2.0

`2021-12-17`

- 新增传递文件流按钮
- 修改文档刷新按钮
- 修改页眉页脚水平线显示隐藏控制接口
- 新版打印插件配置修改

## 1.1.63

`2021-12-16`

- 文本域替换图片接口更改
- 修复单元格复制未复制文本域问题
- 修复跨页表格内复选框点击不生效问题
- 修复跨页表格点击单元格报错问题

## 1.1.62

`2021-12-14`

- 新增浏览器打印回调
- 表格线粗细优化
- 优化续打模式点击页脚时全选页面
- 优化原始数据过大问题
- 修复页眉页脚在光标移动后不可编辑的问题
- 修复只读文本域内不允许插入文本域的问题
- 修复替换文本域时传入数字类型报错问题
- 修复部分点击单元格报错问题
- 新增自定义开始页码及页数接口
- 优化纯文本复制
- 修复替换元素到文本域对象接口问题

## 1.1.61

`2021-12-09`

- 新增 config 配置项 font_family
- 修复区域打印从下网上选中问题
- 新增按住 ctrl 拖拽不删除原来选区功能
- 修复页眉页脚打印问题

## 1.1.60

`2021-12-07`

- 更换复制和复制文本快捷键
- 新增区域打印功能
- 修复字体放大后表格线被遮挡的问题
- 修复空文本域打印时最小宽度不生效问题
- 修复 mac 系统快捷键问题
- 修复页眉页脚连续删除渲染异常问题
- 修改打印纸张大小参数配置

## 1.1.59

`2021-12-03`

- 新增文本域校验功能总开关
- 修改页眉页脚遮盖后清晰度
- 修改默认字体配置，修改堆栈逻辑

## 1.1.58

`2021-12-02`

- 新增表格插入多行多列的接口
- 新增背景阴影配置项

## 1.1.57

`2021-12-01`

- 新增文本域最大宽度与最小宽度配置功能

## 1.1.56

`2021-11-30`

- 新增文档信息可存储页眉页脚水平线展示配置
- 新增批量打印的方法
- 修改横向展示后页面大小不自适应的问题

## 1.1.55

`2021-11-29`

- 修复图片点击报错问题
- 修复模板数据加载报错的问题
- 修复滚动到文档最后，编辑时页面会自动滚动一下的问题

## 1.1.54

`2021-11-28`

- 增加普通字体宽度，色值，加粗字体宽度，背景色的可配置功能。
- 修复了 shift 区域选择不生效的 bug。

## 1.1.53

`2021-11-25`

- 增加复选框同组名成组功能。
- 增加配置项重置接口。
- 修复只读文本域仍能够粘贴内容 BUG。
- 修复分组锁定后仍能点击设置复选框问题。

## 1.1.52

`2021-11-24`

- 修复右键页面空白处不显示右键菜单问题。
- 增加提示信息是否可显示控制属性。
- 增加辅助录入开关控制属性。

## 1.1.51

`2021-11-24`

- 修复文本域 tip 属性不可修改问题。
- 修复根据坐标获取指针位置信息方法返回值为光标位置信息问题。

## 1.1.50

`2021-11-24`

- 增加 BoxField 文件为 XField 的子类。
- 增加表格可视行数属性与可视列数属性。
- 增加编辑器放大后相对偏移量属性。

## 1.1.49

`2021-11-23`

- 转换数据等方法中表格增加 name 属性的赋值
- 选区情况下删除表格的多行
- 修改编辑器事件名 keydownBefore 名为 beforeKeyDown。
- 增加编辑器 input 事件与 exeCommand 命令执行事件。
- 增加获取行、列单元格实例方法与单元格内追加内容方法。

## 1.1.48

`2021-11-22`

- 增加获取搜索关键词的方法
- 增加根据表格 name 值获取表格数组的方法
- 增加获取光标位置处表格的方法
- 增加 keydownbefore 事件
- 增加横向纵向打印参数
- 增加中文输入开始和结束事件

## 1.1.47

`2021-11-19`

- 增加文本域只读时判断，不可通过接口再插入其他内容。
- 增加通过类型获取文本域数组接口。
- 修复表格内拖动图片报错的 bug

## 1.1.46

`2021-11-19`

- 增加页眉页脚水平线显示隐藏控制。
- 优化段落居中逻辑，应用到每一行。

## 1.1.45

`2021-11-19`

- 修复只读文本域不能通过回退键删除只能通过选区删除的问题。
- 修复向空文本域中插入自定多选框未清空背景文本问题。
- 修复 Shift 键选区操作时，点击已选中区域会使选区消失问题。
- 修复插入非成组自定义多选框时不能设置禁用 BUG。

## 1.1.44

`2021-11-18`

- 新增单选框、复选框相关组件。
- 新增页面自适应最大值的方法。
- 新增纸张大小配置
- 修复文本域中存在小组件或图片时复制粘贴错误。

## 1.1.43

`2021-11-15`

- 初次上线稳定版本
