import Editor from "./Editor";
import Font from "./Font";
import { Path } from "./Path";
import fontFaceObj from "./FontFaceObj";
import { GetDataType } from "./EditorConfig";
import pako from "../assets/pako";
// @ts-ignore
import { Base64 } from "js-base64";
import { Config } from "./Config";
import Cell from "./Cell";
import Table from "./Table";
import Paragraph from "./Paragraph";
import Row from "./Row";
import Page from "./Page";
import Character from "./Character";
import XField from "./XField"; // 因为这里定义了 isXField 在 cell 中使用了 所以这里的引用必须放在 import Cell from "./Cell" 后边
import { COMMENT_LIST_MIN_WIDTH, rareSubscript, rareSuperscript } from "./Constant";
import { BarcodeInfo, getBarcodeSrc, validateTextByInfo } from "./BarcodeUtils";
import Fraction from "./Fraction";

export const root_node = {
  pos: [0, 0],
  colspan: 1,
  rowspan: 1,
  children: [],
  is_show_slash_up: false,
  is_show_slash_down: false
};

export function uuid(prefix: string): string {
  const uuid = "xxxxxxxx".replace(/[xy]/g, (c) => {
    const r = (Math.random() * 16) | 0; // 效果等于 parseInt(Math.random() * 16) 就是取整
    const v = c === "x" ? r : (r & 0x3) | 0x8;
    return v.toString(16);
  });

  return prefix + "-" + uuid;
}

// 初始化 cell 在 getRawData 中使用
export function initCell(editor: Editor, id: "trans" | "header_trans" | "footer_trans") {
  const cell = new Cell(
    editor,
    root_node.pos,
    root_node.colspan,
    root_node.rowspan,
    null,
    id
  );
  cell.appendEmptyPara();
  return cell;
}

export function getCanvasRendering(parameter: {width: number, height: number}): {canvas: HTMLCanvasElement, ctx: CanvasRenderingContext2D} {
  const { width, height } = parameter;
  const canvas = document.createElement("canvas");
  canvas.width = width;
  canvas.height = height;
  const ctx = canvas.getContext("2d")!;
  return { canvas, ctx };
}


export function getImageSrcWithWhiteBgc(image: HTMLImageElement, type = "image/png") {
  const { canvas, ctx } = getCanvasRendering({ width: image.width, height: image.height });
  ctx.fillStyle = "#fff";
  ctx.fillRect(0, 0, canvas.width, canvas.height);
  ctx.drawImage(image, 0, 0);
  const src = canvas.toDataURL(type, 0.96);
  return src;
}

/**
 * 判断某个数值是否在某个区间内
 * @param num
 * @param m
 * @param n
 */
export function checkNumInterval(num: number, m: number, n: number) {
  if (m >= n) {
    if (num <= m && num >= n) {
      return true;
    }
  } else {
    if (num >= m && num <= n) {
      return true;
    }
  }
}

export function nearest(numbers: number[], value: number): number {
  let index = 0;

  let distance = Math.abs(value - numbers[0]);

  for (let i = 1; i < numbers.length; i++) {
    const d = Math.abs(value - numbers[i]);

    if (d < distance) {
      distance = d;
      index = i;
    }
  }
  return index;
}
// 获取线段与圆交点的坐标， c:圆心坐标 ，R：半径 ，B:线段另一端坐标
export function getIntersection(C: any, r: number, B: any) {
  const direction = { x: B.x - C.x, y: B.y - C.y };
  const length = Math.sqrt(direction.x ** 2 + direction.y ** 2);
  const normalized = { x: direction.x / length, y: direction.y / length };
  const intersection = { x: C.x + normalized.x * r, y: C.y + normalized.y * r };
  return intersection;
}
/**
 *  替换字符串中换行符的方法
 */
export function replaceLineBreakWith(text: string, relpace_text: string = "") {
  const shouldReplacedCharacters = ["\\r\\n", "\\n", "\\\\n"]; // 每一个 \ 都需要配一个 \ 进行转义
  const reg = new RegExp(shouldReplacedCharacters.join("|"), "g");
  return String(text).replace(reg, relpace_text);
}
/**
 * 保留n位小数
 * @param number
 * @param n 需要保留的位数
 */

export function keepDecimal(number: number, n = 0): number {
  return Math.round(number * Math.pow(10, n)) / Math.pow(10, n);
}

/**
 * 为数组成员求和
 * @param array
 */

export function sumOfArray(array: number[]) {
  return array.reduce((total, current) => total + current, 0);
}

export function deepClone<T>(param: T): T {
  const paramType = typeof param;
  if (paramType !== "object" || param === null) return param;

  const res: any = Array.isArray(param) ? [] : {};
  for (const key in param) {
    res[key] = deepClone(param[key]);
  }
  return res;
}

/**
 * 利用序列化与反序列化实现对象的深拷贝
 * @param obj
 */
export function serializeCopy(obj: Object): any {
  return JSON.parse(JSON.stringify(obj));
}

/**
 * 数组去重
 */
export function removeRepeat(arr: any[]) {
  return Array.from(new Set(arr));
}

/**
 * 将阿拉伯数字转换为汉字
 * @param params 需要转换的数字
 * @returns 转换结果
 */
const chnNumChar = ["零", "一", "二", "三", "四", "五", "六", "七", "八", "九"];
const chnUnitSection = ["", "万", "亿", "万亿", "亿亿"];
const chnUnitChar = ["", "十", "百", "千"];
function thousandToChinese(section: number) {
  const thousandSection = section;
  let strIns = "";
  let chnStr = "";
  let unitPos = 0; // 百十千数组下标
  let zero = true;
  while (section > 0) {
    const v = section % 10;
    if (v === 0) {
      if (!zero) {
        zero = true;
        chnStr = chnNumChar[v] + chnStr;
      }
    } else {
      zero = false;
      strIns = chnNumChar[v];
      strIns += chnUnitChar[unitPos];
      chnStr = strIns + chnStr;
    }
    unitPos++;
    section = Math.floor(section / 10);
  }
  if (thousandSection >= 10 && thousandSection < 20) {
    chnStr = chnStr.slice(1);
  }
  return chnStr;
}
export function numberToChinese(num: number) {
  let unitPos = 0;
  let strIns = "";
  let chnStr = "";
  let needZero = false;
  if (num === 0) {
    return chnNumChar[0];
  }
  while (num > 0) {
    const section = num % 10000;
    if (needZero) {
      chnStr = chnNumChar[0] + chnStr;
    }
    strIns = thousandToChinese(section);
    strIns += section !== 0 ? chnUnitSection[unitPos] : chnUnitSection[0];
    chnStr = strIns + chnStr;
    needZero = section < 1000 && section > 0;
    num = Math.floor(num / 10000);
    unitPos++;
  }
  return chnStr;
}

/**
 * 函数节流
 * @param func 函数
 * @param wait 延迟执行毫秒数
 */
export function throttle(func: Function, wait: number) {
  let timeout: any;
  return function () {
    const context = this;
    const args = arguments;
    if (!timeout) {
      timeout = setTimeout(() => {
        timeout = null;
        func.apply(context, args);
      }, wait);
    }
  };
}
/**
 * 判断是否为一个空对象
 */
export function isEmptyObj(obj: Object) {
  return !Object.keys(obj).length;
}

/**
 * 将新对象属性值赋值给原始对象
 */
export function props2Obj(oriObj: any, newObj: any) {
  for (const prop in newObj) {
    oriObj[prop] = newObj[prop];
  }
}

/**
 * 凯撒加密算法
 * @param str 要加密或者要解密的目标字符串
 * @param shift 位移位数 负数是往左位移
 * @param isDecrypt 是否解密 默认加密
 * @returns 解密或者加密后的字符串
 */
export function caesarCipher(str: string, shift: number, isDecrypt = false): string {
  const s = isDecrypt ? (26 - shift) % 26 : shift;
  const n = s > 0 ? s : 26 + (s % 26);
  return [...str]
    .map((w, i) => {
      if (i % 100 === 0) {
        const u = str.charCodeAt(i); // 返回 str 第i位置处的Unicode编码
        if (u >= 65 && u <= 90) { return String.fromCharCode(((u - 65 + n) % 26) + 65); }
        if (u >= 97 && u <= 122) { return String.fromCharCode(((u - 97 + n) % 26) + 97); }
      }
      return w;
    })
    .join("");
}

export function getImage() {
  const new_image = new Image();
  new_image.crossOrigin = "Anonymous";
  return new_image;
}

/**
 * 将string转xml对象
 * @param xmlString
 */
export function loadXML(xmlString: string) {
  let xmlDoc = null;
  if (!window.DOMParser && window.ActiveXObject) {
    const xmlDomVersions = ["MSXML.2.DOMDocument.6.0", "MSXML.2.DOMDocument.3.0", "Microsoft.XMLDOM"];
    for (let i = 0; i < xmlDomVersions.length; i++) {
      try {
        // eslint-disable-next-line no-undef
        xmlDoc = new ActiveXObject(xmlDomVersions[i]);
        xmlDoc.async = false;
        xmlDoc.loadXML(xmlString);
        break;
      } catch (e) {
        console.log(e, "出错了");
      }
    }
  } else if (window.DOMParser && document.implementation) {
    try {
      const domParser = new DOMParser();
      xmlDoc = domParser.parseFromString(xmlString, "text/xml");
    } catch (e) {
      console.log("出错了", e);
    }
  } else {
    return null;
  }

  return xmlDoc;
}

/**
 * 排序方法
 * @param arr 要排序的数组
 * @param attr 排序的依据,根据 arr 中每一项的什么元素进行排序
 * @returns 返回排好序后的 arr
 */
export function sort(arr: any[], attr?: string) {
  arr.sort((a, b) => {
    const [ax, ay] = attr ? a[attr] : a;
    const [bx, by] = attr ? b[attr] : b;
    if (ax !== bx) {
      return ax - bx;
    }
    return ay - by;
  });
  return arr;
}

// 给文本域排序
export function fieldsSort(fields: XField[]) {
  fields.sort((a, b) => {
    const a_path = a.start_para_path_inner;
    const b_path = b.start_para_path_inner;
    const a_para_index = a_path[a_path.length - 2];
    const b_para_index = b_path[b_path.length - 2];
    if (a_para_index !== b_para_index) {
      return a_para_index - b_para_index;
    }
    const a_char_index = a_path[a_path.length - 1];
    const b_char_index = b_path[a_path.length - 1];
    return a_char_index - b_char_index;
  });
}

//
/**
 * 获取文本域插入位置处的下标 已知当前光标位置处的段落路径，也可以获取到每个文本域的开始边框的段落路径 进行比较 如果文本域开始位置有三种情况，在当前光标前一段落，跟当前光标同一段落，在当前光标后一段落
 * 在同一段落就比较字符位置，因为本来文本域是顺序的(最起码重新打开后的所有文本域是顺序的)，所以一旦找到比当前光标位置靠后的文本域就在该文本域处插入就可以了。顺序就是对的了
 * @param para_path_with_caret 光标位置处的段落路径
 * @param allFieldsInCurrentCell 当前 cell 内的所有文本域集合
 * @returns
 */
export function getInsertIndexInCurrentCellFields(para_path_with_caret: Path, allFieldsInCurrentCell: XField[]): number {
  let insert_index = allFieldsInCurrentCell.length;
  const para_index_with_caret = para_path_with_caret[para_path_with_caret.length - 2];
  const character_index_with_caret = para_path_with_caret[para_path_with_caret.length - 1];
  for (let i = 0; i < insert_index; i++) {
    const field = allFieldsInCurrentCell[i];
    const field_start_para_path = field.start_para_path;
    const field_start_para_index = field_start_para_path[field_start_para_path.length - 2];
    const field_start_char_index = field_start_para_path[field_start_para_path.length - 1];
    if (field_start_para_index === para_index_with_caret) {
      if (field_start_char_index >= character_index_with_caret) {
        insert_index = i;
        break;
      }
    } else if (field_start_para_index > para_index_with_caret) { // 同一个段落里边 光标位置处往后 没有文本域了 就找往后的段落中的文本域 如果都没有 就跟 push 一样即可
      insert_index = i;
      break;
    }
  }
  return insert_index;
}

// xml对象转json
export function xmlToJson(xml: any) {
  // Create the return object
  let obj: any = {};

  if (xml.nodeType === 1) { // element
    // do attributes
    if (xml.attributes.length > 0) {
      obj["@attributes"] = {};
      for (let j = 0; j < xml.attributes.length; j++) {
        const attribute = xml.attributes.item(j);
        obj["@attributes"][attribute.nodeName] = attribute.nodeValue;
      }
    }
  } else if (xml.nodeType === 3) { // text
    obj = xml.nodeValue;
  }

  // do children
  if (xml.hasChildNodes()) {
    for (let i = 0; i < xml.childNodes.length; i++) {
      const item = xml.childNodes.item(i);
      const nodeName = item.nodeName;
      if (typeof (obj[nodeName]) === "undefined") {
        obj[nodeName] = xmlToJson(item);
      } else {
        if (typeof (obj[nodeName].push) === "undefined") {
          const old = obj[nodeName];
          obj[nodeName] = [];
          obj[nodeName].push(old);
        }
        obj[nodeName].push(xmlToJson(item));
      }
    }
  }
  return obj;
}

export function changeCanvasSize(canvas: HTMLCanvasElement, editor: Editor) {
  const parentDom = canvas.parentNode as HTMLElement;
  let height = parentDom.offsetHeight;
  const width = parentDom.offsetWidth;

  // 移动端
  if (editor.isMobileTerminal()) {
    canvas.style.height = height + "px";
    canvas.height = height * editor.config.devicePixelRatio;
    canvas.style.width = width + "px";
    canvas.width = width * editor.config.devicePixelRatio;
    return { width, height };
  }

  if (height === 0) {
    height = 600;
  }

  // PC端
  canvas.style.height = height + "px";
  canvas.height = height * editor.config.devicePixelRatio;
  const page_size = editor.config.getPageSize();
  let totalWidth = page_size.width;
  if (editor.is_comment_mode) {
    totalWidth += Math.max(editor.config.comment.listWidth, COMMENT_LIST_MIN_WIDTH);
  }
  if (width > totalWidth * editor.viewScale) {
    canvas.style.width = width + "px";
    canvas.width = width * editor.config.devicePixelRatio;
  } else {
    canvas.style.width = totalWidth * editor.viewScale + "px";
    canvas.width = totalWidth * editor.config.devicePixelRatio * editor.viewScale;
  }

  return { width, height };
}

export function handleSaveFontFace(font: Font, value: string, width: number) {
  const fontShape = (font.italic && font.bold)
    ? "3"
    : (font.italic ? "2" : font.bold ? "1" : "0");
  if (!fontFaceObj[font.family]) {
    fontFaceObj[font.family] = {};
    fontFaceObj[font.family][value] = {};
    fontFaceObj[font.family][value][fontShape] = {};
    fontFaceObj[font.family][value][fontShape][font.height] = width;
  } else {
    // 已经存了该字了 但是字体 字形 字号等 都不一定 存了
    if (!fontFaceObj[font.family][value]) {
      fontFaceObj[font.family][value] = {};
      fontFaceObj[font.family][value][fontShape] = {};
      fontFaceObj[font.family][value][fontShape][font.height] = width;
    } else {
      // 已经存了该字的字体了 但是字形 字号等 不一定存了
      if (!fontFaceObj[font.family][value][fontShape]) {
        fontFaceObj[font.family][value][fontShape] = {};
        fontFaceObj[font.family][value][fontShape][font.height] = width;
      } else {
        // 已经存了字形了 但是字号不一定存了 如果存了字号了 就不用管了
        if (!fontFaceObj[font.family][value][fontShape][font.height]) {
          fontFaceObj[font.family][value][fontShape][font.height] = width;
        }
      }
    }
  }
}

/**
 * 将字符串转为base64
 * @param str 字符串
 * @returns base64
 */
export function encodeBase64(str: string) {
  // 对字符串进行编码
  // const encode = encodeURIComponent(str);
  // 对编码的字符串转化base64
  const base64 = Base64.encode(str);
  return base64;
}

// base64 转字符串
export function decodeBase64(base64: string) {
  // 对base64转编码

  const decode = Base64.decode(base64);
  // 编码转字符串
  // const str = decodeURIComponent(decode);
  return decode;
}

// ArrayBuffer转为字符串，参数为ArrayBuffer对象
export function arrayBuffer2Str(buf: ArrayBufferLike) {
  let binaryString = "";
  const bytes = new Uint8Array(buf);
  for (let i = 0, len = bytes.length; i < len; i++) {
    binaryString += String.fromCharCode(bytes[i]);
  }
  return binaryString;
}

export function getInfoFromString(str: string) {
  // 正则表达式匹配 @{xxx} 这种结构
  const regex = /@\{([^}]+)\}/g;
  let lastIndex = 0;
  let result: any = [];

  let match;
  while ((match = regex.exec(str)) !== null) {
    // 将匹配到的普通文本部分加入结果数组
    if (match.index > lastIndex) {
      result.push({
        type: "text",
        value: str.slice(lastIndex, match.index),
      });
    }
    // 记录最后一次匹配的结束位置
    lastIndex = match.index + match[0].length;
    const arr = match[1].split("#");
    if (arr.length === 3) {
      result.push({
        type: "fraction",
        value: match[1],
        int: arr[0],
        numerator: arr[1],
        denominator: arr[2],
      })
    }
  }

  // 将最后一段普通文本加入结果数组
  if (lastIndex < str.length) {
    result.push({
      type: "text",
      value: str.slice(lastIndex)
    });
  }

  return result;
}

// // 示例用法
// const input = "Hello @{world}, this is a @{test} string.";
// const { segments, matches } = splitString(input);

// console.log("Segments:", segments);
// console.log("Matches:", matches);

// 压缩数据用
export function getRawDataByConfig(editor: Editor, rawData: any) {
  if (editor.config.getDataType === GetDataType.rawData) {
    return rawData;
  }
  if (editor.config.getDataType === GetDataType.compressedBase64) {
    return compressData(rawData);
  }
}

// TODO 这名字起的有歧义吧 是根据配置是否开启了压缩数据来进行选择用什么 rawData 但是这里边的代码跟配置没有半毛钱关系 压缩数据用
export function useRawDataByConfig(rawData: any) {
  if (typeof rawData !== "string") return rawData;
  // ["{", "\r", "\t", "\n"] 这些字符开头说明是未压缩过的
  if (["{", "\r", "\t", "\n"].some(c => rawData.startsWith(c))) return JSON.parse(rawData);
  return uncompressData(rawData);
}

// 获取转为Base64的压缩后的数据
export function compressData(rawData: any) {
  const imageSrcObj = rawData.imageSrcObj;
  rawData.imageSrcObj = {};
  const rewDataStr = JSON.stringify(rawData);
  const zipRawData = gzipOrUngzip("gzip", rewDataStr);
  // const zipRawData = pako.gzip(rewDataStr, { level: 2 });
  const binaryString = arrayBuffer2Str(zipRawData);
  const base64 = encodeBase64(binaryString);
  return base64 + "$$break$$" + JSON.stringify(imageSrcObj);
}

// 解压 转为base64 的压缩后的数据
export function uncompressData(compressedData: string) {
  while (true) {
    try {
      compressedData = JSON.parse(compressedData); // 传进来的字符串有可能是经过多次的 JSON.stringify 之后 所以要多次的 JSON.parse 恢复过去
    } catch (error) {
      break;
    }
  }
  if (typeof(compressedData) !== "string") {
    compressedData = JSON.stringify(compressedData);
  }
  const splitData = compressedData.split("$$break$$");
  compressedData = splitData[0];
  const str = decodeBase64(compressedData);
  const charData = str.split("").map(function (x: string) {
    return x.charCodeAt(0);
  });
  const binData = new Uint8Array(charData);
  const treatedData = gzipOrUngzip("ungzip", binData);
  const rawData: any = JSON.parse(treatedData);
  if (splitData.length > 1 && splitData[splitData.length - 1] !== "undefined") {
    let imageDataStr = splitData[1];
    imageDataStr = imageDataStr.replace(/\\"/g, '"');
    rawData.imageSrcObj = JSON.parse(imageDataStr);
  }
  return rawData;
}
// 压缩和解压
export function gzipOrUngzip(type: string, rawData: any): any {
  let treatedData;
  if (type === "gzip") {
    treatedData = pako.gzip(rawData, { level: 2 });
  } else if (type === "ungzip") {
    treatedData = pako.ungzip(rawData, { to: "string" });
  }
  return treatedData;
}

/**
 * 深度优先遍历
 * @param node 顶层节点
 * @param list 不需要传 内部递归需要
 * @returns 平铺的最底层数据 文字或者 img 标签
 */
export function depthFirstSearch(node: HTMLElement, list: (string | HTMLElement)[] = []) {
  // node不能为null
  if (node !== null) {
    const children = node.children || [];
    // 如果children.length存在
    if (children.length === 0) {
      if (node.nodeName !== "IMG") {
        list.push(node.innerText);
      } else {
        list.push(node);
      }
    }
    for (let i = 0; i < children.length; i++) {
      const element = children[i] as HTMLElement;
      // 递归调用
      depthFirstSearch(element, list);
    }
  }
  return list;
}

export function handleLowerVersionBrowserCopy(text: string) {
  const textArea = document.createElement("textArea") as HTMLTextAreaElement;
  textArea.value = text;

  // 如果创建的 textArea 不在可视区域内，会引起页面滚动，再滚回来，有闪动的效果，所以加定位
  textArea.style.width = "0px";
  textArea.style.position = "fixed";
  textArea.style.left = "-999px";
  textArea.style.top = "0px";
  textArea.setAttribute("readonly", "readonly");

  document.body.appendChild(textArea);
  textArea.select();
  document.execCommand("copy");
  document.body.removeChild(textArea);
}

export function handleLowerVersionBrowserPaste(editor: Editor) {
  const canvasUsedTextArea = document.getElementsByName("editor_input_name")[0] as HTMLTextAreaElement;
  canvasUsedTextArea.value = ""; // 必须要先清空否则使用的 value 值，每次都会追加
  canvasUsedTextArea.focus();
  const userAgent = navigator.userAgent.toLowerCase();
  if (userAgent.indexOf("electron/") < 0) {
    document.execCommand("paste");
  }
  setTimeout(() => { // 如果不加 setTimeout value 值就是空的
    let str: any  = canvasUsedTextArea.value;
    const changedStr = editor.event.emit("beforePaste", { str });
    if (changedStr !== "origin") {
      // 这就有监听了
      if (changedStr === false) return; // 返回了个 false 那就不执行粘贴逻辑了
      str = changedStr;
    }
    editor.insertText(str || "");
  });
}

export function useLowerVersionCopyPaste() {
  return !window.location.href.startsWith("http://localhost") && (getBrowserInfo().version[0] < 86 || !window.location.href.startsWith("https"));
}

export async function writeClipboard(text: string, html?: string) {
  if (useLowerVersionCopyPaste()) {
    handleLowerVersionBrowserCopy(text);
    return;
  }
  if (!html) {
    html = "<span>" + text + "</span>";
  }
  // 将两种类型的数据都要放入剪切板
  const htmlData = new Blob([html], { type: "text/html" });
  const plainData = new Blob([text], { type: "text/plain" });

  // eslint-disable-next-line no-undef
  const item = new ClipboardItem({
    [htmlData.type]: htmlData,
    [plainData.type]: plainData
  });
  await (navigator.clipboard as any).write([
    item
  ]);
}

export function readClipboard(): Promise<any> {
  return new Promise((resolve, reject) => {
    navigator.clipboard.read().then(async (items: any) => {
      const item = items[0];
      if (item.types.includes("text/html")) {
        let text, editorStr;
        let html = "";
        const htmlBlob = await item.getType("text/html");
        html = await new Response(htmlBlob).text();
        if (html.startsWith(`<pre editor-msun-copy-data=`)) {
          editorStr = html.split("editor-msun-copy-slice")[1];
        }
        const str = await navigator.clipboard.readText();
        if (str.startsWith(Config.program_flag_start)) {
          editorStr = str;
        } else { // 此时也有可能是从
          text = str;
        }
        resolve({ text, html, editorStr });
        return;
      }
      if (item.types.includes("text/plain")) {
        const plainBlob = await item.getType("text/plain");
        const plain = await new Response(plainBlob).text();
        resolve({ text: plain });
      }
    }).catch((error) => {
      reject(error);
    });
  });
}

export function versionDiff(version1: string | undefined, version2: string) {
  if (!version1 || !version2) return 0
  // // 定义两个版本号
  // const version1 = '1.2.3';
  // const version2 = '1.4.0';
  // 将版本号转换为数字数组，方便比较大小
  const v1 = version1.split(".").map(Number);
  const v2 = version2.split(".").map(Number);
  // 比较版本号大小
  for (let i = 0; i < v1.length || i < v2.length; i++) {
    const diff = (v1[i] || 0) - (v2[i] || 0);
    if (diff !== 0) {
      return diff > 0 ? 1 : -1;
    }
  }
  // 如果没有差异，则版本号相等
  return 0;
}

export function mergeObjNonNull(target: any, ...sources: any[]) {
  sources.forEach(source => {
    Object.keys(source).forEach(key => {
      const val = source[key];
      if (val !== null && val !== undefined) {
        target[key] = val;
      }
    });
  });
  return target;
}

// Least common multiple 最小公倍数
export function getLCM(arr: number[]): number {
  // 获取数组中所有数字的最大值
  const maxNum = Math.max(...arr);

  // 初始化最小公倍数为最大值
  let lcm = maxNum;
  if (lcm === 0) return 0; // 为 0 的话就死循环了
  let found = false;

  // 不断增加最小公倍数，直到找到能够被数组中所有数字整除的数
  while (!found) {
    let i;
    for (i = 0; i < arr.length; i++) {
      if (lcm % arr[i] !== 0) {
        break;
      }
    }
    if (i === arr.length) {
      found = true;
    } else {
      lcm += maxNum; // 因为最小一步就得是最大值，否则就不是最大值的公倍数了
    }
  }

  return lcm;
}

/**
 * 判断数据是否是数字 字符串也会判断成数字
 * @param value 要判断的值 参考网址：https://blog.csdn.net/qq_23365135/article/details/*********?ydreferer=aHR0cHM6Ly9wYXNzcG9ydC5jc2RuLm5ldC9sb2dpbj9jb2RlPWFwcGxldHM%3D
 * @returns
 */
export function isNumber(value: any): value is number {
  return !isNaN(parseFloat(value)) && isFinite(value);
}

/**
 * 判断数据不是 undefined 也不是 null
 * @param value 任意值
 * @returns
 */
export function isNotNullAndUndefined<T>(value: T): value is Exclude<T, null | undefined> {
  return value !== null && value !== undefined;
}

export function isBoolean(value: any): value is boolean {
  return typeof value === "boolean";
}

export function isTable(item: any): item is Table {
  return item instanceof Table;
}

export function isParagraph(item: any): item is Paragraph {
  return item instanceof Paragraph;
}

export function isRow(item: any): item is Row {
  return item instanceof Row;
}

export function isPage(item: any): item is Page {
  return item instanceof Page;
}

export function isCharacter(item: any): item is Character {
  return item instanceof Character;
}

export function isFraction(item: any): item is Fraction {
  return item instanceof Fraction;
}

export function isField(item: any): item is XField {
  return item instanceof XField;
}
// 判断点是否在矩形内
export function  isPointInRectangle (x1: number, y1: number, x2: number, y2: number, px: number, py: number) {
  if (x1 > x2 && y1 > y2) {
    if (px <= x1 && px >= x2 && py <= y1 && py >= y2) {
      return true;
    }
  } else if (x1 > x2 && y1 < y2) {
    if (px <= x1 && px >= x2 && py >= y1 && py <= y2) {
      return true;
    }
  } else if (x1 < x2 && y1 > y2) {
    if (px >= x1 && px <= x2 && py <= y1 && py >= y2) {
      return true;
    }
  } else if (x1 < x2 && y1 < y2) {
    if (px >= x1 && px <= x2 && py >= y1 && py <= y2) {
      return true;
    }
  }
  return false;
}
// 当前是否可编辑
export function allowEditing(editor: Editor): boolean {
  if (!editor.operableOrNot(["cell", "group", "field", "editor"])) return false;
  // if (editor.hold_mouse) return false // TODO 放开的话拖拽纯文本功能会受影响
  return true;
}

// 获取指定浏览器的版本
export function getSpecifiedBrowserVersion(broserName: string): number[] {
  const userAgent = navigator.userAgent;
  const index = userAgent.indexOf(broserName);
  if (index >= 0) {
    return userAgent.slice(index + broserName.length + 1).split(" ")[0].split(".").map(Number); // slice 截取的时候 +1 是多处理一个 /
  }
  return [];
}

export function getBrowserInfo(): { name: string, version: number[] } {
  const chromeVersion = getSpecifiedBrowserVersion("Chrome");
  if (chromeVersion.length > 0) {
    return {
      name: "谷歌",
      version: chromeVersion
    };
  }

  const IEVersion = getSpecifiedBrowserVersion("MSIE");
  if (IEVersion.length > 0) {
    return {
      name: "IE",
      version: IEVersion
    };
  }

  const firefoxVersion = getSpecifiedBrowserVersion("Firefox");
  if (firefoxVersion.length > 0) {
    return {
      name: "火狐",
      version: firefoxVersion
    };
  }

  const safariVersion = getSpecifiedBrowserVersion("Safari");
  if (safariVersion.length > 0) {
    return {
      name: "苹果",
      version: safariVersion
    };
  }

  const operaVersion = getSpecifiedBrowserVersion("Opera");

  if (operaVersion.length > 0) {
    return {
      name: "欧朋",
      version: operaVersion
    };
  }

  return {
    name: "",
    version: []
  };
}

export function isEqualIgnoreNullOrUndefined(value1: any, value2: any) {
  if (value1 === null || value1 === undefined) {
    return value2 === null || value2 === undefined;
  }
  if (value2 === null || value2 === undefined) {
    return false;
  }
  return value1 === value2;
}

export function compareJsonIgnoreKey(obj1: any, obj2: any, ignoreProps: string[]): boolean {
  // 如果类型不同，则认为不相等
  if (typeof obj1 !== typeof obj2) {
    return false;
  }

  // 如果是基本类型，则直接比较值是否相等
  if (typeof obj1 === "string" || typeof obj1 === "number" || typeof obj1 === "boolean") {
    return obj1 === obj2;
  }

  // 忽略指定属性进行比较
  if (typeof obj1 === "object") {
    for (const key in { ...obj1, ...obj2 }) {
      if ((ignoreProps as any).includes(key)) {
        continue;
      }
      if (!compareJsonIgnoreKey(obj1[key], obj2[key], ignoreProps)) {
        return false;
      }
    }
    return true;
  }

  // 其他类型默认为不相等
  return false;
}
// 根据base64导出文件
export function downloadFileByBase64(base64: string, fileType: string = "image/png", fileName: string) {
  // 将base64字符串转换成Blob对象
  const blobData = (() => {
    const byteCharacters = atob(base64.split(",")[1]);
    const byteNumbers = new Array(byteCharacters.length);
    for (let i = 0; i < byteCharacters.length; i++) {
      byteNumbers[i] = byteCharacters.charCodeAt(i);
    }
    return new Blob([new Uint8Array(byteNumbers)], { type: fileType });
  })();

  // 创建URL对象
  const url = URL.createObjectURL(blobData);

  // 创建a标签
  const link = document.createElement("a");
  link.style.display = "none";

  // 设置download和href属性
  link.download = fileName || "file";
  link.href = url;

  // 模拟点击事件进行下载
  document.body.appendChild(link);
  link.click();

  // 清理临时的 URL 对象
  URL.revokeObjectURL(url);
}

export function arrDeduplication(arr: number[][]): number[][] {
  const obj: any = {};
  for (const item of arr) {
    obj[item.toString()] = deepClone(item); // 即便 item 是数组也没有关系 obj[数组] 会将里边的数组通过 toString 转为字符串
  }
  return (Object as any).values(obj);
}

// 处理稀缺字符 特殊字符
export function handleRareCharacter(value: string, fontSize: number, top: number) {
  // 不能直接改 font.fontSize 和 font.top 因为引用类型 会把其他的也给改了 所以用两个变量过渡一下
  if (rareSuperscript.has(value)) {
    value = rareSuperscript.get(value)!;
    fontSize -= 2;
    top -= 4;
  }
  if (rareSubscript.has(value)) {
    value = rareSubscript.get(value)!;
    fontSize -= 4;
  }
  return [value, fontSize, top];
}
// 根据base64判断是否为图片
export function isBase64Image(str: string) {
  let res = false;
  if (!str) return res;
  if (str.startsWith("data")) {
    if (str.indexOf("base64") > -1) {
      res = true;
    } else {
      res = false;
    }
  } else {
    try {
      if (Base64.isValid(str)) {
        const prefixArr = ["/9j/",
          "iVBORw0KGgo",
          "R0lGOD",
          "Qk"];
        res = prefixArr.some((prefix) => str.startsWith(prefix));
        if (res && str.length > 100) {
          res = true;
        } else {
          res = false;
        }
      }
    } catch (error) {
      console.log(error, "出错了");
    }
  }
  return res;
}

export default function createImagePlaceholder(text: string) {
  const canvas = document.createElement("canvas");
  const context = canvas.getContext("2d")!;
  // 设置 Canvas 宽度和高度
  canvas.width = 100;
  canvas.height = 50;
  context.fillStyle = "#EEF4FE";
  context.fillRect(0, 0, canvas.width, canvas.height);
  const fontHeight = 20;
  context.fillStyle = "#A0A0A0";
  context.font = fontHeight + "px" + " '宋体'";
  // 绘制文本
  context.fillText(
    text,
    (canvas.width - fontHeight * 3) / 2,
    (canvas.height + fontHeight) / 2 - 5
  );
  const dataUrl = canvas.toDataURL("image/png", 0.5);
  return dataUrl;
}


/**
 * 插入浮动条码
 */
export function getBarcodeOrQrCodeSrc(text: string, barcodeInfo: BarcodeInfo) {

  if (!validateTextByInfo(text, barcodeInfo)) {
    return false
  }
  const src = getBarcodeSrc(text, barcodeInfo)

  return src
}

export function formatDate(date: Date) {
  const year = date.getFullYear();
  const month = ("0" + (date.getMonth() + 1)).slice(-2);
  const day = ("0" + date.getDate()).slice(-2);
  const hours = date.getHours();
  const minutes = ("0" + date.getMinutes()).slice(-2);
  const seconds = ("0" + date.getSeconds()).slice(-2);
  return year + "-" + month + "-" + day + " " + hours + ":" + minutes + ":" + seconds;
}

export function hexToRgba(hex: string) {
  if (hex.startsWith("rgba")) {
    return hex;
  }
  if (hex.startsWith("rgb")) {
    return hex.replace(")", ",0.5)")
  }
  // 移除前缀#符号
  hex = hex.replace(/^\s*#|\s*$/g, '');

  // 将三位十六进制数转换为六位
  if (hex.length === 3) {
    hex = hex.replace(/(.)/g, '$1$1');
  }

  // 提取R、G、B各自的十六进制表示方式
  const rgb = /^(\w{2})(\w{2})(\w{2})$/.exec(hex);

  // 转为10进制并返回
  return rgb ? 'rgba(' +
    parseInt(rgb[1], 16) + ',' +
    parseInt(rgb[2], 16) + ',' +
    parseInt(rgb[3], 16) + ',0.5)' :
    null;
}

export function colorToHex(color: string) {
  // 如果颜色是 RGB 格式
  if (color.startsWith("rgb")) {
    const rgba: any = color.match(/\d+/g);
    const r = parseInt(rgba[0]);
    const g = parseInt(rgba[1]);
    const b = parseInt(rgba[2]);
    return "#" + ((1 << 24) + (r << 16) + (g << 8) + b).toString(16).slice(1);
  }
  // 如果颜色是 RGBA 格式
  else if (color.startsWith("rgba")) {
    const rgba: any = color.match(/\d+(\.\d+)?/g);
    const r = parseInt(rgba[0]);
    const g = parseInt(rgba[1]);
    const b = parseInt(rgba[2]);
    return "#" + ((1 << 24) + (r << 16) + (g << 8) + b).toString(16).slice(1);
  }
  // 其他情况返回原始值
  else {
    return color;
  }
}
export function isTibetan(char:string) {
  const code = char.charCodeAt(0);
  return (code >= 0x0F00 && code <= 0x0FFF);
}


export function extractNumber(str:string) {
  // 使用正则表达式匹配数字，包括小数点后的数字
  let match = String(str).match(/[-+]?\d+(\.\d+)?/);
  // 如果匹配成功，返回匹配到的数字字符串，否则返回NaN或你想要的默认值
  return match ? parseFloat(match[0]) : NaN; // 或者返回null、0或其他默认值
}
