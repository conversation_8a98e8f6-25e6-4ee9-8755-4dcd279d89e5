import Box from "./Box";
import BoxField from "./BoxField";
import Cell, { HeaderFooterType } from "./Cell";
import Character from "./Character";
import { Config, editor_prompt_msg, fontHeightAsc } from "./Config";
import { default_para_style } from "./ContextState";
import Editor from "./Editor";
import Font from "./Font";
import fontFaceObj from "./FontFaceObj";
import Footer from "./Footer";
import Group from "./Groups";
import Header from "./Header";
import { isInPointerDownMobileEdit } from "./Mobile"
import {
  forEachFlatParagraph,
  getCellIndex,
  // getHeaderFieldMaxLength,
  handleCopyData,
  handleCopyDataToParas,
  // handleGroupsHeaderInfo,
  initEditor,
  initPrintEditor,
  insertParagraphsHelper,
  insertTextAfterCursorSet,
  isButton,
  isCell,
  isGroup,
  isImage,
  isWidget,
  isBoxField,
  specialCharHandle,
  ReplaceContainerType,
  isFraction,
  getMaxScrollVal,
} from "./Helper";
import Image from "./Image";
import {
  cursorType,
  fictitiousShadow,
  getFocusImage,
  getShadowChange,
  isInImageRect,
  pointerUpImageChange,
  pointerUpMarkChange,
  setFocusImage,
  setImageParaPath,
  setShadowChange,
} from "./ImageEditing";
import Line from "./line";
import Page from "./Page";
import {
  changeCanvasSize,
  compareJsonIgnoreKey,
  deepClone,
  getRawDataByConfig,
  handleLowerVersionBrowserPaste,
  handleSaveFontFace,
  isCharacter,
  isField,
  isPage,
  isParagraph,
  isRow,
  isTable,
  replaceLineBreakWith,
  root_node,
  serializeCopy,
  useLowerVersionCopyPaste,
  useRawDataByConfig,
  uuid,
  versionDiff,
  isNumber,
  uncompressData,
  isPointInRectangle,
  arrDeduplication,
  sort,
  isNotNullAndUndefined,
  getLCM,
  keepDecimal,
  getInfoFromString,
  calculateRotatedRectSize,
} from "./Utils";
import Paragraph from "./Paragraph";
import ParaStyle, { ParagraphStyle } from "./ParagraphStyle";
import PathUtils, { Path } from "./Path";
import Renderer from "./Renderer";
import Row from "./Row";

import XSelection from "./Selection";
import Shape, { shapeType } from "./shape";
import Table from "./Table";
import Widget from "./Widget";
import XField from "./XField";
import { FieldShowMode, GetDataType } from "./EditorConfig";
import WaterMark from "./WaterMark";
import {
  alignType,
  Direction,
  ElementInParagraph,
  FieldType,
  HightLightParameter,
  insertType,
  M2WParameter,
  pageDirection,
  RowLineType,
} from "./Definition";
import Search from "./Search";
import Button from "./Button";
import {
  COMMENT_LIST_ITEM_SPACE_HEIGHT,
  COMMENT_LIST_ITEM_TITLE_HEIGHT,
  COMMENT_LIST_MIN_WIDTH,
  COMMENT_SCROLL_BAR_WIDTH,
  COMMENT_SWITCH_WIDTH,
  COMMENT_SWITCH_DISTANCE_TO_CROSS,
  COMMENT_TITLE_CROSS_HEIGHT,
  COMMENT_TITLE_CROSS_TOP,
  COMMENT_TITLE_CROSS_WIDTH,
  COMMENT_TITLE_PADDING_RIGHT,
  COMMENT_WIDTH,
  DISTANCE_TO_LINE,
  PIXEL_CONVERSION_FORMULA_RATIO,
  ShapeMode,
  TABLE_NAME_FOR_PACS_IMAGE_LAYOUT,
  VerticalAlign,
  ViewMode,
  COMMENT_SCROLL_MARGIN,
  TypeInImageMeta,
  SkipMode,
} from "./Constant";
import ImageMap from "./ImageMap";
import EditorLocalTest from "../../localtest";
import Comment from "./Comment";
import FloatModel from "./FloatModel";
import { contentChanged } from "./Decorator";
import { isInArc } from "./ImageEditing";
import CommentBox from "./CommentBox";

export default class EditorHelper {

  static caretIsInViewport({ editor }: {editor: Editor}) {
    let left = editor.caret.x;
    let top = editor.caret.y;
    let bottom = top + editor.caret.height;
    const RANGE = 30; // 一个范围值 不能简单的用 0 或者 canvasDOMRect 的宽度来计算 需要有一个小的范围 避免体验不好 如果写 50 的话 在手机上 删除的时候会有问题
    const BOUNDARY = 30; // 边界值 如果没有这个的话 就太挨着边了 看不出来光标了都
    const CARET_WIDTH = 20;

    const xRelativeDom = (left + (editor.offsetX / editor.viewScale)) * editor.viewScale;
    const topRelativeDom = (top + (editor.offsetY / editor.viewScale)) * editor.viewScale;
    const bottomRelativeDom = (bottom + (editor.offsetY / editor.viewScale)) * editor.viewScale;
    const canvasDOMRect = editor.init_canvas.getBoundingClientRect();
    // 一旦换行 editor.caret.y === editor.caret.preY 是 false 一直在同一行的话 就是 true
    if (xRelativeDom < RANGE) { // 删除内容
      if (editor.caret.y === editor.caret.preY) {
        editor.offsetX += (Math.abs(editor.caret.x - editor.caret.preX) + BOUNDARY * editor.viewScale)
      } else {
        editor.offsetX += Math.abs(xRelativeDom - canvasDOMRect.left) + BOUNDARY * editor.viewScale
      }
    } else if (xRelativeDom > canvasDOMRect.width - RANGE) { // 输入内容
      if (editor.caret.y === editor.caret.preY) {
        editor.offsetX -= (Math.abs(editor.caret.preX - editor.caret.x) * editor.viewScale + BOUNDARY);
      } else { // 此时换行了 删除内容了
        editor.offsetX -= (Math.abs(xRelativeDom - canvasDOMRect.right) + BOUNDARY * editor.viewScale)
      }
    }
    if (topRelativeDom < RANGE) { // 删除
      editor.offsetY += Math.abs(topRelativeDom) + BOUNDARY;
    } else if (bottomRelativeDom > canvasDOMRect.height - RANGE) { // 回车换行
      if (editor.caret.y !== editor.caret.preY) {
        editor.offsetY -= (Math.abs(editor.caret.y - editor.caret.preY) * editor.viewScale + BOUNDARY);
      } else {
        editor.offsetY -= (Math.abs(bottomRelativeDom - canvasDOMRect.bottom) + BOUNDARY * editor.viewScale);
        // 相等的时候就是输入法干涉了
      }
    }
    function getOffsetYRange() {
      const max = 0;
      let min =
        ((editor.pages[editor.pages.length - 1].bottom +
          editor.config.page_margin_bottom) *
          editor.viewScale -
          parseInt(editor.init_canvas.style.height)) *
        -1;
      min = Math.ceil(min);// 如果不用 Math.ceil 的话 分组很多 定位到最后一个分组 就会导致绘制大片灰色区域 在视口内的判断就不对了 返回了 false 就不对了
      return {
        max,
        min,
      };
    }
    const { max, min } = getOffsetYRange();
    if (editor.offsetY < min) {
      editor.offsetY = min;
    } else if (editor.offsetY > max) {
      editor.offsetY = max;
    }
  }

  // 表格相关 ↓
  static appendDataToTable(parameter: {
    editor: Editor,
    id?: string;
    name?: string;
    tables?: Table[];
    data: any[];
  }) {
    let { editor, id, name, tables, data } = parameter;
    tables = tables || [];
    if (id) {
      const table = editor.getTableById(id);
      table && tables.push(table);
    }
    if (name) {
      const tbls = editor.getTablesByName(name);
      tbls.length > 0 && tables.push(...tbls);
    }

    for (const table of tables) {
      const excludes = table.getColsByContent("\n");
      table.append([...data], excludes);
    }
    editor.refreshDocument();
    return true;
  }

  static swapCells(c1: Cell, c2: Cell) {
    // 只需要改三个东西
    // 1. paragraph
    // 2. row
    // 3. fields
    // 这三个东西上边有关cell的属性也全部都要更换
    const c1Paragraph = [...c1.paragraph];
    const c1Children = [...c1.children];
    const c1Fields = [...c1.fields];

    c1.paragraph = c2.paragraph;
    c1.children = c2.children;
    c1.fields = c2.fields;
    c1.paragraph.forEach((paragraph) => ((paragraph as Paragraph).cell = c1));
    c1.handleFieldsAssignment(c1.fields);
    c1.typesetting();

    c2.paragraph = c1Paragraph;
    c2.children = c1Children;
    c2.fields = c1Fields;
    c2.paragraph.forEach((paragraph) => ((paragraph as Paragraph).cell = c2));
    c2.handleFieldsAssignment(c2.fields);
    c2.typesetting();
  }

  static getTblByPointIsOnHorizontalLine(
    { editor, x, y }: {editor: Editor, x: number, y: number}
  ): { tbl: Table | undefined; line: number | undefined } {
    y += editor.scroll_top;

    for (let i = 0; i < editor.pages.length; i++) {
      const page = editor.pages[i];

      if (page.contain(x, y)) {
        let offset_x = x - page.left;
        let offset_y = y - page.top;

        for (let j = 0; j < page.children.length; j++) {
          const child = page.children[j];

          if (isTable(child) && child.contain(offset_x, offset_y)) {
            offset_x = offset_x - child.left;
            offset_y = offset_y - child.top; // 现在的offset_y 就是光标距离该表格顶部的距离了

            for (let k = 0; k < child.children.length; k++) {
              const cell = child.children[k]; // cell是本拆分以后的单元格 不是大单元格

              if (cell.contain(offset_x, offset_y)) {
                // 光标在单元格里边
                // xi 理论上这三个判断就可以了 如果还有bug 就注释掉这个  解开下方的注释
                if (cell.start_row_index > 0) {
                  if (offset_y - cell.top <= 2.5) {
                    // 在线的下边 也就是该单元格里边 上部分的时候
                    const tbl = child.origin || child;
                    const line = cell.origin?.start_row_index
                      ? cell.origin.start_row_index - 1
                      : cell.start_row_index - 1;
                    return { tbl, line };
                  }
                  if (cell.bottom - offset_y <= 2.5) {
                    // 在线的上边 也就是该单元格里边 下部分的时候
                    const tbl = child.origin || child;
                    const line =
                      cell.origin?.end_row_index ?? cell.end_row_index;
                    return { tbl, line };
                  }
                } else {
                  if (cell.bottom - offset_y <= 2.5) {
                    // 如果起始行为0了 那么必须得是点下边 在线的上边 也就是该单元格里边 下部分的时候
                    const tbl = child.origin || child;
                    const line =
                      cell.origin?.end_row_index ?? cell.end_row_index;
                    return { tbl, line };
                  }
                }
                break;
              }
            }
          }
        }
      }
    }
    return { tbl: undefined, line: undefined };
  }

  // 表格相关 ↑

  static fieldsShowOrHide({ editor, fields, display = true }: {editor: Editor, fields: XField[], display: Boolean}) {
    for (let i = 0; i < fields.length; i++) {
      const field = fields[i];
      if (display) {
        if (field.meta.before_type && field.meta.before_type !== "anchor") {
          field.type = field.meta.before_type
        } else {
          field.type = "normal"
        }
      } else {
        field.type = "anchor"
      }
      editor.reviseFieldAttr({ field: field });
    }
  }

  static handleAggregationMode({ editor, cell }: {editor: Editor, cell: Cell}) {
    // 设置聚合模式的单元格里边的行数应该跟 rowspan 的数量保持一致
    // 此时还没进行 update 还没有进行分页 所以就只按照 modelData 来处理就可以
    const aggCell = cell.getOrigin(); // 聚合单元格
    const table = aggCell.parent;
    if (table && aggCell.aggregationMode) {
      if (aggCell.colspan > 1) {
        return
      }
      let end = aggCell.end_row_index;
      const startCol = aggCell.start_col_index;
      const endCOl = aggCell.end_col_index;
      const restRowSize = table.row_size.length - aggCell.start_row_index
      if (aggCell.children.length > aggCell.rowspan && aggCell.rowspan < restRowSize) {
        // 此时应该合并
        // 但是合并几个单元格不知道 需要计算
        const mergeCellNum = aggCell.children.length - aggCell.rowspan;
        const startColIndex = aggCell.start_col_index;
        let index = aggCell.index;
        let num = 0;
        const focus = [...editor.selection.focus];
        for (let i = index + 1; i < table.children.length; i++) {
          const c = table.children[i];
          if (c.start_col_index === startColIndex && c.start_row_index === end + 1) {
            if (c.colspan > 1 || c.rowspan > 1) return
            editor.selection.setSelectionByPath([table.cell_index, i, 0, 0], [table.cell_index, i, c.children.length - 1, c.children[c.children.length - 1].children.length || 1], "model_path");
            const rawData = editor.getRawDataBySelection();
            const obj = {} as Cell;
            Cell.attrJudgeUndefinedAssign(obj, c);
            (rawData as any)["cellAttr"] = obj;
            const id = aggCell.id;
            if (!editor.document_meta.meta) {
              editor.document_meta["meta"] = {
                [id]: [rawData]
              };
            } else if (!editor.document_meta.meta[id]) {
              editor.document_meta["meta"][id] = [rawData];
            } else {
              editor.document_meta["meta"][id].push(rawData);
            }
            end++
            index = i;
            c.clear();
            if (++num >= mergeCellNum) {
              break;
            }
          }
        }
        editor.selection.setSelectionByPath([table.cell_index, aggCell.index, 0, 0], [table.cell_index, index, 0, 0], "model_path");
        editor.mergeCell();
        editor.selection.setCursorPosition(focus);
        table.children.forEach((c) => c.typesetting());
        editor.refreshDocument();
      } else if (aggCell.children.length < aggCell.rowspan) {
        let splitNum = aggCell.rowspan - aggCell.children.length;
        let rowIndex = end
        for (let i = 0; i < splitNum; i++) {
          const newCell = new Cell(
            editor,
            [rowIndex, aggCell.start_col_index],
            1,
            1,
            table
          );
          rowIndex--;
          const rawData = editor.document_meta?.meta?.[aggCell.id]?.pop();
          if (rawData) {
            if (rawData.cellAttr) {
              Cell.attrJudgeUndefinedAssign(newCell, rawData.cellAttr);
            }
            editor.internal.imageSrcObj = rawData.imageSrcObj || {};
            rawData.content.forEach((raw: any, i: number, array: any[]) => {
              newCell.insert_raw(raw, array[i + 1]);
            });
          } else {
            newCell.insertEmptyParagraph();
          }
          table.children.push(newCell);
          aggCell.rowspan--;
        }


        table.children.forEach((c) => c.typesetting());
        table.sortingCells();
        let rowNum = aggCell.end_row_index + 1;
        for (let j = 0; j < splitNum; j++) {
          for (let i = 0; i < table.notAllowDrawLine.row.length; i++) {
            const arr = table.notAllowDrawLine.row[i];
            if (arr[0] === rowNum && (arr[1] === startCol || arr[1] === endCOl)) {
              table.notAllowDrawLine.row.splice(i, 1);
              i--;
            }
          }
          rowNum++;
        }
        editor.refreshDocument();
      }
    }
  }

  static adminRemoveFieldsKeepCursor({ editor, fields }: {editor: Editor, fields: (XField | BoxField)[]}) {
    editor.adminMode = true;
    const originAnchor = [...editor.selection.anchor];
    editor.removeFields(fields);
    editor.selection.setCursorPosition(originAnchor);
    editor.refreshDocument();
    editor.adminMode = false;
  }

  static handleFieldNewTextByReplaceRule({ field, replaceRule }: {field: XField, replaceRule: any}) {
    replaceRule.forEach((regOb: any) => {
      const reg = new RegExp(regOb.rule, !regOb.flags ? "g" : "");
      if (isNotNullAndUndefined(field.new_text)) {
        field.new_text = String(field.new_text);

        if (regOb.replace === "*" && field.new_text) {
          const r = new RegExp(regOb.rule);
          const a = field.new_text.match(r);
          if (a && a.length > 1) {
            field.new_text = a.slice(1).join("#")
            return
          }
          const arr = field.new_text.match(reg);
          if (arr?.length) {
            field.new_text = arr.join("#");
            return
          }
        }

        let replaceWithCapturedGroup = regOb.replace.replace(/(?<!\\)\*/g, "$$&"); // 只把*替换掉 \* 这种的要保留
        if (regOb.custom && reg.source === "\n") {
          // 处理flags为2的情况(替换最后一个匹配)
          if (regOb.flags === 2) {
            const matches = field.new_text.match(new RegExp(regOb.rule, "g"));
            if (matches && matches.length > 0) {
              const lastMatch = matches[matches.length - 1];
              const lastIndex = field.new_text.lastIndexOf(lastMatch);
              field.new_text =
                field.new_text.substring(0, lastIndex) +
                field.new_text.substring(lastIndex).replace(new RegExp(regOb.rule), regOb.replace);
            }
          } else {
            field.new_text = field.new_text.replace(
              reg.source,
              replaceWithCapturedGroup
            );
          }
        } else {
          // 处理flags为2的情况(替换最后一个匹配)
          if (regOb.flags === 2) {
            const matches = field.new_text.match(new RegExp(regOb.rule, "g"));
            if (matches && matches.length > 0) {
              const lastMatch = matches[matches.length - 1];
              const lastIndex = field.new_text.lastIndexOf(lastMatch);
              field.new_text =
            field.new_text.substring(0, lastIndex) +
            field.new_text.substring(lastIndex).replace(new RegExp(regOb.rule), regOb.replace);
            }
          } else {
            field.new_text = field.new_text.replace(
              reg,
              replaceWithCapturedGroup
            );
          }
        }
        field.new_text = field.new_text.replace(/\\\*/g, "*")
      }
    });
  }

  static setFieldReadonly(
    { editor, isReadonly,cascade = false, field }: {
      editor: Editor,
      isReadonly: boolean,
    cascade?: boolean,
    field?: XField | BoxField
    }
  ) {
    const focus_field = field || editor.selection.getFocusField();
    if (isBoxField(focus_field)) {
      return BoxField.setDisabled(editor, isReadonly, focus_field);
    }
    if (isField(focus_field)) {
      return XField.setReadonly(editor, isReadonly, cascade, focus_field);
    }
  }

  static deleteGroup({ editor, target_group }: {editor: Editor, target_group?: Group}) {
    if (Array.isArray(target_group)) {
      let res = false;
      for (let i = 0, len = target_group.length; i < len; i++) {
        const group = target_group[i];
        res = Group.delete(editor, group);
      }
      return res;
    } else {
      return Group.delete(editor, target_group);
    }
  }

  static isGroupHeaderInfoDiff({ editor, obj }: {editor: Editor, obj: any}) {
    let res: any;
    const groups = editor.getAllGroup();
    for (const group of groups) {
      for (const name in obj) {
        if (group.header_info[name] !== obj[name]) {
          if (res) {
            res[name] = obj[name];
          } else {
            res = {
              name: obj[name],
            };
          }
        }
      }
    }
    return res;
  }

  static getParaIndexWithKeyword(keywords: string[], paras: (Paragraph | Table)[]) {
    // 开始段落下标
    let sParaIndex = -1
    // 结束段落下标
    let eParaIndex = -1
    for (let i = 0; i < paras.length; i++) {
      const p = paras[i]
      // getStr 参数 true表示获取不包含文本域边框字符及背景文本的所有普通字符,然后去除所有空格与换行符
      const text = p.getStr(true).replace(/\s*/g, '').replace(/\n/g, '')

      if (text === keywords[0]) {
        sParaIndex = i
      }
      if (text.indexOf(keywords[1]) > -1) {
        eParaIndex = i
        break
      }
      if (text === keywords[2]) {
        eParaIndex = i
        break
      }
    }
    return {
      sParaIndex,
      eParaIndex
    }
  }

  static changeContentAlign({ editor, direction }: {editor: Editor, direction: alignType}) {
    editor.changeAlign(direction);
    editor.update();
    editor.scroll_by_focus();
    editor.render();
    return true;
  }

  static  refreshDocument({ editor, isForce = false }: {editor: Editor, isForce?: boolean}) {
    if (isForce) {
      const raw_data = editor.getRawData();
      editor.reInitRaw(raw_data, false);
    }
    editor.update();
    editor.render();
  }

  static updateFormatParagraph({ editor }: {editor: Editor}) {
    for (let i = 0; i < editor.current_cell.paragraph.length; i++) {
      const currentParagraph = editor.current_cell.paragraph[i];
      if (isParagraph(currentParagraph) && currentParagraph.itemsWidth[0] === "x" && currentParagraph.characters.length > 1) {
        const paragraphs = editor.findContinuousParagraphs([currentParagraph]);
        editor.formatParagraph(paragraphs, undefined, undefined, false);
        i = paragraphs[paragraphs.length - 1].para_index + 1;
      }
    }
  }

  static getViewPositionByAbsolutePosition({ editor, absolute_x, absolute_y }: {editor: Editor, absolute_x: number, absolute_y: number}) {
    const x =
      Math.round(
        (absolute_x + editor.internal.view_scale_offset) * editor.viewScale * 10
      ) / 10;
    const y = Math.round(absolute_y * editor.viewScale * 10) / 10;
    return { x, y };
  }

  static toggleFloatModelMode({ editor, open = true }: {editor: Editor, open?: boolean}) {
    editor.editFloatModelMode = open;
    editor.currentFloatModel = editor.floatModels[0];
    if (editor.currentFloatModel) {
      editor.updateCaret();
      editor.refreshDocument();
    } else {
      editor.root_cell.lock = true;
    }
  }

  static getNeedXYbyXY({ editor, param_x, param_y, type = 1 }: {editor: Editor, param_x: number, param_y: number, type?: number}) {
    let x = param_x;
    let y = param_y;
    if (editor.isMobileTerminal()) {
      x = x / editor.viewScale - editor.offsetX / editor.viewScale;
      y = y / editor.viewScale - editor.offsetY / editor.viewScale;
      x = keepDecimal(x, 3);
      y = keepDecimal(y, 3);
      return { x, y };
    }
    switch (type) {
      // 鼠标点击处距canvas左上角canvas外部的xy转换为绘制到canvas内的xy，param_y为相对于canvas最上方的y
      case 1:
        x = param_x / editor.viewScale - editor.internal.view_scale_offset;
        y = param_y / editor.viewScale;
        break;
      // 鼠标点击处canvas内xy转换为距canvas左上角的xy，param_y为相对于canvas最上方的y
      case 2:
        x = (param_x + editor.internal.view_scale_offset) * editor.viewScale;
        y = param_y * editor.viewScale;
        break;
      // 鼠标点击处距屏幕左上的位置转换为canvas内的xy,param_y为相对于屏幕最上方的y
      case 3:
        x =
          (param_x - editor.init_canvas.getBoundingClientRect().left) /
          editor.viewScale -
          editor.internal.view_scale_offset;
        y =
          (param_y - editor.init_canvas.getBoundingClientRect().top) /
          editor.viewScale;
        break;
      // 鼠标点击处canvas内xy转换为距距屏幕左上的xy，param_y为相对于屏幕最上方的y
      case 4:
        x =
          (param_x + editor.internal.view_scale_offset) * editor.viewScale +
          editor.init_canvas.getBoundingClientRect().left;
        y =
          param_y * editor.viewScale +
          editor.init_canvas.getBoundingClientRect().top;
        break;
      default:
        break;
    }
    x = keepDecimal(x, 3);
    y = keepDecimal(y, 3);
    return { x, y };
  }

  static formulaMode({ editor, is_mode, name = null }: {editor: Editor,is_mode: boolean, name?: any}) {
    editor.formula_mode = is_mode;
    if (name && is_mode) {
      editor.internal.formula_name = name
    } else if (!is_mode) {
      editor.internal.formula_name = null
    }
    editor.update();
    editor.render();
  }


  static getFirstCharacter({ editor }: {editor: Editor}): Character | undefined {
    const firstParaOrTable = editor.root_cell.paragraph[0];
    if (isParagraph(firstParaOrTable)) {
      for (const c of firstParaOrTable.characters) {
        if (isCharacter(c)) {
          return c;
        }
      }
    } else if (isTable(firstParaOrTable)) {
      for (const cell of firstParaOrTable.children) {
        for (const para of cell.paragraph) {
          for (const c of (para as Paragraph).characters) {
            if (isCharacter(c)) {
              return c;
            }
          }
        }
      }
    }
  }

  static m2w(parameter: M2WParameter) {
    parameter = { header: true, root: true, footer: false, tableType: 0, ...parameter };
    const { header, root, footer, editor } = parameter;
    let headerData = [];
    if (header) {
      headerData = editor.header_cell.getM2WResult(parameter)
    }
    let contentData = [];
    if (root) {
      contentData = editor.root_cell.getM2WResult(parameter);
    }
    let footerData = [];
    if (footer) {
      footerData = editor.footer_cell.getM2WResult(parameter);
    }
    const res = [...headerData, ...contentData, ...footerData];
    return res;
  }

  static mountInputDOM(parameter: {editor: Editor}) {
    const { editor } = parameter;
    const wrapElement = document.getElementById(editor.editorId || "");
    const input = editor.getInputDOM();

    if (wrapElement && !input && editor.inputDOM) {
      wrapElement.appendChild(editor.inputDOM);
      const input = editor.inputDOM;
      setTimeout(() => {
        input.focus();
      });
      return true;
    }
    return false;
  }

  static removeInputDOM(parameter: {editor: Editor}) {
    const { editor } = parameter;
    const input = editor.getInputDOM();
    if (input) {
      const parent = input.parentNode;
      if (parent) {
        parent.removeChild(input);
        return true;
      }
    }
    return false;
  }

  static getCellByRawNodeList(
    parameter: {editor: Editor, nodeList: any, cell: Cell}
  ): Cell | undefined {
    const { editor, nodeList, cell } = parameter
    editor.internal.transformData = {
      images: [],
      fields: [],
    };
    for (let i = 0; i < nodeList.length; i++) {
      const node = nodeList[i];
      const nextNode = nodeList[i + 1];
      if (nextNode && node.type === "table" && nextNode.type === "table") {
        // 避免两个连续表格报错
        editor.event.emit("message", {
          type: "warning",
          msg: `有两个连续的表格,请修改数据`,
        });
        return;
      }
      editor.event.emit("transCellInsertRaw", {
        cell,
        node,
        nextNode,
      });
    }
    return cell;
  }

  static setCellsGroupKey(parameter: {cells: Cell[][], groupKey: string}) {
    const { cells, groupKey } = parameter;
    for (const arr of cells) {
      const t = arr[0].parent;
      if (isTable(t)) {
        t.setCellsGroupKey(arr, groupKey);
      }
    }
  }

  static setCellsGroupBySelection(parameter: {editor: Editor, groupKey: string}) {
    const { editor, groupKey } = parameter;
    const newCells = editor.internal.getSelectedCellsBySameTable();
    this.setCellsGroupKey({ cells: newCells, groupKey });
    editor.event.emit("message", {
      type: "success",
      msg: `设置成功`,
    });
  }

  static cancelCellsGroupBySelection(parameter: {editor: Editor}) {
    const { editor } = parameter;
    const newCells = editor.internal.getSelectedCellsBySameTable();
    this.setCellsGroupKey({ cells: newCells, groupKey: "" });
    editor.event.emit("message", {
      type: "success",
      msg: `取消成功`,
    });
  }

  static editorConstructor(editor: Editor, config: any) {
    if (config) {
      editor.config.init(config);
    }
    const root_cell = new Cell(
      editor,
      root_node.pos,
      root_node.colspan,
      root_node.rowspan,
      null,
      uuid("content")
    );

    const header_cell = new Cell(
      editor,
      root_node.pos,
      root_node.colspan,
      root_node.rowspan,
      null,
      uuid("header"),
      "header"
    );
    const footer_cell = new Cell(
      editor,
      root_node.pos,
      root_node.colspan,
      root_node.rowspan,
      null,
      uuid("footer"),
      "footer"
    );
    editor.raw = editor.config.rawData;
    return { root_cell, header_cell, footer_cell };
  }

  // 创建初始化的页面 在下边 init 方法里边调用了
  static createInitPage(editor: Editor): Page {
    const page = new Page(
      editor.page_left,
      editor.config.editor_padding_top, // 该页距离canvas最顶部边界的距离
      editor.page_size.width, // 该页的宽
      editor.page_size.height, // 该页的高
      editor
    );

    page.children.push(editor.root_cell.children[0]);
    editor.root_cell.children[0].top =
      page.header.header_outer_bottom + editor.config.content_margin_header;
    page.footer.footer_cell = editor.footer_cell; // new Page 的时候只处理了 header 所以这里不赋值的话 双击页脚会报错
    page.number = 1; // 少了这个双击页眉会报错
    return page;
  }

  /**
   * 初始化方法，程序启动时只需要执行一次，用于解析初始数据，准备其他必要数据
   * @param canvas 绘制内容的canvas dom
   * @param input 代理事件的input dom
   */
  static init(
    editor: Editor,
    canvas: HTMLCanvasElement,
    input: HTMLTextAreaElement,
    div: HTMLElement
  ) {
    editor.internal.originConfigItems = JSON.parse(
      JSON.stringify(editor.config)
    );
    Renderer.init(editor, canvas);
    editor.init_canvas = canvas;
    editor.internal.inputAdapter = input;
    editor.internal.markInput = div;
    canvas.style.cursor = "text";
    editor.internal.client_top = canvas.getBoundingClientRect().top;
    editor.internal.client_left = canvas.getBoundingClientRect().left;

    // 去掉 reInitRaw(editor, editor.raw) 改为插入空段
    editor.root_cell.appendEmptyPara();
    editor.header_cell.appendEmptyPara();
    editor.footer_cell.appendEmptyPara();

    if (
      navigator.userAgent.match(
        /(phone|pad|pod|iPhone|iPod|ios|iPad|Android|Mobile|BlackBerry|IEMobile|MQQBrowser|JUC|Fennec|wOSBrowser|BrowserNG|WebOS|Symbian|Windows Phone)/i
      )
    ) {
      editor.autoChangeViewScale();
    }
    const page = this.createInitPage(editor);
    editor.pages.push(page);
    editor.focus();
  }

  // static reInitRaw (editor: Editor, rawData: any, isClearHistory = true) {
  //   editor.fontMap.clear();
  //   editor.internal.transformData = {
  //     images: [],
  //     fields: []
  //   };
  //   // editor.imageMap.clear(); //不清空图片，因为清空后撤销时图片会重新加载造成闪烁
  //   rawData = useRawDataByConfig(rawData);
  //   if (isClearHistory) {
  //     editor.history.clear();
  //   }
  //   const rawConfig = rawData.config;
  //   if (rawConfig) {
  //     // 该判断中 之所以要判断是否为 undefined 是因为不考虑切换病历的情况 就第一次加载的时候 有可能没有就走配置的(后来的数据都加上了应该没这事了)

  //     if (rawConfig.header_horizontal !== undefined) {
  //       editor.config.show_header_line = rawConfig.header_horizontal;
  //     }

  //     if (rawConfig.rowLineType !== undefined) {
  //       editor.config.rowLineType = rawConfig.rowLineType;
  //     } else {
  //       editor.config.rowLineType = RowLineType.VOID; // 不让配置了 想改 只能通过接口
  //     }
  //   }

  //   // 如果当前是编辑页眉页脚状态，则先退出
  //   if (editor.is_edit_hf_mode) {
  //     editor.exitEditHfMode(false);
  //   }

  //   editor.contextState.resetFontState();
  //   editor.pages = [];
  //   editor.raw = rawData;
  //   editor.internal.imageSrcObj = rawData.imageSrcObj ?? {};
  //   editor.document_meta = editor.raw.meta ?? {};
  //   // 将版本号写入 document_meta 跟处理版本兼容这两行代码就要放在 document_meta 赋值之后生成数据之前，否则可能会没有 versionList 没有版本号
  //   this.recordVersionInfo(editor);
  //   this.handleCompatibilityProblemByVersion(editor);
  //   editor.custom_meta = editor.raw.customMeta ?? {};
  //   if (editor.raw.config) {
  //     // 设置页面大小和方向
  //     editor.config.setPageSize(editor.config.page_size_type, editor.raw.config.direction, editor.raw.config.page_info?.page_size);
  //   } else if (editor.config.page_direction !== "vertical") {
  //     // 如果文档中没有配置的横向纵向信息，且当前页面方向与默认方向不同，则还原页面方向为纵向
  //     editor.config.setPageSize(editor.config.page_size_type, "vertical", editor.raw.config.page_info?.page_size);
  //   }

  //   const pageSize = editor.config.getPageSize();
  //   editor.page_size.width = pageSize.width;
  //   editor.page_size.height = pageSize.height;
  //   if (editor.raw.fontMap) {
  //     for (const id in editor.raw.fontMap) {
  //       const style = editor.raw.fontMap[id];
  //       editor.fontMap.add(style, id);
  //     }
  //   }
  //   // TODO 考虑同attach里用使用异步
  //   editor.root_cell = new Cell(
  //     editor,
  //     root_node.pos,
  //     root_node.colspan,
  //     root_node.rowspan,
  //     null,
  //     uuid("content")
  //   );
  //   editor.header_cell = new Cell(
  //     editor,
  //     root_node.pos,
  //     root_node.colspan,
  //     root_node.rowspan,
  //     null,
  //     uuid("header"),
  //     "header"
  //   );
  //   editor.footer_cell = new Cell(
  //     editor,
  //     root_node.pos,
  //     root_node.colspan,
  //     root_node.rowspan,
  //     null,
  //     uuid("footer"),
  //     "footer"
  //   );

  //   editor.raw.groups.forEach((group: any) => {
  //     editor.root_cell.insertGroup(group);
  //   });
  //   editor.raw.header.forEach((raw: any, index: number, array: []) => {
  //     editor.header_cell.insert_raw(raw, array[index + 1]);
  //   });
  //   editor.raw.content.forEach((raw: any, index: number, array: []) => {
  //     editor.root_cell.insert_raw(
  //       raw,
  //       array[index + 1],
  //       editor.raw.groups
  //     );
  //   });
  //   // 因为内容可能会超出一页,所以页脚需要特殊处理
  //   editor.raw.footer.forEach((raw: any, index: number, array: []) => {
  //     editor.footer_cell.insert_raw(raw, array[index + 1]);
  //   });
  //   if (editor.current_cell.hf_part) {
  //     editor.current_cell = editor[
  //       (editor.current_cell.hf_part + "_cell") as "header_cell" | "footer_cell"
  //     ];
  //   } else {
  //     editor.current_cell = editor.root_cell;
  //   }

  //   // TODO 简化代码
  //   if (editor.raw.shapes && editor.raw.shapes.length) {
  //     editor.shapes = [...Shape.getShapesByRawData(editor.raw.shapes)];
  //   } else {
  //     editor.shapes = [];
  //   }
  //   if (editor.raw.waterMarks && editor.raw.waterMarks.length) {
  //     editor.waterMarks = [...WaterMark.getDataByRawData(editor)];
  //   } else {
  //     editor.waterMarks = [];
  //   }
  //   // 初始设置光标位置在开始位置
  //   editor.selection.setCursorByRootCell("start");

  //   // 处理浮动模型
  //   editor.raw.floatModelRaws?.forEach((floatModel: any) => {
  //     const floatCell = editor.initFloatModel(editor, floatModel.width, floatModel.height, floatModel.originPosition); // new FloatModel(editor, floatModel.width, floatModel.height, floatModel.originPosition); // editor.createFloatModel(floatModel.width, floatModel.height, floatModel.originPosition)!;
  //     floatModel.data.forEach((it: any, index: number, array: any[]) => {
  //       floatCell.insert_raw(it, array[index + 1]);
  //     });
  //     editor.floatModels.push(floatCell);
  //   });
  //   editor.editFloatModelMode = false;
  //   editor.event.emit("reInitRaw");
  // }

  /**
   *
   * @param editor
   * @param x // 此处的 x y 是相对于 canvas 原点的位置
   * @param y
   * @param hold_shift
   * @returns
   */
  static pointerDown(
    editor: Editor,
    x: number,
    y: number,
    hold_shift: Boolean = false
  ) {
    // const result = editor.clickScrollBar(x, y); // TODO 是不是应该放到该方法的最开头

    // if (result) {
    //   editor.hold_mouse = true;
    //   return;
    // }

    editor.holdRotateIcon = false;

    // TODO 先在这儿转换 x 和 y 转成相对于 浮动模型的位置
    editor.internal.currentClickCommentPage = undefined;
    editor.internal.commentCurrentPage = undefined;
    if (editor.isMobileTerminal()) {
      if (editor.internal.VL.is_mobile_selection) {
        editor.selection.clearSelectedInfo()
      } else {
        setTimeout(() => {
          editor.getInputDOM()?.blur()
          editor.getInputDOM()?.focus()
        });

      }
      if (editor.internal.is_mobile_edit) {
        const result = isInPointerDownMobileEdit(x, y, editor)

        if (result) {
          editor.internal.VL.is_mobile_button = true;
          return
        }
      }

    }
    let clickFloatModel = false;

    editor.internal.is_drag_mobile = false
    editor.internal.VL.is_mobile_selection = false
    if (editor.editFloatModelMode) {
      for (const floatModel of editor.floatModels) {
        const res = isInImageRect(
          x,
          y,
          floatModel.originPosition[0] || 0,
          floatModel.originPosition[1] || 0,
          floatModel.width || 0,
          floatModel.height || 0
        );
        if (res !== undefined) {
          editor.internal.onFloatPoint = res;
          editor.currentFloatModel = floatModel;
          editor.internal.onFloatLine = false;
          clickFloatModel = true;
        } else if (
          Shape.isInLine(
            {
              x: floatModel.originPosition[0],
              y: floatModel.originPosition[1],
            },
            {
              x: floatModel.originPosition[0] + floatModel.width,
              y: floatModel.originPosition[1],
            },
            x,
            y,
            5
          )
        ) {
          editor.currentFloatModel = floatModel;
          clickFloatModel = true;
          editor.internal.onFloatLine = true;
          editor.internal.dx = x - floatModel.originPosition[0];
          editor.internal.onFloatPoint = undefined;
          break;
        } else {
          editor.internal.onFloatLine = false;
          editor.internal.onFloatPoint = undefined;
          if (floatModel.contain(x, y)) {
            editor.currentFloatModel = floatModel;
            clickFloatModel = true;
            break;
          }
        }
      }

      x -= editor.currentFloatModel?.originPosition[0] || 0;
      y -= editor.currentFloatModel?.originPosition[1] || 0;
    }
    if (editor.editFloatModelMode && !clickFloatModel) return;

    // 需要在这个位置 因为再往下就没有意义了 多走了一些程序 也消耗性能
    const img = editor.getFocusImage();
    if (img) {
      const row = editor.selection.getFocusRow();
      if (row) {
        const cell = row.parent;
        const table = cell?.parent;
        const newX = x - editor.pages[0].left - (table ? table.left : 0) - (table? cell.left : 0) - row.left;
        const newY = y + editor.scroll_top - editor.pages[(table?.page_number || row.page_number) - 1].top - (table ? table.top : 0) - (table ? cell.top : 0);
        const centerX = img.left + img.width / 2;
        const centerY = row.top - 10 - 8; // Image.ts 中用的就是 15 半径是 8
        const radius = 12;
        if (newX > centerX - radius && newX < centerX + radius && newY > centerY - radius && newY < centerY + radius) {
          editor.holdRotateIcon = true;
          return;
        }
      }
    }

    // 固定表头的话 点击非第一页表头 直接 return 掉 ↓
    const { cell, table, element, page } = editor.getElementByPoint(x, y);
    if (page && editor.is_edit_hf_mode) {
      editor.internal.current_page = page;
    }
    if (table) {
      editor.internal.isCrossTable = true;
      editor.internal.currentTableTop = 0;
      if (page) {
        editor.internal.currentTableTop = table.top + page.top;
      }
      editor.internal.currentTableId = table.id;
    }
    editor.event.emit("beforePointerDown", { cell, table, element });
    if (cell && table) {
      editor.activeGroupKey = cell.groupKey || undefined;
      const origin_table = table?.getOrigin();
      if (
        cell.position[0] < origin_table.fixed_table_header_num &&
        origin_table.split_parts.findIndex((t) => t === table) > 0
      ) {
        return;
      }
    } else {
      editor.activeGroupKey = undefined;
    }

    editor.internal.pointer_down_state = serializeCopy(
      editor.internal.cursor_state
    );

    editor.internal.point_is_selected = false;

    if (editor.is_comment_mode && !editor.useNewVersionCommentList) {
      const scrollTop = editor.scroll_top;
      const pageTop = editor.pages[0].top;
      // y 的值最大距离顶部也就是 page.top 的值 最小值是 0
      const originTop = scrollTop > 0 ? 0 : pageTop; // 测试发现旧版只要滚动一点点 列表的 top 值就变成 0 了
      const left = editor.page_left + editor.page_size.width + 16;
      if (x > left + 250 && x < left + 260) {
        if (y > originTop + 15 && y < originTop + 25) {
          if (!editor.config.comment.hideCloseBtn) {
            editor.internal.toggleCommentMode(false);
            editor.render();
            return;
          }
        }
      }
      if (x > left + 230 && x < left + 230 + COMMENT_SWITCH_WIDTH) {
        if (y > originTop + 15 && y < originTop + 25) {
          if (!editor.config.comment.hideCloseBtn) {
            editor.useNewVersionCommentList = !editor.useNewVersionCommentList;

            editor.internal.currentComment = null;
            editor.internal.unHightLighter(
              editor.internal.current_has_temp_bg_color_characters
            );
            editor.internal.removeCusCommentHighlight(
              editor.internal.current_has_temp_bg_color_characters
            );

            editor.refreshDocument();

            editor.internal.IsInCommentRange = false;
            editor.render();

            return;
          }
        }
      }
    }
    const isClickOnCommentScrollBar =
      EditorHelper.judgeCursorIsOnCommentScrollBar(editor, x, y);
    if (
      isClickOnCommentScrollBar &&
      editor.is_comment_mode && !editor.useNewVersionCommentList
    ) {
      editor.hold_mouse = true;
      editor.internal.cursorIsOnScrollBar = true;
      editor.internal.scrollBarPosition = y;
      return;
    }

    let commentInfo;
    if (editor.is_comment_mode && editor.useNewVersionCommentList) {
      commentInfo = this.getCommentInfoByCursorByPosition(editor, x, y, true);
      if (commentInfo.page) {
        editor.internal.currentClickCommentPage = commentInfo.page;
      }
      if (commentInfo.isOnScrollBar) {
        editor.isOnScrollBar = true;
        editor.hold_mouse = true;
        commentInfo.page && (commentInfo.page.clickScrollY = y);
        return
      } else {
        editor.isOnScrollBar = false;
      }
    }

    if (editor.is_comment_mode) {
      if (commentInfo?.isSwitch) {
        editor.useNewVersionCommentList = !editor.useNewVersionCommentList;
      }
    }

    const isClickSelectAllTableBtn = EditorHelper.judgeClickSelectAllTableBtn(
      editor,
      x,
      y
    );
    if (isClickSelectAllTableBtn) {
      const focusTable = editor.getTableById(editor.internal.currentTableId);
      if (focusTable) {
        const startPath = focusTable.start_path;
        const endPath = focusTable.end_path;
        editor.selection.setSelectionByPath(startPath, endPath, "model_path");
      }
      return;
    }
    // 判断是否是续打
    if (editor.print_continue) {
      editor.setShadowAreas(x, y);
    } else if (editor.area_print) {
      editor.hold_mouse = true; // 摁下之后切换鼠标的保持状态
      EditorHelper.setAreaPrintLocation(x, y, editor);
    } else if (editor.is_shape_mode) {
      EditorHelper.pointerDownShapeDeal(x, y, editor);
    } else if (editor.waterMark) {
      setFocusImage(null);
      editor.hold_mouse = true; // 摁下之后切换鼠标的保持状态
      WaterMark.closeMarkEdit(editor, false);
      WaterMark.pointerDownMarkDeal(x, y, editor);
    } else {
      editor.hold_mouse = true; // 摁下之后切换鼠标的保持状态
      // TODO切换页眉页脚和文档本身的逻辑和别的也不重合，可以考虑执行完成之后就打断后面逻辑
      // 是否编辑页眉页脚
      if (editor.is_edit_hf_mode) {
        const resInfo = editor.judgeClickHeaderFooter(x, y);
        if (!resInfo.isEditHF) return;
        if (resInfo.currentCell.hf_part === "header") {
          editor.header_cell = resInfo.currentCell;
          const currentPage = resInfo.currentPage;
          const header = currentPage.header;
          const content_top = header.header_bottom;

          if (
            y + editor.scroll_top > content_top + header.page.top - 10 &&
            y + editor.scroll_top < content_top + header.page.top + 10 &&
            x > header.page.left &&
            x < header.page.left + 24
          )
            editor.headerFooterHorizontal(!editor.config.show_header_line, editor.config.show_footer_line)
        }
        if (resInfo.currentCell.hf_part === "footer") {
          editor.footer_cell = resInfo.currentCell;
          const currentPage = resInfo.currentPage;
          const footer = currentPage.footer;
          const content_bottom = footer.footer_outer_top;
          const content_top = footer.page.top + content_bottom;
          const res1 = y + editor.scroll_top < content_top + 20;
          const res2 = y + editor.scroll_top > content_top;
          const res3 = x > footer.page.left;
          const res4 = x < footer.page.left + 24;

          if (res1 && res2 && res3 && res4) {
            editor.headerFooterHorizontal(editor.config.show_header_line, !editor.config.show_footer_line)
          }
        }
        // 如果点击的cell不是当前的cell,则替换变量并清空其选区内容
        if (editor.current_cell !== resInfo.currentCell) {
          editor.current_cell = resInfo.currentCell;
          editor.internal.current_page = resInfo.currentPage;
          editor.selection.clearSelectedInfo();
          editor.selection.setCursorPosition(editor.selection.focus);
        }
      }
      if (editor.internal.resize_cell) {
        editor.internal.pointer_down_position = { x, y: y + editor.scroll_top };
        return;
      }
      // 点击判断x,y是否在选区内
      if (!hold_shift && editor.isInSelectedArea(x, y)) {
        // x,y是否在图片编辑角内
        if (editor.internal.cursor_state.area === undefined) {
          editor.internal.point_is_selected = true;
        }
      } else {
        const ori_anchor = [...editor.selection.anchor];
        if (editor.internal.isMultipleSelection) {
          // 如果是多选区
          const { multipleSelected, selected_areas,  selected_cells_path, para_start, para_end } = editor.selection;
          if (!multipleSelected.length) {
            // 多选区 然后里边还没有 然后现在还是选区的情况下
            // 说明第一个选区是没有按 Ctrl 进行的选区 此时又按 Ctrl 进行选区了 所以要把之前的选区 装进去
            if (selected_areas.length || selected_cells_path.length) {
              multipleSelected.push({
                selectedAreas: selected_areas,
                selectedCellPath: selected_cells_path,
                start: para_start,
                end: para_end
              })
            }
          }
        }

        // 该方法会清空选区
        editor.internal.anchor_focus_by(x, y);
        if (hold_shift) {
          editor.selection.setSelectionByPath(
            ori_anchor,
            editor.selection.focus,
            "model_path"
          );
        } else {
          editor.caret.show = true;
        }
      }
    }

    editor.internal.unHightLighter(
      editor.internal.current_has_temp_bg_color_characters
    );
    editor.internal.removeCusCommentHighlight(
      editor.internal.current_has_temp_bg_color_characters
    );
    if (editor.is_comment_mode) {
      if (editor.useNewVersionCommentList && commentInfo) {
        const { page: commentPage, comment, isReplace, isDelete, isOpen, isClose } = commentInfo;
        if (isClose) {
          editor.internal.toggleCommentMode(false);
          editor.render();
        }
        if (comment && commentPage) {
          commentPage.commentBox.forEach((commentCard: any) => {
            commentCard.comment.selected = false;
          });
          // const isAbolish = comment.comment.abolished;

          const commentId = comment.commentId;
          comment.comment.selected = true;
          const res = editor.internal.getInfoByCommentID(commentId);
          if (res && res.characters && res.characters.length) {
            editor.internal.addHighLighter(res.characters);
            const endPath = editor.paraPath2ModelPath(res.end_path);
            editor.selection.setCursorPosition(endPath);
            editor.updateCaret();
            editor.scroll_by_focus();
          }

          if (isOpen) {
            const id = comment.comment.id
            if (editor.newVersionOpenCommentMap.has(id)) {
              editor.newVersionOpenCommentMap.delete(id);
            } else {
              editor.newVersionOpenCommentMap.set(id, true);
            }
            commentPage.setCommentBox();
          }

          const focusParagraph = editor.selection.getFocusParagraph();
          if (isReplace && !editor.config.comment.hideReplaceBtn) {
            editor.replaceComment(commentId, comment.comment.value, focusParagraph?.id);
            commentPage.commentBox.forEach((commentCard: any) => {
              if (commentCard.comment.id === commentId) {
                commentCard.comment.selected = true;
              } else {
                commentCard.comment.selected = false;
              }
            });
          }
          if (isDelete && !editor.config.comment.hideDeleteBtn) {
            editor.deleteComment(commentId);
            commentPage.commentBox.forEach((commentCard: any) => {
              if (commentCard.comment.id === commentId) {
                commentCard.comment.selected = true;
              } else {
                commentCard.comment.selected = false;
              }
            });
          }
        }
      } else {
        const scroll_top = editor.scroll_top;
        const res = Comment.getCommentByXY(editor, x, y + scroll_top);
        const comment = res?.comment;

        const isReplace = res?.isReplace;
        const isDelete = res?.isDelete;
        const isOpen = res?.isOpen;

        if (comment) {
          editor.commentBox.forEach((commentCard: any) => {
            commentCard.comment.selected = false;
          });
          // const isAbolish = comment.comment.abolished;

          const commentId = comment.commentId;
          comment.comment.selected = true;
          const res = editor.internal.getInfoByCommentID(commentId);
          if (res && res.characters && res.characters.length) {
            editor.internal.addHighLighter(res.characters);
            const endPath = editor.paraPath2ModelPath(res.end_path);
            editor.selection.setCursorPosition(endPath);
            editor.updateCaret();
            editor.scroll_by_focus();
          }
          if (isOpen) {
            if (editor.internal.currentComment === comment.comment) {
              editor.internal.currentComment = null;
            } else {
              editor.internal.currentComment = comment.comment;
            }
          }
          const focusParagraph = editor.selection.getFocusParagraph();
          if (isReplace && !editor.config.comment.hideReplaceBtn) {
            editor.replaceComment(commentId, comment.comment.value, focusParagraph.id);
            editor.commentBox.forEach((commentCard: any) => {
              if (commentCard.comment.id === commentId) {
                commentCard.comment.selected = true;
              } else {
                commentCard.comment.selected = false;
              }
            });
          }
          if (isDelete && !editor.config.comment.hideDeleteBtn) {
            editor.deleteComment(commentId);
            editor.commentBox.forEach((commentCard: any) => {
              if (commentCard.comment.id === commentId) {
                commentCard.comment.selected = true;
              } else {
                commentCard.comment.selected = false;
              }
            });
          }
        } else {
          editor.commentBox.forEach((commentCard: any) => {
            commentCard.comment.selected = false;
          });
        }
      }
    }
    if (editor.is_comment_mode && (element as any)?.comment_id) {
      let exist = false;
      const keys = Object.keys(editor.document_meta.commentsIDSet);
      for (let i = 0; i < keys.length; i++) {
        const group_comments = editor.document_meta.commentsIDSet[keys[i]];
        for (let j = 0; j < group_comments?.length; j++) {
          if (group_comments[j].id === (element as any).comment_id) {
            exist = true;
          }
        }
      }
      if (exist) {
        if (editor.useNewVersionCommentList) {
          const commentBoxList = page?.commentBox;
          if (commentBoxList && commentBoxList.length) {
            // 点击的内容高亮
            const res = editor.internal.getInfoByCommentID(
              (element as any).comment_id
            );
            editor.internal.current_has_temp_bg_color_characters = res.characters;
            editor.internal.addHighLighter(res.characters);

            // 让该页批注高亮
            for (const box of commentBoxList) {
              const comment = box.comment;
              if (comment.id === (element as any).comment_id) {
                comment.selected = true;
                const commentId = comment.id;
                editor.newVersionOpenCommentMap.set(commentId, true);
                box.page = page;
                const { isVisible, distance } = box.checkIsInPageport();
                if (!isVisible && distance) {
                  const { max, min } = page.getRangeOfCommentScrollTop();
                  let newScrollTop = page.scrollTop + distance;
                  if (newScrollTop > max) {
                    newScrollTop = max;
                  } else if (newScrollTop < min) {
                    newScrollTop = 0;
                  }
                  page.scrollTop = newScrollTop;
                }
              } else {
                comment.selected = false;
              }
            }

            page.setCommentBox();
          }
        } else {
          const res = editor.internal.getInfoByCommentID(
            (element as any).comment_id
          );
          if (EditorLocalTest.transUse) {
            const commentId = res.characters[0].comment_id;
            const commentArr = editor.internal.getCommentInfo();
            commentArr.forEach((comment: any) => {
              if (comment.id === commentId) {
                comment.selected = true;
                const commentBox = editor.commentBox;
                const curComment = commentBox.find(
                  (comment: any) => comment.commentId === commentId
                );
                editor.internal.currentComment = curComment.comment;
                const originTop = editor.scroll_top
                  ? editor.scroll_top
                  : editor.pages[0].top;
                const commentTop = curComment.top;
                const commentBottom = commentTop + curComment.height;
                const canvasHeight =
                  (editor.init_canvas.height / editor.config.devicePixelRatio - 50) / editor.viewScale
                let totalHeight = commentBox.length * 30;
                commentBox.forEach((comment: any) => {
                  totalHeight += comment.height;
                });
                const ratio = canvasHeight / totalHeight;
                // 从下往上弹的逻辑
                if (commentBottom > originTop + canvasHeight) {
                  const shouldBeBottomPosition =
                    originTop + canvasHeight - 30 - curComment.height;
                  const bottomDifferenceValue =
                    commentBottom - shouldBeBottomPosition - curComment.height;
                  const bottomScrollBarDifferenceValue =
                    bottomDifferenceValue * ratio;
                  editor.internal.currentCommentPosition += bottomDifferenceValue;
                  editor.internal.scrollBarTop += bottomScrollBarDifferenceValue;
                }
                // 从上往下弹的逻辑
                if (commentTop < originTop + 50) {
                  const shouldBeTopPosition = originTop + 50;
                  const topDifferenceValue = shouldBeTopPosition - commentTop;
                  const topScrollBarDifferenceValue = topDifferenceValue * ratio;
                  editor.internal.currentCommentPosition -= topDifferenceValue;
                  editor.internal.scrollBarTop -= topScrollBarDifferenceValue;
                }
              } else {
                comment.selected = false;
              }
            });

            editor.internal.current_has_temp_bg_color_characters = res.characters;
            editor.internal.addHighLighter(res.characters);
            editor.scroll_by_focus();
          } else {
            editor.internal.current_has_temp_bg_color_characters = res.characters;
            editor.internal.addHighLighter(res.characters);
          }
        }
      }
    }

    // 自定义批注点击时的高亮显示
    if (editor.isCusCommentMode && (element as any)?.cusCommentId) {
      let exist = false;
      const keys = Object.keys(editor.document_meta.cusCommentsIDSet);
      for (let i = 0; i < keys.length; i++) {
        const group_comments = editor.document_meta.cusCommentsIDSet[keys[i]];
        for (let j = 0; j < group_comments?.length; j++) {
          if (group_comments[j].id === (element as any).cusCommentId) {
            exist = true;
          }
        }
      }
      if (exist) {
        const res = editor.internal.getInfoByCusCommentId(
          (element as any).cusCommentId
        );
        editor.internal.current_has_temp_bg_color_characters = res.characters;
        editor.internal.addCusCommentHighlight(res.characters);
      }
    }
    if (editor.view_mode !== "form" || editor.adminMode) {
      editor.render();
    }
  }

  // x y 是相对于 canvas 原点的位置
  static pointerMove(editor: Editor, x: number, y: number) {
    if (editor.isMobileTerminal()) {
      if (!editor.internal.VL.is_mobile_selection) return
      editor.internal.VL.is_mobile_button = false
    }
    let containerInfo = null;
    // 摁住鼠标后的
    if (!editor.useNewVersionCommentList) {
      EditorHelper.judgeIfCursorInCommentRange(editor, x);
      editor.internal.cursorIsCrossScrollBar =
      EditorHelper.judgeCursorIsOnCommentScrollBar(editor, x, y);
    }
    if (editor.holdRotateIcon) {
      const img = editor.getFocusImage();
      if (img) {
        const row = editor.selection.getFocusRow();
        if (row) {
          const cell = row.parent;
          const table = cell?.parent;
          const imgCenterX = editor.pages[0].left + (table ? table.left : 0) + (table ? cell.left : 0) + row.left + img.left + img.width / 2;
          const imgCenterY = editor.pages[(table?.page_number || row.page_number) - 1].top + (table ? table.top : 0) + (table ? cell.top : 0) + row.top + img.height / 2 - editor.scroll_top;

          // 计算鼠标相对于图片中心点的向量
          const dx = x - imgCenterX;
          const dy = y - imgCenterY;

          // 计算鼠标向量的长度（用于检查是否与中心点重合）
          const mouseVectorLength = Math.sqrt(dx * dx + dy * dy);

          // 如果鼠标与图片中心重合，保持当前旋转角度不变
          if (mouseVectorLength < 0.001) {
            return;
          }

          // 计算鼠标相对于图片中心的角度
          // 使用 Math.atan2 计算角度（返回弧度）
          let mouseAngle = Math.atan2(dy, dx);

          // 将角度调整到 0 到 2PI 的范围
          if (mouseAngle < 0) {
            mouseAngle += 2 * Math.PI;
          }

          // 计算图片初始方向（垂直向上）相对于水平方向的角度
          // 初始方向是垂直向上，即 -90 度或 -PI/2 弧度
          const initialAngle = -Math.PI / 2;

          // 计算需要旋转的角度 = 鼠标角度 - 初始角度
          // 这样可以使图片的上边线中心点指向鼠标
          let rotationAngle = mouseAngle - initialAngle;

          // 确保角度在 0 到 2PI 范围内
          rotationAngle = rotationAngle % (2 * Math.PI);
          if (rotationAngle < 0) {
            rotationAngle += 2 * Math.PI;
          }

          // 更新图片的旋转角度
          img.rotation = rotationAngle;

          editor.render();
          return
        }
      }
    }

    if (editor.hold_mouse) {
      if (editor.internal.VL.is_mobile_selection && editor.isMobileTerminal()) {
        if (editor.internal.is_drag_mobile) {
          y -= 16
        } else {
          return
        }
      }
      if (editor.internal.point_is_selected) {
        // 只有只读模式下不能 完全 拖拽(表单模式可以往文本域内拖拽)
        if (editor.readonly) return;
        EditorHelper.dragOutsidePageScroll(editor, y);
        editor.render();
        // 绘制虚拟光标
        editor.drawShadowCaret(x, y);
      } else if (editor.area_print) {
        EditorHelper.dragOutsidePageScroll(editor, y);
        editor.drawAreaPrintShadow(x, y);
        editor.render();
      } else if (
        editor.is_comment_mode &&
        editor.internal.cursorIsOnScrollBar &&
        !editor.useNewVersionCommentList
      ) {
        const height = y - editor.internal.scrollBarPosition;
        const ratio = EditorHelper.dragCommentScrollBar(editor);
        editor.internal.commentMovedHeight = height * ratio;
        editor.internal.scrollBarMovedHeight = height;
        editor.render();
      } else if (editor.is_comment_mode && editor.useNewVersionCommentList && editor.internal.currentClickCommentPage) {
        // 是有最大值 最小值的 到边界的时候 需要 return 掉
        const currentPage = editor.internal.currentClickCommentPage;
        if (currentPage && editor.isOnScrollBar) {
          const { max, min } = currentPage.getRangeOfCommentScrollTop();
          const moveDistance = y - currentPage.clickScrollY;
          currentPage.clickScrollY = y;
          let  newScrollTop = currentPage.scrollTop + moveDistance;
          if (newScrollTop < min) {
            newScrollTop = 0;
          } else if (newScrollTop > max) {
            newScrollTop = max
          }
          currentPage.scrollTop = newScrollTop;
          currentPage.setCommentBox();
          editor.render();
        }
      } else if (editor.internal.is_in_field_drag) {
        editor.render();
        // 绘制虚拟光标
        editor.drawFieldWidthShadowCaret(x, y);
      } else {
        // 改变光标样式
        containerInfo = cursorType(
          x,
          y,
          editor,
          editor.page_left,
          editor.internal.pointer_down_state.type
        );
        editor.internal.hold_mouse_move(x, y);
      }
    } else {
      if (editor.is_comment_mode) {
        const res = Comment.getCommentByXY(editor, x, y + editor.scroll_top)
        if (res?.comment && res.comment.comment) {
          const comment = res.comment
          const userName = comment.comment.name
          if (x > comment.left + 5 && x < comment.left + 65 && (y + editor.scroll_top) > comment.top + 15 && (y + editor.scroll_top) < comment.top + 30) {
            if (userName.length > 3) {
              const width = userName.length * 16
              editor.internal.showCompleteUserName = { show: true, top: comment.top - editor.scroll_top, left: comment.left, height: 16, width: width, text: userName }
              editor.render()
            }
          }
        } else {
          editor.internal.showCompleteUserName = {}
        }
      }
      if (!(editor.area_print || editor.area_print)) {
        containerInfo = cursorType(x, y, editor, editor.page_left);

        this.cursorMoveOverContainer(editor, containerInfo);
      }
      //  表单模式下不允许修改图片以及表格
      if (editor.view_mode === "form" && !editor.adminMode)
        return containerInfo;
      editor.internal.release_mouse_move(x, y);
    }
    if (editor.useNewVersionCommentList) {
      const info = this.getCommentInfoByCursorByPosition(editor, x, y);
      editor.internal.commentCurrentPage = info.page;
    } else {
      editor.internal.commentCurrentPage = undefined;
    }
    return containerInfo;
  }

  static pointerUp(editor: Editor, x: number, y: number, event: PointerEvent) {
    if (editor.holdRotateIcon) {
      editor.holdRotateIcon = false;
      const img = editor.getFocusImage();
      if (img && img.rotation) {
        const canvas = document.createElement("canvas");
        const ctx = canvas.getContext("2d", { alpha: true })!;


        // 这三个值是原始的图片的宽高以及src 无论如何旋转 每次值都是相同的
        const imgWidth = img.meta.originWidth || img.width;
        const imgHeight = img.meta.originHeight || img.height;
        const originRotation = img.meta.originRotation || 0;
        const imgSrc = img.meta.src || img.src;

        // 使用当前计算好的旋转角度
        // 不需要累加之前的旋转角度，因为我们已经计算了绝对角度
        const newRotation = img.rotation + originRotation;
        const { newWidth, newHeight } = calculateRotatedRectSize(imgWidth, imgHeight, newRotation);
        canvas.width = newWidth;
        canvas.height = newHeight;
        canvas.style.width = newWidth + "px";
        canvas.style.height = newHeight + "px";

        // 设置透明背景的关键步骤
        canvas.style.backgroundColor = "transparent"; // 非必须，但显式声明
        ctx.clearRect(0, 0, canvas.width, canvas.height); // 初始化透明

        ctx.translate(newWidth / 2, newHeight / 2);
        ctx.rotate(newRotation);
        const img_map = editor.imageMap.get();
        // 绘制图片，注意坐标需要相对于旋转中心点计算
        ctx.drawImage(
          img_map.get(imgSrc).data,
          -imgWidth / 2,  // x坐标偏移图片宽度一半
          -imgHeight / 2, // y坐标偏移图片高度一半
          imgWidth,
          imgHeight
        );
        // 方案：
        // 1. 实时的旋转图片旋转角度永远都是从 0 开始的 不管是旋转原始图片还是旋转已经旋转过的图片 旋转角度都是从 0 开始
        // 2. 鼠标抬起的时候 插入图片永远都是根据原始图片来进行插入的
        const src = canvas.toDataURL("image/png");
        Image.insert(editor, src,
          {
            meta: {
              src: imgSrc,
              originWidth: imgWidth,
              originHeight: imgHeight,
              originRotation: newRotation,
            }
          }
        );
      }
      return;
    }
    if (editor.internal.isOnReplicableIdentifiers) {
      const field =  editor.internal.mouseOverFiledId && editor.getFieldById(editor.internal.mouseOverFiledId);
      if (field) {
        editor.selection.setSelectionByPath(field.start_para_path, field.end_para_path_outer, "para_path");
      }
    }
    if (editor.internal.isMultipleSelection) {
      // 如果是多选区
      const { multipleSelected, selected_areas,  selected_cells_path, para_start, para_end } = editor.selection;
      if (selected_areas.length > 0 || selected_cells_path.length > 0) {
        // 先跟最后一个选区对比吧
        const last = multipleSelected[multipleSelected.length - 1];
        // 因为 multipleSelected 经过排序了 所以你拿最后一个跟新的对比是否是同一个 已经没有意义了
        const repeat = multipleSelected.find(it => it.start.toString() === para_start.toString() && it.end.toString() === para_end.toString());
        if (!last || !repeat) {
          multipleSelected.push({
            selectedAreas: selected_areas,
            selectedCellPath: selected_cells_path,
            start: para_start,
            end: para_end
          });
          editor.selection.clearSelectedInfo();
        }
      }
      // debugger
      multipleSelected.sort((a, b) => {
        // 首先我要在 a b 里边找到最后边的路径
        const [a1, a2, a3, a4] = a.end;
        const [b1, b2, b3, b4] = b.end;
        if (a1 !== b1) {
          return b1 - a1;
        }
        if (a2 !== b2) {
          return b2 - a2;
        }
        if (a3 !== b3) {
          return b3 - a3;
        }
        return b4 - a4;
      })
    }
    if (editor.isMobileTerminal() && editor.internal.VL.is_mobile_selection) {
      editor.internal.VL.is_mobile_button = true
    }
    clearInterval(editor.internal.scroll_timer); // scroll_timer 原来也是私有属性
    editor.internal.click_scroll_bar = false;
    editor.hold_mouse = false;
    if (editor.is_comment_mode) {
      editor.internal.cursorIsOnScrollBar = false;
      editor.internal.currentCommentPosition +=
        editor.internal.commentMovedHeight;
      editor.internal.commentMovedHeight = 0;
      editor.internal.scrollBarTop += editor.internal.scrollBarMovedHeight;
      editor.internal.scrollBarMovedHeight = 0;
      // 需要在松开的时候检查一下滚动条的位置和评论的位置
      if (editor.internal.scrollBarTop < 0) editor.internal.scrollBarTop = 0;
      if (editor.internal.currentCommentPosition < 0)
        editor.internal.currentCommentPosition = 0;
      // 所有的评论卡片的高度
      const commentBox = editor.commentBox;
      let totalHeight = commentBox.length * 30;
      editor.commentBox.forEach((comment: any) => {
        totalHeight += comment.height;
      });

      const canvasHeight =
        (editor.init_canvas.height / editor.config.devicePixelRatio - 50) / editor.viewScale
      const scrollBarHeight = (canvasHeight * canvasHeight) / totalHeight;
      if (editor.internal.scrollBarTop > canvasHeight - scrollBarHeight)
        editor.internal.scrollBarTop = canvasHeight - scrollBarHeight;
      if (
        editor.internal.currentCommentPosition >
        editor.internal.totalCommentHeight - canvasHeight
      )
        editor.internal.currentCommentPosition =
          editor.internal.totalCommentHeight - canvasHeight;
    }

    editor.internal.cursor_state.area = undefined;
    // 文本域公式模式下点击文本域
    if (
      editor.focusElement &&
      editor.focusElement.field &&
      editor.formula_mode &&
      editor.focusElement.field?.name !== editor.internal.formula_name
    ) {
      const field = editor.focusElement.field;
      if (field?.name) {
        editor.event.emit("fieldFormula", field);
      } else {
        if (field && field.type === "box" && field.parent?.name) {
          editor.event.emit("fieldFormula", field.parent);
        }
      }
      return;
    }
    const focusElement = editor.focusElement;
    const elementType = Object.keys(focusElement);
    if (elementType.indexOf("table") === -1) {
      editor.internal.isCrossTable = false;
    }

    if (
      editor.is_shape_mode &&
      (editor.internal.draw_shape === ShapeMode.Line ||
        editor.internal.draw_shape === ShapeMode.Circle ||
        editor.internal.draw_shape === ShapeMode.Cross ||
        editor.internal.draw_shape === ShapeMode.Rect ||
        editor.internal.is_drag_shape ||
        editor.internal.shape_editor)
    ) {
      if (!editor.internal.is_in_shape_sign.type) {
        editor.handleShapes();
      }
    } else if (editor.waterMark) {
      EditorHelper.waterMarkPointerUp(x, y, editor);
    }

    // 文档的拖拽
    if (editor.internal.point_is_selected) {
      const in_selected_area = editor.isInSelectedArea(x, y);
      if (in_selected_area) {
        // 目标点在选区内
        // 如果在选区内，并且已经拖拽选中则与普通点击逻辑相同
        editor.internal.anchor_focus_by(x, y);
      } else {
        // 目标点在选区外
        // 如果当前在编辑页眉页脚状态，拖拽到了正文内容中则不可拖拽
        const res = editor.judgeClickHeaderFooter(x, y);
        if (
          (editor.is_edit_hf_mode && res.isEditHF) ||
          (!editor.is_edit_hf_mode && !res.isEditHF)
        ) {
          // 如果不在选区内并且拖拽了内容
          editor.dragSelectedArea(x, event);
          editor.internal.point_is_selected = false;
          return;
        }
      }
    }
    if (editor.view_mode === "form" && !editor.adminMode) {
      editor.internal.anchor_focus_by(x, y, "up");
    }
    if (getShadowChange()) {
      // 鼠标抬起图片大小改变
      pointerUpImageChange(editor);
    }
    setShadowChange(false);
    // 判断是否是格式刷模式
    if (editor.format_brush) {
      editor.formatBrushBySelection();
    }

    if (editor.internal.resize_cell) {
      editor.change_table_line();
    }
    if (editor.internal.is_in_field_drag) {
      const field = editor.selection.getFocusField();
      if (field) {
        const info = editor.internal.field_shadow_xy;
        const width = Math.round(Math.abs(info.endX - info.x));
        if (info.type === "max") {
          field.max_width = width;
        } else {
          field.min_width = width;
        }
        editor.reviseFieldAttr({ field: field });
        editor.event.emit("message", { type: "success", msg: "设置成功" });
      }
    }
    editor.internal.point_is_selected = false;
    editor.internal.drag_in_path = [];
    editor.internal.shape_editor = false;
    editor.internal.is_drag_shape = false;
    editor.internal.is_in_field_drag = false;
    editor.render();
  }

  static holdMouseMove(editor: Editor, x: number, y: number) {
    if (editor.internal.resize_cell) {
      // 新增 拖动表格横线 拖出canvas 页面滚动 ↓
      const canvas_height = parseInt(editor.init_canvas.style.height);
      // 鼠标指针的y坐标
      const pointer_y = y * editor.viewScale;
      const resize_table = editor.internal.resize_cell.parent!;
      // 每一页 页脚的那条横线 距离当前页顶部的距离
      const footer_top =
        editor.pages[
          editor.is_edit_hf_mode
            ? editor.internal.current_page!.number - 1
            : resize_table.page_number - 1
        ].footer.footer_outer_top;
      // 页脚的那条横线 距离 canvas画布顶部的距离
      const posiY =
        (footer_top + editor.config.editor_padding_top) *
        resize_table.page_number;

      // 鼠标按下后拉到底部自己滚动的逻辑
      if (pointer_y >= canvas_height) {
        const over_y = pointer_y - canvas_height;
        clearInterval(editor.internal.scroll_timer);
        editor.internal.scroll_timer = setInterval(() => {
          if (editor.internal.isInVisualArea(posiY)) {
            clearInterval(editor.internal.scroll_timer);
            editor.internal.scroll_timer = null;
            return;
          }
          editor.internal.scroll(over_y);
        }, 30);
      }
      // 新增 拖动表格横线 拖出canvas 页面滚动 ↑
      // 更改cell尺寸
      !editor.readonly && editor.move_cell_bounding_line(x, y);
    } else {
      const focusImage = getFocusImage();
      if (
        focusImage &&
        editor.internal.cursor_state.area !== undefined &&
        !focusImage.meta.non_editable
      ) {
        editor.render();
        fictitiousShadow(x, y, editor);
      } else if (editor.internal.click_scroll_bar) {
        editor.internal.scrollBarScroll(y);
        editor.render();
      } else if (editor.internal.click_cell_bar) {
        editor.internal.scrollCellBar(y);
        editor.render();
      } else if (editor.is_shape_mode) {
        if (editor.internal.is_drag_shape) {
          Shape.getDragShapeLocation(editor, x, y);
          editor.render();
        } else if (
          (!editor.internal.is_in_shape &&
            (editor.internal.draw_shape === ShapeMode.Line ||
              editor.internal.draw_shape === ShapeMode.Circle ||
              editor.internal.draw_shape === ShapeMode.Cross ||
              editor.internal.draw_shape === ShapeMode.Rect)) ||
          editor.internal.shape_editor
        ) {
          const shape = editor.internal.focus_shape;
          if (editor.internal.shape_editor === "startXY") {
            shape.startXY.x = x - editor.page_left;
            shape.startXY.y = y + editor.scroll_top;
          } else if (isNumber(editor.internal.shape_editor)) {
            if (shape.type === "rect") {
              // 矩形编辑时点击的点设为startXY,所以需要改startXY的值
              shape.startXY.x = x - editor.page_left;
              shape.startXY.y = y + editor.scroll_top;
            } else {
              shape.endXY.x = x - editor.page_left;
              shape.endXY.y = y + editor.scroll_top;
            }

            if (
              shape.type === "fold_line" &&
              shape.foldLine &&
              shape.foldLine.length
            ) {
              if (shape.foldLine[editor.internal.shape_editor - 1]) {
                shape.foldLine[editor.internal.shape_editor - 1].point.x =
                  x - editor.page_left;
                shape.foldLine[editor.internal.shape_editor - 1].point.y =
                  y + editor.scroll_top;
              }
            }
          } else {
            shape.endXY.x = x - editor.page_left;
            shape.endXY.y = y + editor.scroll_top;
          }
          editor.render();
          if (!editor.internal.shape_editor) {
            Shape.drawShapeShadow(editor);
          }
        }
      } else if (editor.waterMark) {
        if (editor.internal.is_edit_mark !== undefined) {
          editor.render();
          fictitiousShadow(x, y, editor, "imageMark");
        } else if (editor.internal.is_drag_mark) {
          WaterMark.getDragMarkLocation(x, y, editor);
          editor.render();
        }
      } else if (editor.internal.onFloatLine && editor.editFloatModelMode) {
        x += editor.currentFloatModel?.originPosition[0] || 0;
        y += editor.currentFloatModel?.originPosition[1] || 0;
        editor.currentFloatModel!.originPosition[0] = Math.ceil(
          x - editor.internal.dx
        );
        editor.currentFloatModel!.originPosition[1] = Math.ceil(y);
        editor.updateCaret(); // 不调用的话 光标位置就留在原地了
        editor.render();
      } else if (
        editor.internal.onFloatPoint !== undefined &&
        editor.editFloatModelMode
      ) {
        const floatModel = editor.currentFloatModel!;
        const floatPoint = editor.internal.onFloatPoint;
        const leftBottom = {
          x: floatModel.originPosition[0],
          y: floatModel.originPosition[1] + floatModel.height,
        };
        const rightBottom = {
          x: floatModel.originPosition[0] + floatModel.width,
          y: floatModel.originPosition[1] + floatModel.height,
        };

        // TODO 也可以拆出来一个方法 通过原来的原点和宽高 根据最新的 x y 计算出最新的原点和宽高
        if (floatPoint === 0) {
          x += floatModel.originPosition[0] || 0;
          y += floatModel.originPosition[1] || 0;

          floatModel.originPosition[0] = x;
          floatModel.originPosition[1] = y;
          floatModel.width = rightBottom.x - x;
          floatModel.height = rightBottom.y - y;
        }
        if (floatPoint === 1) {
          x += floatModel.originPosition[0] || 0;
          y += floatModel.originPosition[1] || 0;

          floatModel.originPosition[1] = y;
          floatModel.height = leftBottom.y - y;
        }

        if (floatPoint === 2) {
          y += floatModel.originPosition[1] || 0;

          floatModel.originPosition[1] = y;
          floatModel.width = x;
          floatModel.height = rightBottom.y - y;
        }

        if (floatPoint === 3) {
          x += floatModel.originPosition[0] || 0;

          floatModel.originPosition[0] = x;
          floatModel.width = rightBottom.x - x;
        }

        if (floatPoint === 4) {
          floatModel.width = x;
        }

        if (floatPoint === 5) {
          x += floatModel.originPosition[0] || 0;

          floatModel.originPosition[0] = x;
          floatModel.width = rightBottom.x - x;
          floatModel.height = y;
        }

        if (floatPoint === 6) {
          floatModel.height = y;
        }
        if (floatPoint === 7) {
          console.log("右下角 ");
          floatModel.width = x;
          floatModel.height = y;
        }
        editor.currentFloatModel?.update();
        editor.updateCaret();
        editor.render();
      } else {
        // 选区行为
        editor.focus_by(x, y);
      }

    }
  }

  static releaseMouseMove(editor: Editor, x: number, y: number) {
    y += editor.scroll_top;
    if (
      editor.is_shape_mode &&
      (editor.internal.draw_shape === ShapeMode.FoldLine ||
        editor.internal.draw_shape === ShapeMode.ContinueLine)
    ) {
      const foldLine = editor.internal.focus_shape.foldLine;
      if (foldLine && foldLine.length) {
        const lastPoint: any = foldLine[foldLine.length - 1].point;
        requestAnimationFrame(() => {
          editor.render();
          // 所有未经render()的缩放的方法都需要自己添加放大缩小倍数
          const scale = editor.config.devicePixelRatio * editor.viewScale;
          Renderer.drawShapeLine(
            {
              x: (lastPoint.x + editor.page_left) * scale,
              y: (lastPoint.y - editor.scroll_top) * scale,
            },
            { x: x * scale, y: (y - editor.scroll_top) * scale },
            "black",
            1,
            "dash"
          );
        });
      }

      return;
    }
    for (let i = 0; i < editor.pages.length; i++) {
      const page = editor.pages[i];

      if (page.contain(x, y)) {
        let offset_x = x - page.left;
        let offset_y = y - page.top;
        let children: any = [];
        if (editor.is_edit_hf_mode) {
          if (editor.current_cell.hf_part === "header") {
            children = page.header.header_cell.children;
          } else {
            children = page.footer.footer_cell.children;
          }
        } else {
          children = page.children;
        }
        for (let j = 0; j < children.length; j++) {
          const table = children[j];
          if (isTable(table) && table.contain(offset_x, offset_y)) {
            offset_x = offset_x - table.left;
            offset_y = offset_y - table.top;

            for (let k = 0; k < table.children.length; k++) {
              const cell = table.children[k];
              if (cell.contain(offset_x, offset_y)) {
                // 光标在单元格里边
                const tblWidth = table.width;
                const origin_table = table.getOrigin();
                if (origin_table?.fixed_table_header_num) {
                  // 固定表头的时候 有些线 不让拖动
                  if (origin_table.split_parts.indexOf(table) > 0) {
                    // origin_table.split_parts.indexOf(table) > 0 之所以 大于0就是让第一页的可以拖动
                    if (
                      cell.end_row_index <
                      origin_table.fixed_table_header_num ||
                      offset_y - cell.top <= 2.5
                    ) {
                      // 如果表格分页并且固定表头的情况下  只有第一页可以拖动第一行的表格线 其他页的不允许拖动
                      return;
                    }
                  }
                }
                // 左右调整，第一列不可调整，最后一列也不可能调整
                if (
                  cell.position[1] > 0 &&
                  offset_x - cell.left <= DISTANCE_TO_LINE &&
                  offset_x < tblWidth - DISTANCE_TO_LINE
                ) {
                  table.onResizableCellBounding(cell, Direction.right, editor);

                  return;
                }
                if (
                  cell.right - offset_x <= DISTANCE_TO_LINE &&
                  offset_x < tblWidth - Config.min_col_size
                ) {
                  // 减去最小的行号 避免
                  table.onResizableCellBounding(cell, Direction.left, editor);

                  return;
                }

                let is_last_talbe = false;
                if (table.origin) {
                  for (let i = 0; i < table.origin.split_parts.length; i++) {
                    if (
                      table.origin.split_parts[i] === table &&
                      i === table.origin.split_parts.length - 1
                    ) {
                      is_last_talbe = true;
                    }
                  }
                }

                // 如果是拆分的表格 最后一条线不能拖动
                if (table.origin && !is_last_talbe) {
                  // 如果是拆分的表格 并且该表格不是拆分后的最后一个
                  if (
                    cell.start_row_index > 0 &&
                    offset_y - cell.top <= DISTANCE_TO_LINE &&
                    cell.end_row_index < table.row_size.length - 1
                  ) {
                    table.onResizableCellBounding(cell, Direction.up, editor);
                    return;
                  }
                  if (
                    cell.bottom - offset_y <= DISTANCE_TO_LINE &&
                    cell.end_row_index < table.row_size.length - 1
                  ) {
                    table.onResizableCellBounding(cell, Direction.down, editor);
                    return;
                  }
                  break;
                }
                // 上下调整,表格第一行不可进行调整
                if (
                  cell.start_row_index > 0 &&
                  offset_y - cell.top <= DISTANCE_TO_LINE &&
                  !editor.is_edit_hf_mode // 页眉页脚模式 不能上下拖动
                  // && !table.origin // 表格分页时 鼠标样式不变 表格不可调整
                ) {
                  table.onResizableCellBounding(cell, Direction.up, editor);

                  return;
                }
                if (
                  cell.bottom - offset_y <= DISTANCE_TO_LINE &&
                  cell.split_parts.length === 0 &&
                  !editor.is_edit_hf_mode // 页眉页脚模式 不能上下拖动
                  // && !table.origin // 表格分页时 鼠标样式不变 表格不可调整
                ) {
                  table.onResizableCellBounding(cell, Direction.down, editor);

                  return;
                }

                break;
              }
            }
            break;
          }
        }
        break;
      }
    }
    editor.internal.resize_cell = null;
  }

  // 通过配置 决定是否处理压缩数据
  static reInitRawByConfig(
    editor: Editor,
    rawData: any,
    isClearHistory = true,
    configItems: string[] = ["all"]
  ) {
    rawData = useRawDataByConfig(rawData); // 必须先处理一下，否则如果是字符串或者压缩后的则指定使用的配置不生效
    editor.config.handleConfig(editor, rawData, configItems);
    editor.reInitRaw(rawData, isClearHistory);
  }

  static reInitRawCarryFont(
    editor: Editor,
    rawDataContent: any,
    fontStr: string
  ) {
    const baseRawData = JSON.parse(JSON.stringify(editor.config.rawData));
    baseRawData.content = rawDataContent;
    baseRawData.fontMap = JSON.parse(fontStr);
    editor.reInitRaw(baseRawData);
  }

  static reInitConfig(editor: Editor, custom_config: any) {
    editor.config.init(custom_config);
    editor.internal.originConfigItems = JSON.parse(
      JSON.stringify(editor.config)
    );
    editor.refreshDocument(true);
  }

  static checkFormula(editor: Editor) {
    const field = editor.focusElement.field;
    if (editor.readonly) return true;

    if (field && field.formula) {
      editor.event.emit(
        "message",
        "文本域中含有公式时不允许编辑，改变公式中指向的文本域的值可更新文本域"
      );
      return false;
    }
    return true;
  }

  static highlightByKeywordSubscripts({
    editor,
    keywords,
    groupId,
    highLightColor = "orange",
  }: HightLightParameter) {
    keywords.sort((a, b) => a.upset - b.upset);
    let containers;
    if (groupId) {
      const group = editor.current_cell.getGroupById(groupId);
      containers = group?.paragraph;
    } else {
      containers = editor.current_cell.paragraph;
    }
    let keyword = keywords.shift();

    if (!containers || !keyword) return;

    let index = 0;
    let tempWord = "";
    const markCharacters = [];
    let begin = false;
    // eslint-disable-next-line no-labels
    OUTFOR: for (const container of containers) {
      if (isParagraph(container)) {
        for (const character of container.characters) {
          if (!character.value || character.field_position !== "normal") {
            continue;
          }
          // 也可以直接 slice 截取,index 跳过截取的字符串的长度,但是这样跨段的关键字就拿不到了
          if (index === keyword.upset) {
            begin = true;
          }
          if (begin) {
            tempWord += character.value;
            markCharacters.push(character);
          }
          if (index === keyword.offset - 1) {
            begin = false;

            if (tempWord !== keyword.name) {
              // 有一个不匹配就直接 return false
              return false;
            }
            tempWord = "";
            keyword = keywords.shift();
            // eslint-disable-next-line no-labels
            if (!keyword) break OUTFOR; // 没有了 所有关键字都查完了 也没必要继续走了 得放在最后都判断完了再更新 keyword
          }
          index++;
        }
      } else {
        for (const cell of container.children) {
          const paragraphs = cell.paragraph as Paragraph[];
          for (const paragraph of paragraphs) {
            for (const character of paragraph.characters) {
              if (!character.value || character.field_position !== "normal") {
                continue;
              }
              if (index === keyword.upset) {
                if (character.value !== keyword.name[0]) {
                  return;
                }
                begin = true;
              }
              if (begin) {
                tempWord += character.value;
                markCharacters.push(character);
              }
              if (index === keyword.offset - 1) {
                begin = false;
                if (tempWord !== keyword.name) {
                  // 有一个不匹配就直接 return false
                  return false;
                }
                tempWord = "";
                keyword = keywords.shift();
                if (!keyword) return; // 没有了 所有关键字都查完了 也没必要继续走了 得放在最后都判断完了再更新 keyword
              }
              index++;
            }
          }
        }
      }
    }

    for (const character of markCharacters) {
      const font = editor.fontMap.add({
        ...character.font,
        highLight: highLightColor,
      });
      character.font = font;
    }
    editor.refreshDocument();
    return true;
  }

  static highLightByFields(editor: Editor, parameter:{isHighLight: boolean, id?: string, name?: string, color?: string, fields?: XField[]}) {
    if (!parameter) return;
    let handleFields = [];
    let { id, name, color, fields, isHighLight } = parameter;
    if (id) {
      const field = editor.getFieldById(id);
      if (field) {
        handleFields.push(field);
      }
    }
    if (name) {
      const fields = editor.getFieldsByName(name);
      if (fields && fields.length > 0) {
        handleFields.push(...fields);
      }
    }
    if (fields && Array.isArray(fields)) {
      handleFields.push(...fields);
    }
    for (const field of handleFields) {
      const allElements = field.getAllElements();
      for (const element of allElements) {
        if (isCharacter(element)) {
          const font = editor.fontMap.add(
            Object.assign({}, element.font, {
              temp_word_bgColor: isHighLight ? (color || "orange") : undefined,
            })
          );
          element.font = font;
        }
      }
    }
    editor.render();
  }

  static insertText(
    editor: Editor,
    text: string,
    type: insertType = "init"
  ): boolean {
    // 可以考虑把这里的判断条件放入到 allowEditing 里边，allowEditing 在调用该方法之前也调用了, 问题是下边使用了 focus_field 挪过去就有点重复
    // 光标所在处是否为 文本域
    if (!XSelection.deleteAndControlRecord(editor)) return false;
    const focus_field = editor.selection.getFocusField();
    // 如果是文本域且文本域为只读时停止或者不是文本域中且为表单模式时停止
    if (
      ((focus_field && focus_field.isReadonly) || focus_field?.inputMode) &&
      !editor.adminMode
    ) {
      editor.event.emit("message", editor_prompt_msg.readonly_field);
      return false;
    }
    // 只有表单模式下 单元格不能编辑的时候 才要 return 出去
    if (
      !focus_field &&
      editor.view_mode === "form" &&
      !editor.adminMode &&
      !Table.judgeIsEditableInFormMode(editor.selection.focus, editor)
    ) {
      return false;
    }

    editor.caret.cache = null;
    const split_mark = "$s_mark$";
    const replace_value = split_mark + "\n" + split_mark;
    text = replaceLineBreakWith(text, replace_value); // 处理传入的换行字符

    const splitTextArray = text.split(split_mark);
    let start_row = null;
    let focus_row = null;
    focus_row = start_row = editor.selection.getFocusRow();
    for (let i = 0; i < splitTextArray.length; i++) {
      const splitText = splitTextArray[i];
      if (!splitText) continue;
      const info = getInfoFromString(splitText);
      // 段落系的坐标,因为下面有方法会更改坐标，所以此处需要重新获取
      for (const t of info) {
        const para_path = editor.selection.para_focus;
        const current_paragraph = focus_row.paragraph;
        if (t.type === "text") {
          if (!t.value) {
            continue;
          }
          // 参数为 text 行数 字符数 文字样式
          current_paragraph.insertText(
            t.value,
            para_path[para_path.length - 1],
            editor.contextState.getFontState(),
            focus_field // 当前是否在文本域内插入，也没必要每次都重新获取文本域，因为一次插入不可能插入到两个文本域里边去吧？
          );
          insertTextAfterCursorSet(editor, t.value, current_paragraph, para_path);
        } else if (t.type === "fraction") {
          current_paragraph.insertFraction({ editor, int: t.int, numerator: t.numerator, denominator: t.denominator, focusField: focus_field, index: para_path[para_path.length - 1] })
          insertTextAfterCursorSet(editor, " ", current_paragraph, para_path);
        }
        focus_row = editor.selection.getFocusRow(); // 需要重新获取，因为每一次循环都是要换行的，所以 row 就不一样了
      }
    }
    // 过渡使用
    if (type === "transit") {
      return true;
    }
    editor.update(...editor.getUpdateParamsByContainer(start_row));
    editor.scroll_by_focus();
    editor.render();
    return true;
  }

  static insertBox(
    editor: Editor,
    content: any,
    name: string = "",
    focus_para?: Paragraph
  ) {
    // 分组如果锁定 则不能编辑
    if (!editor.operableOrNot(["cell", "group"])) return false;
    if (!editor.selection.isCollapsed) {
      editor.delete_backward();
    }
    focus_para = focus_para || editor.selection.getFocusParagraph();
    const focus_row = editor.selection.getFocusRow();
    const focus_field = editor.selection.getFocusField();
    // 1、如果是文本域且文本域为只读时停止或者不是文本域中且为表单模式时停止
    if (
      (focus_field && focus_field.isReadonly) ||
      (!focus_field && editor.view_mode === "form" && !editor.adminMode)
    ) {
      return false;
    }
    const para_path = editor.selection.para_focus;
    focus_para.insertBox(
      editor,
      content,
      para_path[para_path.length - 1],
      focus_field,
      name
    );
    editor.selection.stepForward(1, focus_row);
    editor.update();
    editor.render();
    return true;
  }

  /**
   * 改变页面缩放
   * @param editor Editor
   */
  static changePageZoom(editor: Editor) {
    if (
      editor.page_size.width *
      editor.config.devicePixelRatio *
      editor.viewScale >=
      editor.init_canvas.width &&
      !editor.print_mode
    ) {
      const viewScale =
        editor.init_canvas.width /
        editor.config.devicePixelRatio /
        editor.page_size.width;
      editor.setViewScale(viewScale);
    }
  }

  static getSearchStr({
    numberOfWords = 5,
    symbol = ["，", "。", "[", "]"],
    editor,
  }: {
    numberOfWords?: number;
    symbol?: string[];
    editor: Editor;
  }) {
    const characters = editor.selection.getFocusParagraph().characters;
    const para_start = editor.selection.para_start;
    const before_cursor_characters = characters.slice(
      0,
      para_start[para_start.length - 1]
    );
    const chars = before_cursor_characters;
    const resArr = [];
    for (let i = chars.length - 1; i >= 0; i--) {
      if ((symbol as any).includes(chars[i].value) || !isCharacter(chars[i])) {
        return { text: resArr.slice(0, numberOfWords).reverse().join(""), lastChar: chars.pop()?.value };
      }
      resArr.push(chars[i].value);
    }
    return { text: resArr.slice(0, numberOfWords).reverse().join(""), lastChar: chars.pop()?.value };
  }

  /** 鼠标相关 ↓ */

  static getElementByModelPath(
    path: Path = [],
    editor: Editor,
    containers: any[] = editor.current_cell.children,
    info: any = {}
  ): {
      element?: Character | Image | Widget | Line | Box | Button | null;
      row?: Row;
      cell?: Cell;
      table?: Table;
      page?: Page
    } {
    const num = path.shift()
    if (num !== undefined) {
      const container = containers[num]
      if (container) {
        if (isTable(container)) {
          info.table = container
        } else if (isCell(container)) {
          info.cell = container
        } else if (isRow(container)) {
          info.row = container
        } else {
          info.element = container
        }
        this.getElementByModelPath(path, editor, container.children, info)
      }
    }
    return info
  }

  static judgeClickHeaderFooter(editor: Editor, x: number, y: number): any {
    y += editor.scroll_top;
    const containers = editor.pages;
    let isEditHF = false;
    let currentPage: Page | null = null;
    let currentCell: Cell = editor.root_cell;
    for (let i = 0; i < containers.length; i++) {
      const container = containers[i];
      if (isPage(container)) {
        if (container.header.contain(x, y)) {
          currentCell = container.header.header_cell;
          currentPage = container;
          isEditHF = true;
          break;
        }
        if (container.footer.contain(x, y)) {
          currentCell = container.footer.footer_cell;
          currentPage = container;
          isEditHF = true;
          break;
        }
      }
    }
    return { isEditHF, currentCell, currentPage };
  }

  static editorIsVisible(editor: Editor) {
    // 判断当前canvas是否为可视状态，只判断 visibility和dispaly即可，如果非可视状态，则无需获取焦点和设置定时器(定时器通过is_focus控制)
    let dom = editor.init_canvas;
    while(dom) {
      const style = getComputedStyle(dom);
      if (style.visibility === "hidden" || style.display === "none") {
        return false;
      }
      dom = dom.parentElement;
    }
    return true;
  }

  static focus(editor: Editor) {
    const isVisible = this.editorIsVisible(editor);
    if (isVisible) {
      // 加定时器是为了避免使用 elementUI 的时候出现堆栈溢出, blur 和 focus 死循环的问题
      editor.internal.inputAdapter.focus();
      editor.is_focus = true;
      editor.caret.show = true;
      if (editor.print_mode) {
        editor.caret.show = false;
      }
      editor.render();
      editor.event.emit("editorFocus");
    } else {
      editor.is_focus = false;
      editor.caret.show = false;
      editor.render();
    }
  }

  static blur(editor: Editor) {
    const field = editor.selection.getFocusField()
    if (field?.showPoint) {
      field.showPoint = false;
      editor.internal.is_point_down = false
      editor.internal.focus_drag_field = null
    }
    editor.caret.show = true;
    editor.is_focus = false;
    editor.getInputDOM()?.blur()
    editor.render();
    clearTimeout(editor.internal.cursorFlashingTimer);
    editor.event.emit("editorBlur");
  }

  // 设置区域打印选区的方法
  static setAreaPrintLocation(x: number, y: number, editor: Editor) {
    const ab_xy = editor.getAbsoluteXYByPointer(x, y);
    editor.internal.area_location.transition_x =
      editor.internal.area_location.start_absolute_x = x;
    if (
      x < editor.page_left + editor.config.page_padding_left ||
      x >
      editor.page_left +
      editor.page_size.width -
      editor.config.page_padding_right
    ) {
      editor.internal.area_location.transition_x =
        editor.internal.area_location.start_absolute_x = editor.page_left;
    }
    editor.internal.area_location.end_absolute_x = 0;
    editor.internal.area_location.end_absolute_y = 0;
    editor.internal.area_location.transition_y =
      editor.internal.area_location.start_absolute_y = ab_xy.y;
  }

  /**
   * 处理pointDown时shape的处理逻辑
   * @param x
   * @param y
   * @param editor
   * @returns
   */
  static pointerDownShapeDeal(x: number, y: number, editor: Editor) {
    const focusShape = editor.internal.focus_shape;
    if (focusShape.type === "line" || focusShape.type === "fold_line") {
      // 点击时x,y是否在线的端点编辑框内
      editor.internal.is_in_shape_sign = focusShape.isInLineSign(
        x - editor.page_left,
        y + editor.scroll_top,
        focusShape,
        editor
      );
    } else {
      editor.internal.is_in_shape_sign = {};
    }
    // 设置线段端点图形属性
    if (focusShape.is_editor && editor.internal.is_in_shape_sign.type) {
      if (editor.internal.is_in_shape_sign.location === "startXY") {
        focusShape.addition &&
          (focusShape.addition.startXY = editor.internal.is_in_shape_sign.type);
      } else if (editor.internal.is_in_shape_sign.location === "endXY") {
        focusShape.addition &&
          (focusShape.addition.endXY = editor.internal.is_in_shape_sign.type);
      } else {
        focusShape.foldLine &&
          focusShape.foldLine.length &&
          (focusShape.foldLine[
            (editor.internal.is_in_shape_sign.location as number) - 1
          ].type = editor.internal.is_in_shape_sign.type);
      }
      return;
    }
    editor.internal.is_in_shape = editor.internal.getFocusShape(x, y, editor);

    // 如或是绘制折线和继续绘制且已有聚焦的continue_shape，则关闭shape的编辑状态
    if (
      editor.internal.draw_shape === ShapeMode.FoldLine ||
      (editor.internal.draw_shape === ShapeMode.ContinueLine &&
        editor.internal.focus_continue_shape)
    ) {
      editor.internal.focus_shape.is_editor = false;
    }
    // 若focus_continue_shape中无值且点击位置是fold_line，则给focus_continue_shape赋值
    if (
      !editor.internal.focus_continue_shape &&
      editor.internal.is_in_shape &&
      editor.internal.is_in_shape.type === "fold_line"
    ) {
      editor.internal.focus_continue_shape = editor.internal.is_in_shape;
    }
    // 取消聚焦后如果不是编辑状态则无法编辑
    if (
      editor.internal.focus_shape &&
      !editor.internal.focus_shape.is_editor &&
      editor.internal.draw_shape
    ) {
      editor.internal.is_in_shape = false;
    }
    editor.hold_mouse = true; // 摁下之后切换鼠标的保持状态
    if (editor.internal.is_in_shape) {
      editor.internal.focus_shape = editor.internal.is_in_shape;
      Shape.shapeEditor(editor, x, y);
    } else {
      editor.closeShapesEditor();

      const params: any = {
        startXY: { x: x - editor.page_left, y: y + editor.scroll_top },
        endXY: { x: x - editor.page_left, y: y + editor.scroll_top },
        para: { paraId: "", spacingX: 0, spacingY: 0 },
      };
      let type: shapeType = "line";
      if (editor.internal.draw_shape === ShapeMode.Circle) {
        type = "circle";
      } else if (editor.internal.draw_shape === ShapeMode.Cross) {
        type = "cross";
      } else if (editor.internal.draw_shape === ShapeMode.Rect) {
        type = "rect";
      } else if (editor.internal.draw_shape === ShapeMode.FoldLine) {
        type = "fold_line";
        params.foldLine = [
          {
            point: { x: x - editor.page_left, y: y + editor.scroll_top },
            type: null,
          },
        ];
      }
      editor.shapeDeal(params, type, x, y);
    }

    if (editor.internal.shape_editor) {
      if (editor.internal.shape_editor === "startXY") {
        editor.internal.focus_shape.addition &&
          (editor.internal.focus_shape.addition.show = "startXY");
      } else if (editor.internal.shape_editor === "endXY") {
        editor.internal.focus_shape.addition &&
          (editor.internal.focus_shape.addition.show = "endXY");
      } else if (isNumber(editor.internal.shape_editor)) {
        editor.internal.focus_shape.addition &&
          (editor.internal.focus_shape.addition.show =
            editor.internal.shape_editor);
      }
    }
  }

  static waterMarkPointerUp(x: number, y: number, editor: Editor) {
    if (editor.internal.is_edit_mark !== undefined) {
      pointerUpMarkChange(editor);
    } else {
      WaterMark.putMarkInputInCanvas(editor);
      const mark = WaterMark.isInTextMark(x, y, editor);
      if (mark) {
        mark.is_edit = true;
        editor.internal.markInput.innerText = mark.params.value;
        editor.internal.markId = mark.params.id;
        const page = editor.getPageByRealY(y + editor.scroll_top);
        let real_y =
          page * (editor.page_size.height + editor.config.page_margin_bottom) +
          editor.config.editor_padding_top -
          editor.scroll_top +
          mark.start.y;
        if (mark.mode === "single") {
          real_y = mark.start.y - editor.scroll_top;
        }
        mark.params.real_y = real_y + editor.scroll_top;

        editor.drawMarkInput(mark.start.x + editor.page_left, real_y, mark);

        editor.showWartFontEditor = true;
      } else {
        if (editor.internal.is_mark_text) {
          editor.internal.markId = "";
          editor.showWartFontEditor = true;
          editor.drawMarkInput(x, y);
        }
      }
    }
  }

  /**
   * 当光标经过容器时设置容器的一些样式
   * @param containerInfo
   */
  static cursorMoveOverContainer(
    editor: Editor,
    containerInfo:
      | {
        row: Row;
        element: ElementInParagraph | null;
      }
      | undefined
  ) {
    editor.internal.mouseOverFiledId = null;
    const oriFieldState = editor.internal.overDrawStyleInfo;
    if (!containerInfo) {
      editor.internal.overDrawStyleInfo = null;
    } else {
      const { element } = containerInfo;
      if (element?.field_id) {
        if (editor.internal.pressedCtrl || editor.config.source === "design") {
          editor.internal.mouseOverFiledId = element.field_id;
          editor.render();
        } else {
          editor.internal.mouseOverFiledId = null;
        }
        if (
          editor.config.fieldShowMode === FieldShowMode.overShowBgColor ||
          editor.config.fieldShowMode === FieldShowMode.showBgColor ||
          editor.config.fieldShowMode === FieldShowMode.mergeSymbol
        ) {
          const pushFieldInfo = (
            field: XField,
            key: string,
            opacity: number
          ) => {
            // 只读文本域和标签文本域输入移入背景色显示浅灰色 （也需要考虑其他只读情况，例如分组锁定和单元格锁定或编辑器只读）
            // 下拉选择文本域显示浅绿色 ，  其他文本域显示浅蓝色
            editor.internal.overDrawStyleInfo[key] = {
              fieldId: field.id,
              color: this.getFocusOrOverFieldBgColor(editor, field, opacity),
            };
          };
          editor.internal.overDrawStyleInfo = {};
          const field = editor.getFieldById(element.field_id)!;
          const pField = field.getOutermostParent();
          if (pField && pField !== field) {
            pushFieldInfo(pField, "parent", 0.15);
            pushFieldInfo(field, "main", 0.1);
          } else {
            pushFieldInfo(field, "main", 0.15);
          }
        }
      } else {
        editor.internal.overDrawStyleInfo = null;
      }
    }
    if (oriFieldState !== editor.internal.overDrawStyleInfo) {
      editor.render();
    }
  }

  static getFocusOrOverFieldBgColor(
    editor: Editor,
    field: XField | BoxField,
    opacity: number = 0.15
  ) {
    if (field instanceof BoxField) {
      if (field.disabled) {
        return `rgba(137, 137, 137, ${opacity})`;
      }
    } else {
      if (!field.editable) {
        if (field.parent) {
          return `rgba(147, 147, 147, ${opacity * 2})`;
        }
        return `rgba(137, 137, 137, ${opacity})`;
      }
    }
    if (field.type === FieldType.select) {
      if (field.parent) {
        return `rgba(110, 196, 131, ${opacity * 2})`;
      }
      return `rgba(100, 186, 121,  ${opacity})`;
    }
    if (field.parent) {
      return `rgba(55, 179, 213, ${opacity * 2})`;
    }

    return `rgba(55, 179, 213,  ${opacity})`;
  }

  static renderContainerArea(editor: Editor) {
    if (editor.isMobileTerminal() && editor.internal.VL.is_mobile_selection) return
    const overFieldInfo = editor.internal.overDrawStyleInfo;
    const getFieldArea = (fieldId: string) => {
      const field = editor.getFieldById(fieldId);
      if (!field || field.isRemove || !field.end_para || ((editor.internal.isMultipleSelection || editor.internal.pressedCtrl) && !field.canBeCopied)) {
        return false;
      }
      if (field.cell.getLocation() === "root" && editor.current_cell !== editor.root_cell) return false;
      if (field.cell.getLocation() === "header" && editor.current_cell !== editor.header_cell) return false;
      if (field.cell.getLocation() === "footer" && editor.current_cell !== editor.footer_cell) return false;
      const areas = [
        {
          start_para_path: field.start_para_path,
          end_para_path: field.end_para_path_outer,
        },
      ];
      return areas;
    };
    if (editor.internal.mouseOverFiledId) {
      const areas = getFieldArea(editor.internal.mouseOverFiledId);
      if (areas) {
        this.drawReplicableIdentifiers(editor, areas);
      }
    }
    if (overFieldInfo) {
      if (overFieldInfo.parent) {
        const areas = getFieldArea(overFieldInfo.parent.fieldId);
        if (areas) {
          this.drawSelectionAreas(editor, areas, overFieldInfo.parent.color);
        }
      }
      const mainAreas = getFieldArea(overFieldInfo.main.fieldId);
      if (mainAreas) {
        this.drawSelectionAreas(editor, mainAreas, overFieldInfo.main.color);
      }
    }
    const focusFieldInfo = editor.internal.focusDrawStyleInfo;
    if (focusFieldInfo) {
      const focusAreas = getFieldArea(focusFieldInfo.fieldId);
      this.drawSelectionAreas(editor, focusAreas, focusFieldInfo.color);
    }
  }

  static getSpacingPageTopByY(y: number, editor: Editor) {
    const spacing =
      Math.round(
        ((y - editor.config.editor_padding_top) %
          (editor.page_size.height + editor.config.page_margin_bottom)) *
        10
      ) / 10;
    return spacing;
  }

  static getPageByRealY(y: number, editor: Editor) {
    const page = Math.floor(
      (y - editor.config.editor_padding_top) /
      (editor.page_size.height + editor.config.page_margin_bottom)
    );
    return page;
  }

  static dblclick(editor: Editor, x: number, y: number) {
    // const result = editor.clickScrollBar(x, y); // TODO 是不是应该放到该方法的最开头
    // if (result) return;

    // 双击时取消拖拽属性，因为会影响获取焦点文本域
    editor.internal.point_is_selected = false;
    editor.internal.drag_in_path = [];
    if (editor.print_continue) {
      return;
    }
    if (editor.is_comment_mode && editor.useNewVersionCommentList) {
      const commentInfo = EditorHelper.getCommentInfoByCursorByPosition(editor, x, y, true);
      if (commentInfo) {
        editor.event.emit("clickComment", commentInfo);
      }
    } else if (editor.is_comment_mode) {
      const commentInfo = Comment.getCommentByXY(editor, x, y + editor.scroll_top);
      if (commentInfo) {
        editor.event.emit("clickComment", commentInfo);
      }
    }
    // 双击的目标如果是field 则设置选区选中
    const { element, table, field: focus_field } = editor.getElementByPoint(x, y);
    // 光标定位到页眉文本域然后三连击正文空白处报错bug,需注释该行
    // (Config.dblclick_path as Path) = editor.selection.focus;
    if (focus_field && !isImage(element) && focus_field.type !== "label") {
      // 起始位置
      const selection_start_path = focus_field.start_para_path;
      PathUtils.movePathCharNum(selection_start_path);
      // 结束位置
      const selection_end_path = focus_field.end_para_path;
      editor.selection.setSelectionByPath(
        selection_start_path,
        selection_end_path
      );
      if (editor.internal.isMultipleSelection) {
        editor.saveMultipleSelectionByCurrent();
        editor.selection.clearSelectedInfo();
      }
      editor.render(); // TODO这里渲染完成之后应该可以这个方法
    }
    // 表单模式下不允许操作文本域外的内容
    if (
      editor.view_mode === "form" &&
      !editor.adminMode &&
      (table ? !table.editableInFormMode : true)
    ) {
      return;
    }
    const resInfo = editor.judgeClickHeaderFooter(x, y);

    if (editor.is_edit_hf_mode === resInfo.isEditHF) {
      const { tbl, line } = editor.getTblByPointIsOnHorizontalLine(x, y);
      // 双击表格线让其紧凑展示
      if (isTable(tbl)) {
        // 分组锁定的情况下 不能拖动表格线 ↓↓↓↓↓
        const group_id = tbl.group_id;
        if (group_id) {
          const group = editor.selection.getGroupByGroupId(group_id);
          const isLock = group?.lock;
          if (isLock) {
            return;
          }
        }
        if (tbl.tableFiexedStyle) return;
        let maxRowSpan = 1;
        for (const cell of tbl.children) {
          if (cell.end_row_index === line && cell.rowspan > maxRowSpan) {
            maxRowSpan = cell.rowspan;
          }
        }
        // 分组锁定的情况下 不能拖动表格线 ↑↑↑↑↑
        for (let i = 0; i < maxRowSpan; i++) {
          // 要修改几个row_size
          tbl.min_row_size[line! - i] = Config.min_row_size;
        }
        tbl.children.forEach((cell) => cell.typesetting());
        editor.update(...editor.getUpdateParamsByContainer(tbl));
        editor.scroll_by_focus();
        // 双击的是表格线 那么其余功能和这个并没有交集，直接return掉
        editor.render();
      }
    } else {
      // 展示页眉页脚的情况下，编辑页眉页脚
      if (editor.show_header_footer && !editor.is_forbid_edit_hf) {
        editor.dblClickEditHeaderFooter(x, y, resInfo);
      }
    }
  }

  static tripleClick(event: MouseEvent, editor: Editor) {
    if (editor.print_continue) {
      return;
    }
    // const { x, y } = editor.getNeedXYbyXY(event.offsetX, event.offsetY);
    // const result = editor.clickScrollBar(x, y); // TODO 是不是应该放到该方法的最开头
    // if (result) return;

    // 光标定位到页眉文本域然后三连击正文空白处报错bug,需注释该行
    // if (editor.selection.getFocusField()) {
    //   editor.selection.setCursorPosition(Config.dblclick_path);
    // }
    const focus_para = editor.selection.getFocusParagraph();
    const para_path = editor.selection.para_anchor;
    const focus_para_startpath = focus_para.cell.parent
      ? [para_path[0], para_path[1], focus_para.para_index, 0]
      : [focus_para.para_index, 0];

    const focus_para_endpath = focus_para.cell.parent
      ? [
        para_path[0],
        para_path[1],
        focus_para.para_index,
        focus_para.characters.length,
      ]
      : [focus_para.para_index, focus_para.characters.length];

    // 需要的是para_path
    editor.selection.setSelectionByPath(
      focus_para_startpath,
      focus_para_endpath
    );
    if (editor.internal.isMultipleSelection) {
      editor.saveMultipleSelectionByCurrent();
      editor.selection.clearSelectedInfo();
    }
    editor.render();
  }

  static locationCaret(para_tabl: Paragraph | Table) {
    const location: number[] = [];
    if (isParagraph(para_tabl)) {
      location.push(
        para_tabl.children[para_tabl.children.length - 1].cell_index,
        0
      );
    } else {
      location.push(para_tabl.cell_index, 0, 0, 0);
    }
    return location;
  }

  /**
   * 拖动页面外滚动
   * @param editor Editor
   * @param y y
   */
  static dragOutsidePageScroll(editor: Editor, y: number) {
    const canvas_height = parseInt(editor.init_canvas.style.height);
    // 鼠标指针的y坐标
    const pointer_y = y * editor.viewScale;
    // 鼠标按下后拉到底部自己滚动的逻辑
    if (pointer_y >= canvas_height) {
      const over_y = pointer_y - canvas_height;
      clearInterval(editor.internal.scroll_timer);
      editor.internal.scroll_timer = setInterval(() => {
        editor.internal.scroll(over_y);
      }, 30);
    } else if (y <= 0) {
      clearInterval(editor.internal.scroll_timer);
      editor.internal.scroll_timer = setInterval(() => {
        editor.internal.scroll(y);
      }, 30);
    } else {
      clearInterval(editor.internal.scroll_timer);
    }
  }

  /**
   * 双击页眉或者页脚
   * @param editor Editor
   * @param x 位置
   * @param y 位置
   * @param resInfo
   * @returns 是否成功
   */
  static dblclickHeaderOrFooter(
    editor: Editor,
    x: number,
    y: number,
    resInfo: any
  ) {
    editor.is_edit_hf_mode = resInfo.isEditHF;
    editor.internal.current_page = resInfo.currentPage;
    if (resInfo.currentCell.hf_part === "header") {
      editor.header_cell = resInfo.currentCell;
    }
    if (resInfo.currentCell.hf_part === "footer") {
      editor.footer_cell = resInfo.currentCell;
    }
    if (editor.current_cell !== resInfo.currentCell) {
      editor.current_cell = resInfo.currentCell;
      editor.internal.anchor_focus_by(x, y);
      editor.render();
      return true;
    }
  }

  /** 鼠标相关 ↑ */

  static tabDown(editor: Editor) {
    const selection = editor.selection;
    let paragraphs = selection.selected_para_info.paragraphs;
    if (editor.selection.multipleSelected.length) {
      const map = new Map();
      for (const current of editor.selection.multipleSelected) {
        const {
          paragraphs: newParagraphs
        } = editor.selection.getParaInfoByPath(current.start, current.end, editor.paraPath2ModelPath(current.start), editor.paraPath2ModelPath(current.end), current.selectedAreas, editor.selection.getCellsInfoByPath(current.selectedCellPath));
        for (const p of newParagraphs) {
          if (!map.has(p)) {
            map.set(p, true);
          }
        }
      }
      paragraphs = Array.from(map.keys());
      map.clear()
    }
    if (paragraphs.find((para) => para.start_para_path.length === 4)) return;
    if (!editor.selection.isCollapsed) {
      paragraphs.forEach((para) => {
        if (para.children[0].children.length > 1) {
          para.indentation += 2;
          para.updateChildren();
        }
      });
    } else {
      // if (!editor.delSectionRecordStack()) {
      //   return false;
      // }
      // 光标所在行
      const focus_row = editor.selection.getFocusRow();
      // 段落对应的路径
      const para_path = [...editor.selection.para_focus];
      // 判断该段是不是list行
      if (focus_row.paragraph.islist) {
        // 判断list类型的行中光标位置
        if (PathUtils.isStartPathInPara(para_path)) {
          // 段首：list级别加1
          focus_row.paragraph.adjustlevel(1);
        } else {
          editor.tabAddFourSpace(focus_row, para_path, "\t");
        }
      } else {
        editor.tabAddFourSpace(focus_row, para_path, "\t");
      }
    }
    editor.update();
    editor.scroll_by_focus();
    editor.render();
    return true;
  }

  static enterDown(editor: Editor) {
    if (!editor.delSectionRecordStack()) {
      return false;
    }
    // 光标行
    const focus_row = editor.selection.getFocusRow();
    // // 过渡数据路径 [x,y]:[行下标，字符下标] [x,y,z,m]:[列表下标，单元格，行下标，字符下标]
    // const para_path = editor.selection.para_focus;
    // 判断改行是不是list行 当前行是否为空
    if (
      focus_row.paragraph.islist &&
      focus_row.paragraph.characters.length === 1
    ) {
      // 段尾：list级别减1
      focus_row.paragraph.adjustlevel(-1);
      editor.update();
      editor.scroll_by_focus();
      editor.render();
    } else {
      return editor.insertText("\n");
    }
  }

  static deleteBackward1(editor: Editor, para_start_path: Path = [...editor.selection.para_start], para_end_path: Path = [...editor.selection.para_end], currentSelection?: {
    selectedAreas: {
      start_para_path: Path;
      end_para_path: Path;
      last_linebreak?: boolean; // 区域的最后一行是否包含换行符
    }[],
    selectedCellPath: Path[],
    start?: Path,
    end?: Path,
  }) {
    let res_info: Path | boolean | undefined;
    // editor.selection.start是光标在row级别的开始位置 一定在前边
    // editor.selection.para_start 是光标在段落级别的开始位置 一定在前边
    // 获取开始行
    let start_row: Row | Table = editor.selection.getRowByPath(
      editor.selection.start
    );
    // 列表删除
    if (editor.selection.isCollapsed && !currentSelection) {
      // 调整list级别
      const focus_paragraph = start_row.paragraph;
      if (start_row.previous_container) {
        start_row = start_row.previous_container;
      }
      if (
        focus_paragraph.islist &&
        PathUtils.isStartPathInPara(para_end_path)
      ) {
        focus_paragraph.adjustlevel(-1);
        editor.update(...editor.getUpdateParamsByContainer(start_row));
        editor.scroll_by_focus();
        editor.render();
        return true;
      }
    }

    // 单元格删除
    if (editor.selection.isOnlyCell || (currentSelection && !currentSelection.selectedAreas.length && currentSelection.selectedCellPath.length)) {
      if (currentSelection) {
        editor.selection.selected_cells_path = currentSelection.selectedCellPath
      }
      const selectedCells = editor.selection.selected_cells;
      let unDeletable = false;
      selectedCells.forEach((cell: any) => {
        cell.cell.fields.forEach((field: any) => {
          if (!field.deletable) unDeletable = true;
        });
      });
      if (unDeletable) {
        editor.event.emit("message", {
          type: "warning",
          msg: "表格存在不可删除文本域！",
        });
        return false;
      }
      if (
        editor.view_mode === "form" &&
        !editor.adminMode &&
        !selectedCells[0].cell.parent!.editableInFormMode
      ) {
        for (let i = 0; i < selectedCells.length; i++) {
          const cell = selectedCells[i].cell;
          const fields = cell.fields;
          for (let j = 0; j < fields.length; j++) {
            const field = fields[j];
            if (!field.readonly && field.type !== "label") {
              field.clear();
              field.reShowPlaceholder();
            }
          }
        }
      } else {
        for (let i = 0; i < selectedCells.length; i++) {
          selectedCells[i].cell.clear();
        }
      }

      // 删除后将光标定位到第一个单元格
      res_info = para_start_path.splice(0, 2).concat([0, 0]);
    } else {
      res_info = editor.deleteContentByPath(para_start_path, para_end_path);
    }
    if (Array.isArray(res_info)) {
      // 将para_path再转为model_path进行光标位置设置
      editor.selection.setCursorPosition(editor.paraPath2ModelPath(res_info));
    } else {
      return false;
    }
    setFocusImage(null);
    editor.selection.clearSelectedInfo();
    let update_container = start_row;
    if (start_row.parent.parent) {
      update_container = start_row.parent.parent.previous_container;
    }
    return true;
  }

  static deleteBackward(editor: Editor) {
    const multipleSelected = editor.selection.multipleSelected;
    if (multipleSelected.length) {
      for (let i = 0; i < multipleSelected.length; i++) {
        const current = multipleSelected[i];
        editor.selection.setSelectionByPath(
          current.start!,
          current.end!,
          "para_path",
          0,
          false
        );
        const success = this.deleteBackward1(editor);
        if (!success) return false;
      }
      editor.selection.multipleSelected.length = 0;
    } else {
      const success = this.deleteBackward1(editor);
      if (!success) return false;
    }

    editor.update();
    // editor.shapeIsInPages();
    editor.scroll_by_focus();
    editor.render();

    return true;
  }

  static deleteForward(editor: Editor) {
    const selection = editor.selection;
    if (selection.isCollapsed) {
      const focus_path: Path = selection.focus;
      // 为保持与回退键删除行为一致，此处如果是表格，删除到单元格内最后一个字符停止删除
      let cell = selection.getDeepCellByPath([...focus_path]);
      if (!cell) {
        cell = editor.current_cell;
      }
      if (PathUtils.isEndPathInCell(focus_path, cell)) {
        return false;
      }
      const focus_row = selection.getFocusRow();
      if (PathUtils.isEndPathInRow(focus_path, focus_row)) {
        // 如果下一行为表格，则停止
        const nextRow = cell.children[focus_row.cell_index + 1];
        if (isTable(nextRow)) {
          return false;
        }
        if (nextRow.paragraph.group_id !== focus_row.paragraph.group_id) {
          return false;
        }
      }
      // 判断当前字符位置是否为文本域开始位置
      const para_path: Path = selection.para_focus;
      const char =
        focus_row.paragraph.characters[para_path[para_path.length - 1]];
      if (char.field_id && char.field_position !== "normal") {
        if (char.field_position === "placeholder") {
          // 如果在一个空文本域中，则不再继续删除
          return false;
        }
        // 文本域是否为只读，如果是只读则不需要移动光标位置
        const field = cell.getFieldById(char.field_id);
        if (field?.isReadonly) {
          return false;
        }
        const next_char =
          focus_row.paragraph.characters[para_path[para_path.length - 1] + 1];
        const nextField = cell.getFieldById(next_char?.field_id);
        if (
          editor.view_mode === "form" &&
          !editor.adminMode &&
          char.field_position === "end" &&
          (!nextField || nextField.isReadonly)
        ) {
          // 如果是表单模式 光标在文本域末尾 并且再往前 紧挨着 就没有文本域了 或者文本域是只读的 那么不需要移动光标位置
          return false;
        }
        selection.stepForward(1, focus_row);
        return editor.delete_forward();
      } else {
        selection.stepForward(1, focus_row);
      }
    }
    return editor.delete_backward();
  }

  static deleteContentByPath(
    editor: Editor,
    para_start_path: Path,
    para_end_path: Path
  ) {
    let res_info: Path | undefined | boolean;
    const selection = editor.selection;
    // 回退键删除单个字符
    if (PathUtils.equals(para_start_path, para_end_path)) {
      const cell = selection.getParagraphByPath(para_start_path).cell;
      res_info = cell.deleteElement(para_start_path, selection);
    } else {
      let cell: Cell = editor.current_cell;
      // 一个单元格内选区删除
      if (PathUtils.isSameCell(para_start_path, para_end_path)) {
        cell = selection.getParagraphByPath(para_start_path).cell;
      }
      const selected_info = selection.selected_fields_chars;

      if (
        editor.view_mode === "form" &&
        !editor.adminMode &&
        selected_info.field_chars
      ) {
        const tem_field_chars = selected_info.field_chars;
        const selected_cells = editor.selection.selected_cells;
        const field_chars = tem_field_chars.filter(
          (char: any) => editor.getFieldById(char.field_id)?.type !== "label"
        );
        const all_chars = selected_info.all_chars;
        if (
          field_chars.length !== all_chars.length &&
          field_chars.length !== 0
        ) {
          // 处理选区里的单元格里的文本域
          const selections = this.splitSelectionInFormMode(editor);
          // 对这些部分分别进行删除
          if (selections.length) {
            for (let i = selections.length - 1; i >= 0; i--) {
              editor.selection.setSelectionByPath(
                selections[i][0],
                selections[i][1]
              );
              res_info = cell.deleteBySelection(
                selections[i][0],
                selections[i][1],
                selection
              );
              res_info &&
                editor.selection.setCursorPosition(
                  editor.paraPath2ModelPath(res_info)
                );
              editor.render();
            }
          }
          if (selected_cells.length) {
            for (let i = 0; i < selected_cells.length; i++) {
              const cell = selected_cells[i].cell;
              if (cell.parent!.editableInFormMode) {
                cell.clear();
                res_info = [cell.parent!.para_index, i, 0, 0];
                continue;
              }
              const fields = cell.fields;
              for (let j = 0; j < fields.length; j++) {
                const field = fields[j];
                if (!field.readonly && field.type !== "label") {
                  field.clear();
                  field.reShowPlaceholder();
                  res_info = field.start_para_path_inner;
                  editor.selection.setCursorPosition(
                    editor.paraPath2ModelPath(res_info)
                  );
                }
              }
            }
          }
          // 将选区分为不同的部分
        } else if (
          field_chars.length === all_chars.length &&
          field_chars.length !== 0
        ) {
          let flag = true;
          for (let i = 0; i < field_chars.length; i++) {
            const char = field_chars[i];
            if (char.field_position !== "normal") {
              const field = editor.getFieldById(char.field_id);
              if (!field?.parent) {
                flag = false;
              }
              if (field?.cell && isTable(field.cell.parent) && field.cell.parent.editableInFormMode) {
                flag = true;
              }
              break;
            }
          }
          if (flag) {
            res_info = cell.deleteBySelection(
              para_start_path,
              para_end_path,
              selection
            );
          } else {
            const selections = this.splitSelectionInFormMode(editor);
            if (selections.length) {
              for (let i = selections.length - 1; i >= 0; i--) {
                editor.selection.setSelectionByPath(
                  selections[i][0],
                  selections[i][1]
                );
                res_info = cell.deleteBySelection(
                  selections[i][0],
                  selections[i][1],
                  selection
                );
              }
            }
          }
        } else {
          res_info = cell.deleteBySelection(
            para_start_path,
            para_end_path,
            selection
          );
        }
      } else {
        res_info = cell.deleteBySelection(
          para_start_path,
          para_end_path,
          selection
        );
      }
    }
    return res_info;
  }

  static splitSelectionInFormMode(editor: Editor): any {
    // if(editor.view_mode !=="form") return
    const selection = editor.selection;
    const selected_info = selection.selected_fields_chars;
    const tem_field_chars = selected_info.field_chars;
    const field_chars = tem_field_chars.filter(
      (char: any) => editor.getFieldById(char.field_id)?.type !== "label"
    );
    const allFields: any = [];
    const selectionPathArr = [];
    const selectionStart = selection.para_start;
    const selectionEnd = selection.para_end;
    // 拿到选区里所有的文本域，包括全选中的和部分选中的
    // 只处理表格之外的文本域 表格内的后边直接全选一起处理
    for (let i = 0; i < field_chars.length; i++) {
      let field = editor.getFieldById(field_chars[i].field_id);
      while (field?.parent) {
        field = field.parent;
      }
      if (!allFields.includes(field) && field?.type !== "label") {
        // 如果选区的开头和结尾在表格外边，就只处理外边的文本域，表格直接清除文本域
        if (selectionStart.length === 2 || selectionEnd.length === 2) {
          if (field?.start_para_path.length !== 4) {
            allFields.push(field);
          }
        } else {
          allFields.push(field);
        }
      }
    }
    const firstField = allFields[0];
    const lastField = allFields[allFields.length - 1];
    const first_start_para_path = firstField?.start_para_path;
    const first_end_para_path = firstField?.end_para_path;
    const last_start_para_path = lastField?.start_para_path;
    const last_end_para_path = lastField?.end_para_path;
    if (allFields.length >= 3) {
      const firstChar = field_chars.find(
        (char: any) =>
          char.field_position === "start" && char.field_id === firstField?.id
      );
      const lastChar = field_chars.find(
        (char: any) =>
          char.field_position === "end" && char.field_id === lastField?.id
      );
      if (firstChar) {
        selectionPathArr.push([first_start_para_path, first_end_para_path]);
      } else {
        selectionPathArr.push([selectionStart, firstField!.end_para_path]);
      }
      for (let i = 1; i < allFields.length - 1; i++) {
        const field = allFields[i];
        selectionPathArr.push([field!.start_para_path, field!.end_para_path]);
      }

      if (lastChar) {
        selectionPathArr.push([last_start_para_path, last_end_para_path]);
      } else {
        selectionPathArr.push([last_start_para_path, selectionEnd]);
      }
    } else {
      if (allFields.length === 1) {
        const startChar = field_chars.find(
          (char: any) =>
            char.field_position === "start" && char.field_id === firstField?.id
        );
        const endChar = field_chars.find(
          (char: any) =>
            char.field_position === "end" && char.field_id === lastField?.id
        );
        if (startChar && !endChar) {
          selectionPathArr.push([first_start_para_path, selectionEnd]);
        } else if (endChar && !startChar) {
          selectionPathArr.push([selectionStart, last_end_para_path]);
        } else {
          selectionPathArr.push([first_start_para_path, first_end_para_path]);
        }
      } else if (allFields.length === 2) {
        const firstChar = field_chars.find(
          (char: any) =>
            char.field_position === "start" && char.field_id === firstField?.id
        );
        const lastChar = field_chars.find(
          (char: any) =>
            char.field_position === "end" && char.field_id === lastField?.id
        );
        if (firstChar) {
          selectionPathArr.push([first_start_para_path, first_end_para_path]);
        } else {
          selectionPathArr.push([selectionStart, first_end_para_path]);
        }
        if (lastChar) {
          selectionPathArr.push([last_start_para_path, last_end_para_path]);
        } else {
          selectionPathArr.push([last_start_para_path, selectionEnd]);
        }
      } else {
        return [];
      }
    }
    return selectionPathArr;
  }

  static exitEditHfMode(editor: Editor, isUpdate: boolean = true) {
    editor.current_cell = editor.root_cell;
    editor.is_edit_hf_mode = false;
    editor.internal.current_page = null;
    if (isUpdate) {
      editor.selection.setCursorByRootCell("start");
      editor.updateCaret();
      editor.render();
    }
  }

  static quitEditHeaderAndFooterMode(editor: Editor) {
    editor.is_edit_hf_mode = false;
    editor.current_cell = editor.root_cell;
    editor.internal.current_page = null;
    editor.selection.setCursorByRootCell("start");
    editor.refreshDocument();
  }

  static enterEditHeaderAndFooterMode(
    editor: Editor,
    type: HeaderFooterType = "header",
    page?: Page
  ) {
    if (editor.view_mode === ViewMode.FORM) {
      if (type === "header" && editor.header_cell.fields.length === 0) return;
      if (type === "footer" && editor.footer_cell.fields.length === 0) return;
    }
    editor.is_edit_hf_mode = true;
    editor.internal.current_page = page || editor.pages[0];
    if (type === "footer") {
      editor.current_cell = editor.footer_cell =
        editor.internal.current_page.footer.footer_cell;
    } else {
      editor.current_cell = editor.header_cell =
        editor.internal.current_page.header.header_cell;
    }
    editor.selection.setCursorByRootCell("start");
    editor.refreshDocument();
    return true;
  }

  static isInVisualArea(editor: Editor, y: number) {
    const canvas_height = parseInt(editor.init_canvas.style.height);
    // 相对于可视区顶部的位置
    const topY = y - editor.scroll_top;
    if (topY >= 0 && topY <= canvas_height - 10) {
      return true;
    }
    return false;
  }

  static isShowFooterOrHeader(editor: Editor) {
    if (!editor.is_edit_hf_mode) {
      editor.show_header_footer = !editor.show_header_footer;
      editor.render();
    }
  }

  /**
   * 在浏览器窗口尺寸改变的时候调用这个方法 更新 canvas 的尺寸
   * @param editor Editor
   */
  static updateCanvasSize(editor: Editor) {
    changeCanvasSize(editor.init_canvas, editor);
    editor.internal.client_top = editor.init_canvas.getBoundingClientRect().top;
    editor.internal.client_left =
      editor.init_canvas.getBoundingClientRect().left;

    editor.update();
    // 调用 editor.udpate 不传参 参数 page_number 最小值为 1 所以无论如何 editor.pages 都会留一个 page 的，所以在更改视口大小调用 updateCanvasSize 的时候，第一页的 page.left 没有变化，就在 updateCanvasSize 里边改动就可以
    editor.render();
  }

  /**
   * 自适应窗口大小
   * @param editor Editor
   */
  static adaptSize(editor: Editor) {
    const canvas = editor.init_canvas;
    const parentDom = canvas.parentNode as HTMLElement;
    let height = parentDom.offsetHeight;
    const width = parentDom.offsetWidth;
    if (height === 0) {
      height = 600;
    }
    canvas.style.height = height + "px";
    canvas.height = height * editor.config.devicePixelRatio;
    canvas.style.width = width + "px";
    canvas.width = width * editor.config.devicePixelRatio;
    const page_size = editor.config.getPageSize();
    if (width > page_size.width) {
      editor.setMaxViewScale();
    } else {
      const newScale = width / page_size.width;
      editor.setViewScale(newScale);
    }
    editor.refreshDocument();
  }

  static changeCustomPageSize(
    width: number,
    height: number,
    direction: pageDirection,
    editor: Editor
  ) {
    if (width > 100 || height > 100 || width < 4 || height < 4 ) {
      editor.event.emit("message", "自定义宽高不能超过100cm或小于4cm");
      return;
    }
    const page_size=   editor.config.setCustomPageSize(width, height, direction);
    editor.reInitConfig({
      page_size_type: "custom",
      page_direction: direction,
      page_size:page_size
    });
    editor.updateCanvasSize();
  }

  static changePageSize(editor: Editor, type: any, direction: pageDirection) {
    editor.reInitConfig({ page_size_type: type, page_direction: direction });
    editor.updateCanvasSize();
  }

  static setCharacterSize(
    parameter: {
      editor: Editor,
      element: XField,
      type: "smaller" | "bigger" | "base",
      baseHeight?: number,
      maxHeight?: number,
      isIncludesBorder?: boolean
    }
  ) {
    const { editor, element, type, baseHeight, maxHeight, isIncludesBorder } = parameter;
    if (isField(element)) {
      element.setCharacterSize(type, baseHeight, maxHeight, isIncludesBorder);
      editor.refreshDocument();
      return true;
    }
  }

  static changePageDirection(editor: Editor, direction: pageDirection) {
    editor.print_continue = false;
    if (editor.raw) {
      if (!editor.raw.config) {
        editor.raw.config = {};
      }
      editor.raw.config.direction = direction;
    }
    editor.reInitConfig({ page_direction: direction });
  }

  // 滚动单元格滚动条滚动逻辑
  static scrollCellBar(editor: Editor, y: number) {
    const cell = editor.internal.scroll_cell_info;
    if (cell && cell.children.length) {
      const total_height = cell.children[cell.children.length - 1].bottom;
      const scroll_height =
        ((y - cell.scroll_cell_bar_top) / (cell.height - 3)) * total_height;
      editor.internal.scroll(scroll_height * 6);
      cell.scroll_cell_bar_top = y;
    }
  }

  // TODO 可以抽离出来滚动逻辑 就可以在拖动滚动条滚动和选区滚动 甚至点按钮滚动页面 调用
  static dragScrollBar(editor: Editor, y: number) {
    // 所有页面和页面间距加起来的高度
    const complete_height =
      ((editor.page_size.height + editor.config.page_margin_bottom) *
        editor.pages.length +
        editor.config.editor_padding_top) *
      editor.viewScale;
    // canvas高度
    const canvas_height =
      editor.init_canvas.height / editor.config.devicePixelRatio;

    // 滚动条高度
    const scroll_bar_height =
      (canvas_height * (canvas_height - 20)) / complete_height;
    // 滚动比率
    const ratio =
      (canvas_height - scroll_bar_height - 20) /
      (complete_height - canvas_height);
    // 每次鼠标拖动页面滚动距离
    const scroll_height = (y - editor.internal.scroll_bar_top) / ratio;

    editor.internal.scroll(scroll_height);
    editor.internal.scroll_bar_top = y;
  }

  // TODO 这个应该是抽离出来的 滚动逻辑吧
  static scroll(editor: Editor, value: number) {
    if (editor.isMobileTerminal()) return
    if (editor.internal.markInput.style.display === "block") {
      WaterMark.putMarkInputInCanvas(editor);
    }
    if (editor.internal.cell_is_scroll) {
      const cell = editor.internal.scroll_cell_info;
      if (!cell || !cell.children.length) return;
      // 获取单元格最后行的结束位置
      const bottom = cell.children[cell.children.length - 1].bottom;
      // 若最后一行底部的位置超过cell的高度，则最大滚动距离为最后一行底部位置减去cell的高度
      if (bottom <= cell.height) return;
      const max_scroll = bottom - cell.height;
      // 单次滚动距离
      cell.scroll_cell_top += value / 6;
      if (cell.scroll_cell_top < 0) {
        cell.scroll_cell_top = 0;
      } else if (cell.scroll_cell_top >= max_scroll) {
        cell.scroll_cell_top = max_scroll;
      }
    } else {
      const comentPage = editor.internal.commentCurrentPage!
      let flag = true; // 当在批注里边滚动到边界的时候 要能够正常滚动页面 所以要加个标识 判断该滚动页面还是该滚动批注列表
      let maxValue = 0;
      if (comentPage) {
        let { max } = comentPage.getRangeOfCommentScrollTop();
        max < 0 && (max = 0);
        maxValue = max
        if (value < 0 && comentPage.scrollTop <= 0) {
          flag = false;
        }
        if (value > 0 && comentPage.scrollTop >= max) {
          flag = false;
        }
      }

      let oldCanScroll = true;
      const totalHeight = editor.internal.totalCommentHeight;
      const canvasHeight =
            (editor.init_canvas.height / editor.config.devicePixelRatio - 50) / editor.viewScale
      const scrollBarHeight = (canvasHeight * canvasHeight) / totalHeight;
      if (!editor.useNewVersionCommentList) {
        if (editor.internal.scrollBarTop <= 0 && value < 0) {
          oldCanScroll = false
        }
        if ((editor.internal.scrollBarTop >= canvasHeight - scrollBarHeight) && value > 0) {
          oldCanScroll = false
        }
      }

      if (
        editor.is_comment_mode &&
        (editor.useNewVersionCommentList ? (comentPage && flag) : (editor.internal.IsInCommentRange && oldCanScroll)) &&
        !editor.internal.click_scroll_bar
      ) {
        if (editor.useNewVersionCommentList) {
          comentPage.scrollTop += value;
          if (comentPage.scrollTop < 0) {
            comentPage.scrollTop = 0;
          }

          if (comentPage.scrollTop > maxValue) {
            comentPage.scrollTop = maxValue;
          }
          comentPage.setCommentBox();
        } else {
          editor.internal.scrollBarTop += value;
          const ratio = EditorHelper.dragCommentScrollBar(editor);
          editor.internal.currentCommentPosition += value * ratio;
          if (editor.internal.scrollBarTop < 0) editor.internal.scrollBarTop = 0;
          if (editor.internal.currentCommentPosition < 0)
            editor.internal.currentCommentPosition = 0;
          if (editor.internal.scrollBarTop > canvasHeight - scrollBarHeight)
            editor.internal.scrollBarTop = canvasHeight - scrollBarHeight;
          if (
            editor.internal.currentCommentPosition >
            editor.internal.totalCommentHeight - canvasHeight
          )
            editor.internal.currentCommentPosition =
              editor.internal.totalCommentHeight - canvasHeight;
          // editor.internal.scrollBarMovedHeight = editor.internal.commentMovedHeight;
          editor.render();
        }
      } else {
        editor.scroll_top += value;
        if (editor.scroll_top < 0) {
          editor.scroll_top = 0;
        }
        // 最大滚动距离
        const maxScrollVal = getMaxScrollVal(editor);
        if (editor.scroll_top > maxScrollVal && maxScrollVal >= 0) { // editor.view_scale 有可能是负值吗？ 如果可能为负 getMaxScrollVal 里边就不能除以 要在外边(这里)除
          editor.scroll_top = maxScrollVal;
        } else if (maxScrollVal < 0) {
          editor.scroll_top = 0;
        }
      }
    }
    editor.updateCaret();
    editor.render();
  }

  static scrollByFocus(editor: Editor) {
    let y: number = 0;
    y = Math.ceil(editor.caret.y + editor.caret.height);
    if (!editor.selection.isCollapsed) {
      const focus_row_height = editor.selection.getFocusRow().height;
      y =
        editor.getElementAbsolutePositionByViewPath(
          editor.modelPath2viewPath(editor.selection.focus)
        ).y +
        editor.config.page_padding_bottom +
        focus_row_height;
    }
    const cell = editor.selection.getFocusCell();
    if (cell && cell.parent && cell.set_cell_height.type === "scroll") {
      const focus_row = editor.selection.getFocusRow();
      const real_top = cell.realTop;
      const total_height = cell.children[cell.children.length - 1].bottom;
      // 判断光标是否超出可视范围，超出滚动使光标在可视范围内
      if (y >= real_top + cell.height - focus_row.padding_vertical) {
        cell.scroll_cell_top = focus_row.bottom - cell.height;
      } else {
        if (y <= real_top + editor.caret.height + focus_row.padding_vertical) {
          cell.scroll_cell_top = focus_row.top;
        }
        if (
          total_height <= cell.scroll_cell_top + cell.height &&
          total_height > cell.height
        ) {
          cell.scroll_cell_top = total_height - cell.height;
        } else if (total_height <= cell.height) {
          cell.scroll_cell_top = 0;
        }
      }
      editor.updateCaret();
    } else {
      // 光标行小于滚动行的情况：左 | 上移光标
      if (y - editor.caret.height <= editor.scroll_top) {
        editor.scroll_top = y - editor.caret.height - 5;
      } else if (
        y >
        editor.scroll_top +
        editor.init_canvas.height /
        editor.viewScale /
        editor.config.devicePixelRatio
      ) {
        editor.scroll_top =
          y -
          editor.init_canvas.height /
          editor.viewScale /
          editor.config.devicePixelRatio +
          5;
      }
      const true_scroll =
        editor.pages[editor.pages.length - 1].bottom +
        editor.config.page_margin_bottom -
        editor.init_canvas.height /
        editor.config.devicePixelRatio /
        editor.viewScale;
      if (true_scroll > 0 && editor.scroll_top > true_scroll) {
        editor.scroll_top = true_scroll;
      }
    }
  }

  static scrollByPage(
    editor: Editor,
    type: string = "top",
    complete_height: number,
    canvas_height: number
  ) {
    if (type === "top") {
      if (
        editor.scroll_top -
        editor.page_size.height -
        editor.config.page_margin_bottom >
        0
      ) {
        editor.scroll_top -=
          editor.page_size.height + editor.config.page_margin_bottom;
      } else {
        editor.scroll_top = 0;
      }
    }
    if (type === "bottom") {
      if (
        editor.scroll_top <
        (complete_height -
          canvas_height -
          (editor.page_size.height + editor.config.page_margin_bottom) * 2) /
        editor.viewScale
      ) {
        editor.scroll_top +=
          editor.page_size.height + editor.config.page_margin_bottom;
      } else {
        editor.scroll_top =
          (complete_height - canvas_height) / editor.viewScale;
      }
    }
  }

  static scrollTo(editor: Editor, position: "top" | "bottom") {
    if (position === "top") {
      editor.scroll_top = 0;
      return;
    }
    editor.selection.setCursorByRootCell("end"); // 此时路径就已经是对的了 但是需要更改新的光标位置的x和y坐标值
    editor.updateCaret(); // 更新了 caret 的坐标
    editor.scroll_by_focus(); // 滚动到光标位置处(caret.x caret.y处)
  }

  /**
   *
   * @param res_info //getContainerInfoByPointHelper方法获取的鼠标处的数据
   * @param editor
   * @returns 获取鼠标处分组的方法
   */
  static getFocusGroupByInfo(res_info: any, editor: Editor) {
    let group;
    if (res_info) {
      const { model_path } = res_info;
      const row = editor.selection.getRowByPath(model_path); // 获取剪切前虚拟光标所在的row
      if (row) {
        let group_id = row.paragraph.group_id;
        if (row.parent.parent) {
          group_id = row.parent.parent.group_id;
        }
        if (group_id) {
          group = editor.selection.getGroupByGroupId(group_id);
        }
      }
    }
    return group;
  }

  static anchorFocusBy(editor: Editor, x: number, y: number, type?: "up") {
    y += editor.scroll_top;
    editor.caret.cache = null;
    const res_info = editor.getContainerInfoByPointHelper(x, y);
    // 如果点击位置在一个设置了最小宽度的文本域中，又点到了最后一个字符的右侧，则将光标置于文本域结束字符之前
    if (res_info?.row.parent) {
      let path = editor.viewPath2ModelPath(res_info.view_path);
      if (path[path.length - 1] === res_info?.row.children.length && path[path.length - 1] > 0) {
        path[path.length - 1] = path[path.length - 1] - 1
      }
      const focusInfo = editor.getElementByModelPath(path, editor);
      if (focusInfo.element?.field_id) {
        const field = res_info.row.parent.getOrigin().getFieldById(
          focusInfo.element?.field_id
        );
        if (
          editor.internal.focus_drag_field &&
          editor.internal.focus_drag_field !== field
        ) {
          return;
        }
        //文本域最大最小宽度拖拽设置属性
        if (field?.showPoint) {
          if (res_info.y) {
            const end_xy = EditorHelper.getAbsoluteXYByParaPath(
              field.end_para_path,
              editor
            );
            const info = editor.internal.field_shadow_xy;
            const height = field.rows[0]?.height;
            const arc_x = end_xy.x + field.end_sym_char.width;
            const arcResult1 = isPointInRectangle(
              arc_x - 8,
              end_xy.y,
              arc_x,
              end_xy.y + height / 2,
              x,
              y
            );
            const arcResult2 = isPointInRectangle(
              arc_x - 8,
              end_xy.y + height / 2,
              arc_x,
              end_xy.y + height,
              x,
              y
            );
            if (arcResult1) {
              editor.internal.is_in_field_drag = true;
              info.type = "max";
            } else if (arcResult2) {
              editor.internal.is_in_field_drag = true;
              info.type = "min";
            }
            const start_xy = EditorHelper.getAbsoluteXYByParaPath(
              field.start_para_path,
              editor
            );
            info.x = start_xy.x + field.start_sym_char.draw_width;
            info.y = end_xy.y;
            let maxX =
              editor.page_left +
              editor.page_size.width -
              editor.config.page_padding_right -
              field.end_sym_char.draw_width;
            if (focusInfo.cell) {
              maxX =
                editor.page_left +
                editor.config.page_padding_left +
                focusInfo.cell.right -
                field.end_sym_char.draw_width -
                editor.config.table_padding_horizontal -
                1;
            }
            info.maxX = maxX;
          }
          return;
        }
        if (field?.meta.changed) {
          if (editor.internal.is_in_link) {
            field.meta.changed = false;
            return;
          }
        }
        if (
          field?.min_width &&
          field.children[field.children.length - 1] === res_info.element
        ) {
          // 为避免结尾字符换行问题，这里使用段落字符转换一次
          const resPath = field.end_para_path;
          res_info.model_path = editor.paraPath2ModelPath(resPath);
        }
      }
    }
    // 如果当前点击的直接是复选框
    if (
      editor.view_mode === "form" &&
      !editor.adminMode &&
      type === "up" &&
      res_info &&
      isWidget(res_info.element) &&
      !editor.formula_mode
    ) {
      // 设置元素选中
      return editor.internal.setElementCheckedHelper(res_info);
    }
    const updateCaretByPath = (path: Path) => {
      editor.selection.setCursorPosition(path);
      editor.internal.form_view_ori_path = [];
      editor.updateCaret();
    };
    // 如果当前模式为表单模式,光标抬起的时候并且无选区 设置光标位置
    if (
      editor.view_mode === "form" &&
      !editor.adminMode &&
      type === "up" &&
      editor.selection.isCollapsed
    ) {
      if (
        res_info &&
        Table.judgeIsEditableInFormMode(res_info.model_path, editor)
      ) {
        return updateCaretByPath(res_info.model_path);
      }
      if (!editor.internal.form_view_ori_path.length) {
        // 解决pacs 设置字体大小 在pointerdown之后 下垃框消失 导致在编辑器内部检测到执行了pointerup方法报错的bug
        // 设置form_view_ori_path默认为空数组 刚开始进来的时候 return掉 不重置光标 这样设置好的字体还会生效
        return;
      }
      const field_id = editor.getNearestFieldByContainer(res_info);
      if (!field_id) {
        return updateCaretByPath(editor.internal.form_view_ori_path);
      }
      editor.internal.form_view_ori_path = []; // 为了在编写过程中 再次改变字体大小 出现同样的问题 所以置空
    }

    // 如果存在分组表单
    if (
      type !== "up" &&
      editor.selection.isCollapsed &&
      editor.internal.existFormGroup() &&
      editor.config.useGroupFormMode
    ) {
      // 拿到当前鼠标点击处的group
      const clickGroup = EditorHelper.getFocusGroupByInfo(res_info, editor);
      // console.log(clickGroup);

      // 判斷与当前分组是否为同一个，如果不是同一个则判断就近文本域
      if (
        !clickGroup ||
        !clickGroup.is_form ||
        clickGroup !== editor.selection.getFocusGroup()
      ) {
        if (res_info) {
          editor.selection.setCursorPosition(res_info.model_path);
          return updateCaretByPath(res_info.model_path);
        }
      }
      if (clickGroup && clickGroup.is_form) {
        const field_id = editor.getNearestFieldByContainer(res_info);
        if (!field_id && editor.internal.form_view_ori_path.length) {
          return updateCaretByPath(editor.internal.form_view_ori_path);
        }
      }
    }

    // 鼠标按下时设置光标位置，此时不需要绘制出光标，用于表单模式下的选区
    if (editor.view_mode === "form" && !editor.adminMode && type !== "up") {
      // 记录当前光标位置
      editor.internal.form_view_ori_path = [...editor.selection.focus];
      editor.selection.clearSelectedInfo();
      if (res_info) {
        editor.selection.setCursorPosition(res_info.model_path);
        // editor.updateCaret();
        const table = editor.current_cell.children[res_info.model_path[0]];
        if (isTable(table) && table.editableInFormMode && !isWidget(res_info.element)) {
          // 表单模式的时候 会在 pointerUp 的时候执行选中逻辑 非表单模式的时候 会在 pointerDown 的时候执行选中逻辑
          // 设置元素选中
          if (editor.internal.setElementCheckedHelper(res_info)) {
            return;
          }
        }

        if (
          res_info.element && res_info.element.field_id &&
          isWidget(res_info.element) &&
          res_info.element.widgetType === "caliper"
        ) {
          this.setCaliperSelect(x, y, res_info, editor);
          return;
        }

      }

      // TODO 注意：因为需要表单模式下选区易用，此处可能会造成鼠标按下去粘贴不到文本域的情况，因为设置的光标位置可能在文本域外
      return;
    }

    // 如果不是表单模式并且不是鼠标抬起时，设置光标位置，如果是表单模式的话则鼠标抬起时设置光标位置（此时需要判断是否为选区）
    if (
      ((editor.view_mode !== "form" || editor.adminMode) && type !== "up") ||
      (editor.view_mode === "form" &&
        !editor.adminMode &&
        type === "up" &&
        editor.selection.isCollapsed)
    ) {
      editor.selection.clearSelectedInfo();
      if (res_info) {
        editor.selection.setCursorPosition(res_info.model_path);

        if (
          res_info.element &&
          isWidget(res_info.element) &&
          res_info.element.widgetType === "caliper"
        ) {
          this.setCaliperSelect(x, y, res_info, editor);
          return;
        }
        if (res_info.element && isImage(res_info.element) && res_info.element.meta && res_info.element.meta.imageType === TypeInImageMeta.HUMAN_BODY_DIAGRAM_TYPE) {
          editor.handleClickHumanBodyDiagram({ clickInfo: res_info });
          return
        }
      } else {
        // 页眉页脚时特殊处理，当点击空白处时定位到开头。（与正文逻辑不同）
        editor.selection.setCursorByRootCell("start");
      }
      // else {
      //   editor.selection.setCursorByRootCell("start");
      // }
      // 设置元素选中
      if (editor.internal.setElementCheckedHelper(res_info)) {
        return;
      }
      editor.updateCaret();
    }
  }
  static getAbsoluteXYByParaPath(paraPath: any, editor: Editor) {
    const modelPath = editor.paraPath2ModelPath(paraPath);
    const viewPath = editor.modelPath2viewPath(modelPath);
    const xy = editor.getElementAbsolutePositionByViewPath(viewPath);
    return xy;
  }
  static setCaliperSelect(x: number, y: number, res_info: any, editor: Editor) {
    const { view_path, row } = res_info;
    // 获取element的真实坐标
    view_path[view_path.length - 1] = res_info.row.children.indexOf(
      res_info.element
    );
    // element的左上角的绝对坐标
    const element_p = editor.getElementAbsolutePositionByViewPath(view_path);
    const element = res_info.element;
    const top = (row.height - res_info.element.height) / 2;
    if (element.params && element.params.num) {
      for (let i = 0; i <= element.params.num; i++) {
        const centerX = element_p.x + 5.5 + element.params.spacing * i;
        const centerY = element_p.y + element.height / 2 - 6 + top;
        const spacing = element.params.spacing;
        if (
          x <= centerX + spacing / 2 &&
          x >= centerX - spacing / 2 &&
          y <= centerY + element.height / 2 &&
          y >= centerY - element.height / 2
        ) {
          if (element.selectNum === i) {
            element.selectNum = undefined;
          } else {
            element.selectNum = i;
          }
          return;
        }
      }
    }
  }

  static focusBy(editor: Editor, x: number, y: number) {
    const canvas_height = parseInt(editor.init_canvas.style.height);
    if (editor.isMobileTerminal()) {
      editor.setSelectionByXY(x, y);
      if (!editor.selection.isCollapsed) {
        editor.render();
      }
      return
    }
    // 鼠标指针的y坐标
    const pointer_y = y * editor.viewScale;

    // 鼠标按下后拉到底部自己滚动的逻辑
    if (pointer_y >= canvas_height) {
      const over_y = pointer_y - canvas_height;
      clearInterval(editor.internal.scroll_timer);
      editor.internal.scroll_timer = setInterval(() => {
        editor.internal.scroll(over_y);
        const scroll_y = canvas_height + editor.scroll_top;
        editor.setSelectionByXY(x, scroll_y);
      }, 30);
    } else if (y <= 0) {
      clearInterval(editor.internal.scroll_timer);
      editor.internal.scroll_timer = setInterval(() => {
        editor.internal.scroll(y);
        const scroll_y = editor.scroll_top;
        editor.setSelectionByXY(x, scroll_y);
      }, 30);
    } else {
      clearInterval(editor.internal.scroll_timer);
      y += editor.scroll_top;
      if (editor.internal.IsInCommentRange) return;
      editor.setSelectionByXY(x, y);
    }
    if (!editor.selection.isCollapsed) {
      editor.render();
    }
  }

  static createElement(
    editor: Editor,
    type:
      | "widget"
      | "image"
      | "char"
      | "font"
      | "field"
      | "table"
      | "paragraph"
      | "cell"
      | "shape"
      | "wartermark"
      | "group",
    props: any = {}
  ) {
    if (type === "widget") {
      let { selected, widgetType } = props;
      widgetType = widgetType === "radio" ? widgetType : "checkbox";
      selected = selected === true;
      // height = isNaN(height) ? 16 : height;
      const font = editor.contextState.getFontState();
      return new Widget(selected, font.height, font, widgetType);
    }
    if (type === "image") {
      let { src, width, height, meta, url } = props;
      width = isNaN(width) ? 50 : width;
      height = isNaN(height) ? 50 : height;
      src = src === undefined ? "" : src;
      url = url === undefined ? "" : url;
      const font = editor.contextState.getFontState();
      const image = new Image(src, width, height, font, meta);
      image.url = url;
      return image;
    }
    if (type === "char") {
      let { font, value, width } = props;
      if (!font) {
        font = new Font(Object.assign({}, editor.config.default_font_style));
      }
      value = value === undefined ? "" : value;
      width = isNaN(width) ? undefined : width;
      return new Character(font, value, width);
    }
    if (type === "font") {
      return new Font(
        Object.assign({}, editor.config.default_font_style, props)
      );
    }
    if (type === "field") {
      let { field_id, style, cell, type, name } = props;
      cell = cell || editor.current_cell;
      if (field_id) {
        if (editor.judgeFieldIdExist(field_id)) {
          return "文本域id重复";
        }
      } else {
        field_id = uuid("field");
      }
      style = style || Object.assign({}, editor.config.default_font_style);
      if (!type) {
        const field = new XField(field_id, style, cell);
        name && (field.name = name);
        return field;
      } else if (type === "box") {
        const boxField = new BoxField(field_id, style, cell);
        name && (boxField.name = name);
        return boxField;
      }
    }
    if (type === "table") {
      const {
        editor,
        id,
        groupId,
        colSize,
        rowSize,
        minRowSize,
        parentCell,
        left,
        right,
        top,
        newPage,
        skipMode,
      } = props;
      return new Table(
        editor,
        id ?? uuid("table"),
        groupId,
        colSize,
        rowSize,
        minRowSize,
        parentCell,
        left,
        right,
        top,
        !!newPage,
        skipMode
      );
    }
    if (type === "paragraph") {
      const { id, parentCell, groupId } = props;
      return new Paragraph(id, parentCell, groupId);
    }
    if (type === "cell") {
      const {
        position = root_node.pos,
        colspan = root_node.colspan,
        rowspan = root_node.rowspan,
        table = null,
        belong = "content",
      } = props;
      return new Cell(
        editor,
        position,
        colspan,
        rowspan,
        table,
        uuid(table ? "cell" : belong),
        belong === "content" ? "" : belong
      );
    }
    if (type === "shape") {
      const { params = {}, isEdit = false, shapeType } = props;
      return new Shape(params, isEdit, shapeType);
    }
    if (type === "wartermark") {
      const {
        wartermarkType,
        mode,
        width,
        height,
        start,
        isEdit = false,
        params,
        name,
      } = props;
      return new WaterMark(
        wartermarkType,
        mode,
        width,
        height,
        start,
        isEdit,
        params,
        name
      );
    }
    if (type === "group") {
      const { groupId, date } = props;
      return new Group(groupId, editor.current_cell, date);
    }
  }

  static setElementCheckedHelper(
    editor: Editor,
    res_info: any
  ): Boolean | undefined {
    // 如果当前元素带有field_id，则当前焦点肯定在文本域内
    let focus_field = null;
    if (res_info && res_info.element) {
      const field_id = res_info.element.field_id;
      if (field_id && res_info.row) {
        const ori_cell = res_info.row.parent.getOrigin();
        focus_field = ori_cell.getFieldById(field_id);
      }
    }
    if (
      res_info &&
      res_info.element &&
      ((isWidget(res_info.element) && !res_info.element.disabled) ||
        isImage(res_info.element) ||
        (focus_field &&
          focus_field.type === "box" &&
          !(focus_field as BoxField).box_widget?.disabled))
    ) {
      // 还需要判断当前多选框组所在的父文本域是否为只读，或者当前分组是否为只读
      if (!editor.operableOrNot(["cell", "group"])) return false;
      if (editor.formula_mode) return false;
      const box_parent = (focus_field as BoxField)?.parent_box;
      if (box_parent && box_parent.parent && box_parent.parent.isReadonly)
        return;
      editor.setSelectImageOrWidget(res_info, focus_field);
      return true;
    } else {
      if (getFocusImage()) {
        setFocusImage(null);
        editor.refreshDocument();
        return true;
      }
    }
  }

  static dragToField(editor: Editor, str: String) {
    const field_str = str.slice(Config.drag_flag_start.length);
    let field_config;
    if (field_str.includes(Config.drag_field_config)) {
      const config_str = field_str
        .split(Config.drag_field_config)
        .slice(-2)
        .join("");
      if (config_str.length) {
        field_config = JSON.parse(config_str);
      }
    }
    editor.insertField(field_config);
    editor.focus();
  }

  static handleCut(editor: Editor, is_drag: boolean =false) {

    if (editor.selection.isOneTable) {
      // 此时要删除整个表格
      const tbl = editor.selection.selected_cells[0].cell.parent!;
      if (
        tbl.children.find((cell) => cell.lock) ||
        !editor.operableOrNot(["group", "editor"]) ||
        editor.view_mode === ViewMode.FORM
      ) {
        return false;
      }
      editor.selection.setCursorPosition([tbl.cell_index, 0]);
      tbl.remove();
      editor.selection.clearSelectedInfo();
      editor.update();
      editor.render();
      return true;
    }
    editor.delete_backward();
    editor.event.emit("exeCommand", { command: "cut" }, event);
    return true;
  }

  static cut(editor: Editor, is_drag: boolean = false) {
    if (editor.selection.isCollapsed && !editor.selection.multipleSelected.length) {
      return;
    }
    if (editor.selection.selected_cells.find(({ cell }) => cell.lock)) {
      return;
    }
    editor.selection.copyData(is_drag);
    if (editor.selection.multipleSelected.length) {
      while(editor.selection.multipleSelected.length) {
        // 不能用 for 循环，因为循环完了 里边有删除 editor.selection.multipleSelected 长度就变了
        const currentSelection = editor.selection.multipleSelected[0];
        editor.selection.setSelectionByPath(currentSelection.start, currentSelection.end, "para_path", 0, false);
        this.handleCut(editor);
      }
      return true;
    } else {
      return this.handleCut(editor, is_drag)
    }
  }

  static putStr2Canvas(editor: Editor, str: string, keepOrigin: boolean) {
    const sel = editor.selection; // 选区
    let selectedCells = null;
    // 选区单元格复制到另一堆选区单元格判断，因为下方会将选区清空，所以要提前将内容记录
    if (!sel.isCollapsed && (sel.isOnlyCell || sel.isOneTable)) {
      if (sel.start.length === sel.end.length && sel.end.length === 4) {
        // 避免选中一个表格加一个换行符的情况下也进来 此时选中的表格就会删除 再走下方的定位逻辑就报错了 因为定位到的单元格不存在了
        selectedCells = sel.selected_cells;
      }
    }
    // 替换掉原来的 if (!editor.delSectionRecordStack()) return; 这种写法是避免调用两次 contentChanged 因为处理历史对堆栈中有调用 contentChanged 事件 还不能传 false 否则就不能粘贴了
    if (!XSelection.deleteAndControlRecord(editor)) return;
    const para_path = sel.para_start; // 考虑删除选区 不能用focus 因为要根据para_path插入character 所以para_path 也是要实时更新的
    const field = sel.getFieldByPath(para_path);
    if (editor.view_mode === ViewMode.FORM && !editor.adminMode && !field&&!Table.judgeIsEditableInFormMode(editor.selection.focus, editor)) {
      // 如果是表单模式并且不在文本域中，则不允许粘贴
      return;
    }
    // 判断插入位置在段落的末尾并且不在开头
    let inParaEndNotFirst;
    if (
      !PathUtils.isStartPathInPara(para_path) &&
      PathUtils.isEndPathInPara(para_path, sel.getFocusParagraph())
    ) {
      inParaEndNotFirst = true;
    }

    const {
      cell: selectedCell,
      hasLastLinebreak,
      rawDataContent,
      fontStr,
    } = handleCopyDataToParas(editor, str, true);

    let canChangedCell: Cell | undefined = selectedCell;

    const changedRawData = editor.event.emit("beforePaste", {
      ...editor.internal.transformData,
      rawDataContent,
      fontStr,
    }) as any;
    if (changedRawData !== "origin") {
      // 这就有监听了
      if (changedRawData === false) return; // 如果返回 false 那就不执行粘贴逻辑了，直接 return 掉
      if (changedRawData) {
        const content = deepClone(changedRawData.content); // 防止内存不能回收问题
        Object.assign(
          editor.internal.imageSrcObj,
          changedRawData.imageSrcObj ?? {}
        );
        canChangedCell = editor.getCellByRawNodeList(content);
        if (!canChangedCell) return;
      }
    }

    // 如果是选中了一些内容 然后输入 则需要先删除

    const copiedData: any = handleCopyData(
      canChangedCell,
      field,
      para_path,
      editor
    ); // 处理后复制出来的数据

    if (selectedCells) {
      // 选区
      if (copiedData.length <= 1 && isTable(copiedData[0])) {
        // 选中了一个表格里边的几个单元格
        // 少的单元格 往 多个单元格里边粘贴 报错
        // 选中表格以及外部 往单元格里边粘贴
        const copiedCells = copiedData[0].children;
        let len = selectedCells.length;
        if (copiedCells.length < selectedCells.length) {
          len = copiedCells.length;
        }
        for (let i = 0; i < len; i++) {
          const { cell: selectedCell } = selectedCells[i];
          selectedCell.paragraph = [...copiedCells[i].paragraph];
          selectedCell.paragraph.forEach(
            (paragraph) => ((paragraph as Paragraph).cell = selectedCell)
          );
          selectedCell.children = [...copiedCells[i].children];
          selectedCell.handleFieldsAssignment(copiedCells[i].fields);
          selectedCell.updateParaIndex();
          selectedCell.updateRowBounding(0);
          selectedCell.typesetting();
        }
        editor.update(); // 得先调用 update 在调用 navigateToThisCell 否则 navigateToThisCell 里边用到的 viewData 中还没有数据,定位就会报错
        selectedCells[selectedCells.length - 1].cell.navigateToThisCell("end"); // 要在 sroll_by_focus 之前重置光标 因为修改了内容 原来选区的光标位置就不一定对了 所以先纠正
        editor.scroll_by_focus();
        editor.render();
        editor.event.emit("exeCommand", {
          command: "paste",
          outerText: false,
          onlyText: false,
        });
        return;
      }
    }

    insertParagraphsHelper(
      sel,
      para_path,
      field,
      copiedData,
      hasLastLinebreak,
      false,
      inParaEndNotFirst,
      false,
      keepOrigin
    );

    editor.update();

    editor.scroll_by_focus();
    editor.render();
    editor.event.emit("exeCommand", {
      command: "paste",
      outerText: false,
      onlyText: false,
    });
  }

  static async paste(
    editor: Editor,
    isDrag: boolean,
    isOnlyText: boolean = false,
    keepOrigin: boolean = false
  ) {
    if (!editor.operableOrNot(["cell", "group"])) return;
    if (isDrag) {
      let str: any = editor.internal.drag_data;
      if (str.startsWith(Config.program_flag_start)) {
        // 拖拽的是文档内容
        editor.internal.putStr2Canvas(str, keepOrigin);
        contentChanged(editor, "paste");
        return;
      } else if (str.startsWith(Config.drag_flag_start)) {
        // 拖拽的是文档外 但是要成为文本域的内容
        editor.internal.makeDragDataToField(str);
        contentChanged(editor, "paste");
        editor.event.emit("exeCommand", {
          command: "paste",
          outerText: true,
          onlyText: false,
        });
        return;
      } else {
        // 拖拽的是文档外 但是要成为纯文本的内容
        const changedStr = editor.event.emit("beforePaste", { str });
        if (changedStr !== "origin") {
          // 这就有监听了
          if (changedStr === false) return; // 返回了个 false 那就不执行粘贴逻辑了
          str = changedStr;
        }

        EditorHelper.insertText(editor, str);
        editor.event.emit("exeCommand", {
          command: "paste",
          outerText: true,
          onlyText: false,
        });
      }
      editor.internal.drag_data = "";
      return;
    }

    if (
      editor.view_mode === ViewMode.FORM &&
      !editor.adminMode &&
      editor.hold_mouse
    ) {
      // 表单模式下 如果按着鼠标左键 没有抬起 就不让粘贴 就不判断 光标是否在文本域里边了
      return;
    }
    if (useLowerVersionCopyPaste()) {
      handleLowerVersionBrowserPaste(editor);
      return;
    }

    if (isOnlyText) {
      // 如果粘贴为纯文本
      let str: any = await navigator.clipboard.readText();

      const changedStr = editor.event.emit("beforePaste", { str });
      if (changedStr !== "origin") {
        // 这就有监听了
        if (changedStr === false) return; // 返回了个 false 那就不执行粘贴逻辑了
        str = changedStr;
      }

      EditorHelper.insertText(editor, str);
      contentChanged(editor, "paste");
      editor.event.emit("exeCommand", {
        command: "paste",
        outerText: true,
        onlyText: false,
      });
      return;
    }

    let t = 0;

    const items = await (navigator.clipboard as any).read().catch(() => {
      t = 1;
    });
    if (t === 1) return;
    const item = items[0];
    // 如果剪切板内是html
    if (item.types.includes("text/html")) {
      const htmlBlob = await item.getType("text/html");
      const html = await new Response(htmlBlob).text();
      if (html.startsWith(`<pre editor-msun-copy-data=`) && !isDrag) {
        const str = html.split("editor-msun-copy-slice")[1];
        editor.internal.putStr2Canvas(str, keepOrigin);
      } else {
        if (item.types.includes("image/png")) return; // 认为既有 text/html 又有 image/png 的是从 Excel 中来的
        const rawData = editor.event.emit("html2RawData", html);
        if (rawData && rawData !== "origin") {
          return editor.insertTemplateData(rawData);
        }
        // 从 word 复制的内容也是 html 格式的
        let str: any = await navigator.clipboard.readText();
        const changedStr = editor.event.emit("beforePaste", { str });
        if (changedStr !== "origin") {
          // 这就有监听了
          if (changedStr === false) return; // 返回了个 false 那就不执行粘贴逻辑了
          str = changedStr;
        }
        if (str.startsWith(Config.program_flag_start)) {
          editor.internal.putStr2Canvas(str, keepOrigin);
        } else {
          // 此时也有可能是从
          EditorHelper.insertText(editor, str);
          editor.event.emit("exeCommand", {
            command: "paste",
            outerText: true,
          });
        }
        // resolve(true);
      }
      contentChanged(editor, "paste");
      return;
    }
    if (item.types.includes("text/plain")) {
      const plainBlob = await item.getType("text/plain");
      let str: any = await new Response(plainBlob).text();
      const changedStr = editor.event.emit("beforePaste", { str });
      if (changedStr !== "origin") {
        // 这就有监听了
        if (changedStr === false) return; // 返回了个 false 那就不执行粘贴逻辑了
        str = changedStr;
      }
      EditorHelper.insertText(editor, str);
      contentChanged(editor, "paste");
    }
  }

  static insertTableAfterField({ editor, field }: {editor: Editor, field?: XField | null}) {
    // 确定操作的文本域
    field = field || editor.selection.getFocusField();
    if (field) {
      // 如果不是最外层的文本域，不能插入
      if (field.parent) {
        return;
      }
      const end_path = field.end_para_path;
      // 表格里不行
      if (end_path.length > 2) return;
      // 向后移一位
      end_path[end_path.length - 1] += 1;
      editor.selection.setCursorPosition(editor.paraPath2ModelPath(end_path));
    }
  }

  static  insertFieldAfterField({ editor, positionField, insertField }: {editor: Editor, positionField?: XField | null, insertField?: any}) {
    positionField = positionField || editor.selection.getFocusField();
    if (!positionField) return;

    insertField = insertField || {}
    const originAdminMode = editor.adminMode;
    editor.adminMode = true;
    const endParaPath = positionField.end_para_path;
    endParaPath[endParaPath.length - 1] += 1;
    editor.selection.setCursorPosition(editor.paraPath2ModelPath(endParaPath));
    const field = XField.insert(
      editor,
      {
        id: insertField.id || uuid("field"),
        ...insertField,
      }
    );
    if (isNotNullAndUndefined(insertField?.value)) {
      XField.updateText(editor, { fields: [field], value: insertField.value })
    }
    editor.adminMode = originAdminMode;
  }

  static getPointerOutSideCaretPath(
    editor: Editor,
    x: number,
    y: number,
    containers: (Page | Table | Cell | Row)[],
    path: Path = []
  ) {
    for (let i = 0; i < containers.length; i++) {
      const container = containers[i];
      if (isPage(container)) {
        if (!container.contain_vertical(y)) {
          if (y <= editor.config.editor_padding_top) {
            y += editor.config.page_margin_bottom;
          } else if (
            y >
            (editor.page_size.height + editor.config.page_margin_bottom) *
            (i + 1) &&
            y <
            (editor.page_size.height + editor.config.page_margin_bottom) *
            (i + 1) +
            editor.config.page_margin_bottom
          ) {
            y -= editor.config.page_margin_bottom;
          }
        }
        if (container.contain_vertical(y)) {
          path.push(i);

          x -= container.left;
          y -= container.top;

          const start_element = container.children[0];
          const end_element = container.children[container.children.length - 1];

          if (y <= start_element.top && y >= 0) {
            y = start_element.top;
          } else if (y >= end_element.bottom && y <= container.height) {
            y = end_element.bottom;
          }
          return editor.getPointerOutSideCaretPath(
            x,
            y,
            container.children,
            path
          );
        }
      } else if (isTable(container)) {
        if (container.contain_vertical(y)) {
          path.push(i);
          x -= container.left;
          y -= Math.round(container.top);
          return editor.getPointerOutSideCaretPath(
            x,
            y,
            container.children,
            path
          );
        }
      } else if (isCell(container)) {
        if (container.contain_vertical(y)) {
          if (
            (x <= 0 && container.left === 0) ||
            (container.parent &&
              x >= container.parent.right - editor.config.page_padding_left &&
              container.right >
              container.parent.right -
              editor.config.page_padding_left -
              container.width) ||
            (container.parent &&
              x >= container.parent.width &&
              Math.round(container.right) === container.parent.width) ||
            container.contain(x, y)
          ) {
            path.push(i);
            x -= container.left;
            y -= container.top;
            return editor.getPointerOutSideCaretPath(
              x,
              y,
              container.children,
              path
            );
          }
        }
      } else if (isRow(container)) {
        if (container.parent.parent) {
          if (container.contain_vertical(y)) {
            path.push(i);
            if (x <= container.left) {
              x = container.left;
              path.push(0);
            } else if (container.left < x && x < container.right) {
              path.push(container.offset_near(x));
            } else {
              x = container.left;
              path.push(container.children.length);
            }
            return {
              row: container,
              view_path: path,
              x: x,
              y: y,
            };
          }
          if (i === containers.length - 1) {
            const last_row =
              container.parent.children[container.parent.children.length - 1];
            if (last_row) {
              path.push(container.parent.children.length - 1);
              x -= last_row.left;
              y = last_row.top;

              if (x <= last_row.left) {
                x = last_row.left;
                path.push(0);
              } else if (last_row.left < x && x < last_row.right) {
                path.push((last_row as Row).offset_near(x));
              } else {
                x = last_row.left;
                path.push(last_row.children.length);
              }
              return {
                row: last_row as Row,
                view_path: path,
                x: x,
                y: y,
              };
            }
          }
        } else {
          if (container.contain_vertical(y)) {
            path.push(i);
            x -= container.left;
            y = container.top;

            if (x <= 0) {
              x = 0;
              path.push(0);
            } else if (x > 0 && x < container.right) {
              path.push(container.offset_near(x));
            } else {
              x = container.left;
              path.push(container.children.length);
            }
            return {
              row: container,
              view_path: path,
              x: x,
              y: y,
            };
          }
        }
      }
    }
  }

  static getContainerInfoByPoint(
    editor: Editor,
    x: number,
    y: number,
    containers: (Page | Table | Cell | Row | FloatModel)[],
    path: Path = []
  ) {
    const fractionObj: any = {
      FRACTION: undefined,
    }

    if (editor.editFloatModelMode && !path.length) {
      path = [editor.pages[0].number - 1];
    }

    if (editor.current_cell.hf_part && isPage(containers[0])) {
      x -= editor.internal.current_page!.left;
      y -= editor.internal.current_page!.top;
      containers = editor.current_cell.children;
      path = [editor.internal.current_page!.number - 1];
    }

    for (let i = 0; i < containers.length; i++) {
      const container = containers[i];

      if (isTable(container) || isCell(container)) {
        if (container.contain(x, y)) {
          path.push(i);
          x -= container.left;
          y -= container.top;

          return editor.getContainerInfoByPoint(x, y, container.children, path);
        }
      } else {
        if (container.contain_vertical(y)) {
          path.push(i);

          if (isRow(container)) {
            x -= container.left;
            path.push(container.offset_near(x));
            const elements = [...container.children];
            container.linebreak && elements.push(container.linebreak);

            let element:
              | Character
              | Image
              | Widget
              | Line
              | Box
              | Button
              | null = null;

            for (let j = 0; j < elements.length; j++) {
              const currentElement = elements[j];
              const nextElement = elements[j + 1];
              const offsetX = nextElement
                ? nextElement.left - currentElement.right
                : 0;
              if (
                currentElement.contain_horizontal(x, offsetX > 0 ? offsetX : 0)
              ) {
                if (isFraction(currentElement)) {
                  y -= container.top;
                  fractionObj.FRACTION = currentElement;
                  const max = Math.max(currentElement.numeratorWidth, currentElement.denominatorWidth);
                  const min = Math.min(currentElement.numeratorWidth, currentElement.denominatorWidth);
                  const horizontalStartY = container.height - container.padding_vertical - currentElement.font.height / 2;
                  const bottom = container.height - container.padding_vertical + currentElement.font.height / 2 + 2;
                  if (x >= currentElement.left && x <= currentElement.left + currentElement.intWidth) {
                    // 整数
                    fractionObj.POSITION = 0;
                    const relativeX = x - currentElement.left;
                    const str = currentElement.int + "";
                    let totalWidth = 0;
                    for (let s = 0; s < str.length; s++) {
                      const { width } = Renderer.measure(currentElement.font, str[s]);
                      if (relativeX >= totalWidth && relativeX <= totalWidth + width) {
                        fractionObj.INDEX = s;
                      }
                      totalWidth += width ;
                    }
                  } else if (y <= bottom && y >= horizontalStartY) {
                    // 分母
                    fractionObj.POSITION = 2;
                    const relativeX = x - (currentElement.left + currentElement.intWidth + (max === currentElement.denominatorWidth ? 0 : (max - min) / 2));
                    const str = currentElement.denominator + "";
                    let totalWidth = 0;
                    for (let s = 0; s < str.length; s++) {
                      const { width } = Renderer.measure(currentElement.font, str[s]);
                      if (relativeX >= totalWidth && relativeX <= totalWidth + width) {
                        fractionObj.INDEX = s;
                      }
                      totalWidth += width ;
                    }

                  } else if (y >container.top && y <=horizontalStartY) {
                    // 分子
                    fractionObj.POSITION = 1;
                    const relativeX = x - (currentElement.left + currentElement.intWidth + (max === currentElement.numeratorWidth ? 0 : (max - min) / 2));
                    const str = currentElement.numerator + "";
                    let totalWidth = 0;
                    for (let s = 0; s < str.length; s++) {
                      const { width } = Renderer.measure(currentElement.font, str[s]);
                      if (relativeX >= totalWidth && relativeX <= totalWidth + width) {
                        fractionObj.INDEX = s;
                      }
                      totalWidth += width ;
                    }
                  }
                }

                element = currentElement;
                break;
              }
            }

            return {
              row: container,
              element,
              view_path: path,
              x,
              y,
              fractionObj
            };
          }

          x -= container.left;
          y -= container.top;

          return editor.getContainerInfoByPoint(x, y, container.children, path);
        }
      }
    }
  }

  static getElementByPointer(editor: Editor, x: number, y: number) {
    const obj: {
      isEditHF: boolean; // 判断是否在页眉上
      page?: Page; // 是否在白色区域内
      row?: Row; // 当前行
      table?: Table | null; // 当前表格
      cell?: Cell; // 当前单元格
      image?: Image | null; // 当前图片
      element?: Character | Image | Widget | Line | Box | Button | null; // 当前元素
      paragraph?: Paragraph; // 当前段落
      group?: Group | null;
      field?: XField | BoxField | null;
      button?: Button;
      viewPath?: Path;
      fractionObj?: any
    } = {
      isEditHF: false,
    };
    // TODO pointerdown 事件 先走的这儿
    // 1. 判断是否点在了页眉上还是页脚上
    const res =
      !editor.editFloatModelMode && editor.judgeClickHeaderFooter(x, y);
    y += editor.scroll_top;
    if (res.isEditHF) {
      obj.isEditHF = true;
    } else {
      obj.isEditHF = false;
    }
    const container_info = editor.getContainerInfoByPoint(x, y);
    if (container_info) {
      obj.fractionObj = container_info.fractionObj;
      obj.row = container_info.row;
      obj.element = container_info.element;
      if (PathUtils.isPath(container_info.view_path)) {
        const model_path = editor.viewPath2ModelPath(container_info.view_path);
        const para_path = editor.modelPath2ParaPath(model_path);
        obj.field = editor.selection.getFieldByPath(para_path);
      }
      obj.viewPath = container_info.view_path;
    }
    if (isImage(obj.element)) {
      obj.image = obj.element;
    }
    if (isButton(obj.element)) {
      obj.button = obj.element;
    }
    if (obj.row) {
      obj.paragraph = obj.row.paragraph;
    }
    if (obj.paragraph && obj.paragraph.group_id) {
      obj.group = editor.selection.getGroupByGroupId(obj.paragraph.group_id);
    }
    for (let i = 0; i < editor.pages.length; i++) {
      const page = editor.pages[i];

      if (page.contain(x, y)) {
        obj.page = page;
        let offset_x = x - page.left;
        let offset_y = y - page.top;

        // const children = (editor.is_edit_hf_mode && editor.current_cell.hf_part === "header") ? page.header.header_cell.children : page.children;
        let children = [];
        if (editor.is_edit_hf_mode) {
          if (editor.current_cell.hf_part === "header") {
            children = page.header.header_cell.children;
          } else {
            children = page.footer.footer_cell.children;
          }
        } else {
          children = page.children;
        } // 此时的children 是包括页眉页脚的

        // page.children 是不包括页眉页脚的
        for (let j = 0; j < children.length; j++) {
          const table = children[j];
          if (isTable(table) && table.contain(offset_x, offset_y)) {
            obj.table = table;
            offset_x = offset_x - table.left;
            offset_y = offset_y - table.top;

            for (let k = 0; k < table.children.length; k++) {
              const cell = table.children[k];
              if (cell.contain(offset_x, offset_y)) {
                obj.cell = cell;
              }
            }
          }
        }
      }
    }
    if (!obj.group && obj.table && obj.table.group_id) {
      obj.group = editor.selection.getGroupByGroupId(obj.table.group_id);
    }
    return obj;
  }

  static getContainerInfoByPointHelper(editor: Editor, x: number, y: number) {
    // 获取鼠标所在处元素信息

    const container_info = editor.getContainerInfoByPoint(x, y); // 当在页面里边点击的时候 没有点击到元素 点击到了空白地方 返回的也是 undefined

    let res_info: any = null;
    if (container_info) {
      const { view_path } = container_info;
      const path = editor.viewPath2ModelPath(view_path);
      res_info = Object.assign({ model_path: path }, container_info);
      res_info.view_path = view_path;
    } else {
      if (!editor.is_edit_hf_mode) {

        const container_info = editor.getCaretPathInTableEmptyArea(x, y); // 当在页面里边点击的时候 没有点击到元素 点击到了空白地方 返回的就是距离点击位置最近的正确的内容
        if (container_info) {
          const { view_path } = container_info;
          const model_path = editor.viewPath2ModelPath(view_path);
          res_info = Object.assign({ model_path: model_path }, container_info);
          res_info.view_path = view_path;
        } else {
          // 点击表格外或者页面外的路径处理逻辑
          const container_info = editor.getPointerOutSideCaretPath(x, y);
          if (container_info) {
            const { view_path } = container_info;
            const model_path = editor.viewPath2ModelPath(view_path);
            res_info = Object.assign(
              { model_path: model_path },
              container_info
            );
            res_info.view_path = view_path;
          }
        }
      } else {
        const container_info = editor.getCaretPathInTableEmptyArea(x, y);
        if (container_info && container_info.view_path.length !== 5)
          return res_info;
        if (container_info) {
          const { view_path } = container_info;
          const model_path = editor.viewPath2ModelPath(view_path);
          res_info = Object.assign({ model_path: model_path }, container_info);
          res_info.view_path = view_path;
        }
      }
    }
    if (editor.editFloatModelMode) {
      if (res_info?.view_path) {
        const index =
          editor.floatModels.findIndex(
            (floatModel) => floatModel === editor.currentFloatModel
          ) ?? 0;
        res_info.view_path[0] = index;
      }
    }
    return res_info;
  }

  static getUpdateParamsByContainer(
    container: Table | Cell | Row | undefined
  ): number[] {
    if (!container) {
      return [0, 1, 0];
    }
    if (isTable(container)) {
      const modelTable = container.getOrigin();
      return [
        modelTable.cell_index,
        modelTable.page_number,
        modelTable.page_index,
      ];
    } else if (isCell(container)) {
      const modelCell = container.getOrigin();

      if (modelCell.parent) {
        return this.getUpdateParamsByContainer(modelCell.parent);
      } else {
        return [0, 1, 0];
      }
    } else {
      if (container.parent.parent) {
        return this.getUpdateParamsByContainer(container.parent.parent);
      } else {
        const last_con_para =
          container.paragraph.previousParagraph ?? container.paragraph;
        if (isTable(last_con_para)) {
          return this.getUpdateParamsByContainer(last_con_para);
        } else {
          return [
            last_con_para.children[0].cell_index,
            last_con_para.children[0].page_number,
            last_con_para.children[0].page_index,
          ];
        }
      }
    }
  }

  static getAllCell(editor: Editor, assign_cell?: Cell) {
    const outer_cell: Cell[] = []; // 最底层的cell数组
    const all_cell: Cell[] = []; // 所有的cell集合
    if (assign_cell) {
      outer_cell.push(assign_cell);
    } else {
      outer_cell.push(editor.header_cell, editor.root_cell, editor.footer_cell);
    }
    // 收集所有的cell
    for (let i = 0; i < outer_cell.length; i++) {
      const cell = outer_cell[i];
      all_cell.push(cell);
      for (let j = 0; j < cell.children.length; j++) {
        const para = cell.children[j];
        if (isRow(para)) continue;
        all_cell.push(...para.children);
      }
    }
    return all_cell;
  }

  static getContentState(editor: Editor, is_first_undo: boolean = false) {
    // 云病历全是分组的时候，就只将分组内的数据 转为 rawData 进行保存 如果不是云病历那种 全是分组的情况 就还是要走全量的数据转换
    const undoStack = editor.history.getUndo(); // undoStack 撤销堆栈 redoStack 重做堆栈
    const undoStackLastRecordData = undoStack[undoStack.length - 1];

    let last_operation_group_id = undoStackLastRecordData?.group_id; // 默认从历史堆栈中取(认为没有切换分组进行操作) 如果切换分组操作了 就取当前操作的分组 id
    const last_operation_group: any =
      last_operation_group_id &&
      editor.selection.getGroupByGroupId(last_operation_group_id);

    let current_operation_group = editor.selection.getFocusGroup();

    if (is_first_undo) {
      // 第一次撤销，应该重新保存上一次操作的分组中的数据，或者整篇文档的数据
      if (last_operation_group) {
        current_operation_group = last_operation_group;
      } else {
        current_operation_group = null;
      }
    }
    // 例子：上一个分组删除了一段数据，接着换一个分组删除一段数据 这种情况下 这两个分组的数据都需要保存 否则 只保存哪个分组都不对 都少一份
    let rawData1: any;
    let rawData2: any;
    // 当前在分组的情况
    if (current_operation_group) {
      // 上一次没有操作，撤销堆栈为空时，只记录当前分组内容
      if (!undoStackLastRecordData) {
        rawData1 = editor.getGroupRawData(current_operation_group);
        if (editor.config.getDataType === GetDataType.compressedBase64) {
          rawData1 = uncompressData(rawData1);
        }
        rawData1.group_id = current_operation_group?.id;
      } else if (!last_operation_group_id) {
        // 上一次在分组外边，记录全量内容
        const other_info = editor.packSaveDocumentInfo();
        // rawData1 = rawDataTrans.modelDataToRawData(
        //   editor.header_cell,
        //   editor.root_cell,
        //   editor.footer_cell,
        //   other_info
        // );
        rawData1 = editor.event.emit(
          "modelData2RawData",
          editor.header_cell,
          editor.root_cell,
          editor.footer_cell,
          other_info
        );
      } else {
        // 上一次在分组里边
        // 上一次操作的分组与当前分组是同一个分组，则只记录当前分组内容
        if (last_operation_group_id === current_operation_group.id) {
          rawData1 = editor.getGroupRawData(current_operation_group);
          if (editor.config.getDataType === GetDataType.compressedBase64) {
            rawData1 = uncompressData(rawData1);
          }
          rawData1.group_id = current_operation_group.id;
        } else {
          // 不是同一个分组，则要记录两个分组的内容
          rawData1 = editor.getGroupRawData(current_operation_group);
          rawData1.group_id = current_operation_group.id;

          rawData2 = editor.getGroupRawData(last_operation_group);
          rawData2.group_id = last_operation_group_id;
        }
      }
      last_operation_group_id = current_operation_group.id;
    } else {
      const other_info = editor.packSaveDocumentInfo();
      // rawData1 = rawDataTrans.modelDataToRawData(
      //   editor.header_cell,
      //   editor.root_cell,
      //   editor.footer_cell,
      //   other_info
      // );
      rawData1 = editor.event.emit(
        "modelData2RawData",
        editor.header_cell,
        editor.root_cell,
        editor.footer_cell,
        other_info
      );
      last_operation_group_id = "";
    }
    const contentState: any = {
      content1: rawData1,
      content2: rawData2,
      selection: editor.selection.getSelection(),
      group_id: last_operation_group_id, // 只用于当前接口中作判断，不能用于恢复撤销内容时的判断
      hf_cell_info: null, // 第一位代表页码，第二位 页眉/页脚 记录当前编辑器的cell,处理页眉页脚编辑与正文编辑连续撤销时bug
    };
    // 如果是页眉页脚编辑器状态，则记录当前编辑页眉页脚cell信息
    if (editor.is_edit_hf_mode) {
      contentState.hf_cell_info = [
        editor.internal.current_page?.number,
        editor.current_cell.hf_part,
      ];
    }
    return contentState;
  }

  static setContentState(
    editor: Editor,
    contentState: any,
    optionType: "redo" | "undo" = "undo"
  ) {
    if (!contentState) return;
    editor.selection.multipleSelected =contentState.multipleSelected || [];
    setFocusImage(null);
    const c1 = contentState.content1;
    if (c1?.group_id) {
      // 说明该恢复的是分组数据 只是不太确定是要恢复一个分组还是两个分组
      // 没有用 this.current_cell 是因为解决 编辑页眉的时候进行撤销 导致页眉按照分组内数据进行修改的bug 所以都用了 this.root_cell 而且在下边还有给 this.current_cell 赋值的地方 所以 this.current_cell 也不太对
      const c2 = contentState.content2;
      Object.assign(editor.internal.imageSrcObj, c1?.imageSrcObj ?? {});
      Object.assign(editor.internal.imageSrcObj, c2?.imageSrcObj ?? {});
      Group.recoverData(editor, c1?.group_id, c1?.content);
      Group.recoverData(editor, c2?.group_id, c2?.content);
      editor.document_meta = (c2?.meta || c1?.meta)||{}; // 撤销的时候 也要恢复 批注信息
      editor.custom_meta = c2?.customMeta || c1?.customMeta; // 撤销的时候 也要恢复 批注信息
      editor.shapes = [...Shape.getShapesByRawData(c2?.shapes || c1?.shapes)];
    } else {
      // 重置编辑器中展示的数据内容
      editor.reInitRaw(contentState.content1, false);

      editor.update();
    }
    // 选区（光标）状态信息
    let selectionState;
    // 该操作的目的是为了使重做的时候将光标置于正确的位置。
    if (optionType === "redo") {
      const undoStack = editor.history.getUndo();
      const undoContentState = undoStack[undoStack.length - 1];
      selectionState = undoContentState.end_selection;
    }
    if (!selectionState) {
      selectionState = contentState.selection;
    }
    // 重置选区对象赋值给当前编辑器
    editor.selection.anchor = selectionState.anchor;
    editor.selection.focus = selectionState.focus;
    editor.selection.selected_cells_path = selectionState.cell_path;
    editor.selection.selected_areas = selectionState.selected_areas;
    // 编辑页眉页脚状态,必须在update之后赋值，因为update之前pages还未初始化
    if (contentState.hf_cell_info) {
      editor.internal.current_page =
        editor.pages[contentState.hf_cell_info[0] - 1];
      const part = contentState.hf_cell_info[1] as "header" | "footer";
      const hf = editor.internal.current_page[part];
      if (hf instanceof Header) {
        editor.current_cell = editor.header_cell = hf.header_cell;
      }
      if (hf instanceof Footer) {
        editor.current_cell = editor.footer_cell = hf.footer_cell;
      }
      editor.is_edit_hf_mode = true;
    } else {
      editor.exitEditHfMode(false);
    }
    editor.updateFormatParagraph();
    // editor.updateCaret(); // 更新光标
    editor.update(/* ...editor.getUpdateParamsByContainer() */); // 不能只更新光标 需要 update
    editor.scroll_by_focus();
    editor.render();
  }

  /**
   * 选区是否在表单模式的分组内
   * @param editor
   * @returns
   */
  static isInSameFormGroup(group_id: string | null, editor: Editor) {
    const paraList = editor.selection.selected_para_info.paragraphs;
    for (let i = 0; i < paraList.length; i++) {
      const paragraph = paraList[i];
      let groupId;
      if (paragraph.cell.parent) {
        groupId = paragraph.cell.parent.group_id;
      } else {
        groupId = paragraph.group_id;
      }
      if (groupId !== group_id) {
        editor.event.emit("message", "不同分组之间不允许拖拽");
        return false;
      }
    }
    return true;
  }

  static dragPaste(
    editor: Editor,
    x: number,
    event?: DragEvent | PointerEvent
  ) {
    // 拖拽内容有表格时,因为存在表格的情况下无法设置选区，  当拽位置为表格并且选区内存在单元格时终止操作
    if (
      !editor.internal.drag_in_path.length ||
      (PathUtils.isTablePath(editor.internal.drag_in_path) &&
        editor.selection.selected_cells_path.length)
    ) {
      return;
    }

    let drag_in_path = editor.viewPath2ModelPath(editor.internal.drag_in_path);

    let drag_in_para_path = editor.modelPath2ParaPath(drag_in_path);
    const drag_in_row = editor.selection.getRowByPath(drag_in_path); // 获取剪切前虚拟光标所在的row
    let group_id = drag_in_row.paragraph.group_id;
    if (drag_in_row.parent.parent) {
      group_id = drag_in_row.parent.parent.group_id;
    }
    const result = EditorHelper.isInSameFormGroup(group_id, editor);
    if (!result) return;
    // 判断选区内是否包含表单模式的分组内容，如果有，return
    const field = editor.selection.getFieldByPath(drag_in_para_path);
    if (group_id) {
      // 分组锁定时
      const group = editor.selection.getGroupByGroupId(group_id);

      if (group?.is_form && editor.config.useGroupFormMode) {
        editor.view_mode = ViewMode.FORM;
        if (!field) {
          return;
        }
        editor.refreshDocument();
      }
      if (group?.lock) {
        return;
      }
    }

    const table = editor.current_cell.paragraph[drag_in_para_path[0]];
    // 非普通文本域或文本域为只读时
    // 表单模式下如果没有文本域 或者文本域只读 也不能执行拖拽
    if (
      editor.view_mode === "form" &&
      !editor.adminMode &&
      (!field || field?.isReadonly) &&
      !(isTable(table) && table.editableInFormMode)
    ) {
      editor.internal.drag_in_path = [];
      Renderer.restore();
      return;
    }
    if (field) {
      if (editor.selection.selected_cells_path.length) {
        // 如果拖拽的内容有表格 也直接return掉 因为接下来什么操作都不需要执行
        return;
      }
      if (field.type !== "normal" || field.isReadonly) {
        return;
      }
      // 如果文本域内容为空则将光标置于文本域开始位置
      if (!field.children.length) {
        drag_in_para_path = field.start_para_path;
        drag_in_para_path[drag_in_para_path.length - 1] =
          drag_in_para_path[drag_in_para_path.length - 1] + 1;
        drag_in_path = editor.paraPath2ModelPath(drag_in_para_path);
      }
    }

    // TODO 应该放在这儿吧 要不要只判断分组呢 人家要是想要有其他操作呢 还是放在这儿吧
    const isHandleDrop = editor.event.emit("dragDrop", [...drag_in_path], x);
    if (isHandleDrop !== "origin") {
      return;
    }

    const drag_in_para = drag_in_row.paragraph;
    const before_para_id = drag_in_para.previousParagraph?.id; // 在这儿记录的不正确 因为下边有可能就执行 cut 方法给删除了也就不存在了
    const before_element =
      drag_in_para.characters[
        drag_in_para_path[drag_in_para_path.length - 1] - 1
      ];
    const next_element =
      drag_in_para.characters[drag_in_para_path[drag_in_para_path.length - 1]];
    let drag_in_rows = drag_in_row.parent.children;

    let drag_focus_path: any = [];

    if (event && event.ctrlKey) {
      // 如果是按着ctrl拖拽 不删除原来的选区
      editor.selection.copyData(true);
      editor.selection.setCursorPosition(drag_in_path);
      // 粘贴后执行设置选区操作，将异步粘贴更改为同步 ，否则连续操作会有问题
      const afterPaste = async () => {
        await editor.paste(true);
        // 设置选区
        editor.selection.setSelectionByPath(
          drag_in_path,
          editor.selection.focus,
          "model_path"
        );
        editor.render(); // 如果这儿不立即调用editor.render 渲染选区会有延迟
      };
      afterPaste().then(() => null);
      return;
    }
    // 要在 cut 之前记录一下 否则 cut 完了之后就没有了
    const hasLastLinebreak =
      (!editor.selection.isCollapsed && editor.selection.hasLastLinebreak) ||
      editor.selection.isOneTable;
    editor.cut(true);
    if (PathUtils.isTablePath(drag_in_path)) {
      drag_focus_path = [
        drag_in_row.parent.parent?.cell_index,
        getCellIndex(drag_in_row.parent),
      ];
    } else {
      // 粘贴位置在表格外
      drag_in_rows = editor.current_cell.children;
    }

    if (!before_element) {
      const before_para = drag_in_row.parent.paragraph.find((item) => {
        return item.id === before_para_id;
      });
      if (isTable(before_para)) {
        drag_focus_path.push(...[before_para.cell_index + 1, 0]);
      } else if (isParagraph(before_para)) {
        const before_row =
          before_para.children[before_para.children.length - 1];
        drag_focus_path.push(...[before_row.cell_index + 1, 0]);
      } else {
        if (hasLastLinebreak && drag_in_path[drag_in_path.length - 2] !== 0) {
          // 判断不等于 0 是因为如果从一个表格拖拽到另外一个表格中 直接赋值就不对了 而如果拖到不是第 0 行的开头,就不走这儿了
          const path = editor.selection.start;
          drag_focus_path = path;
        } else {
          drag_focus_path.push(...[0, 0]);
        }
      }
    } else {
      for (let i = 0; i < drag_in_rows.length; i++) {
        const item = drag_in_rows[i];
        let is_before = true;
        if (isRow(item)) {
          const index = item.children.findIndex((item) => {
            if (item === before_element) {
              return true;
            } else if (item === next_element) {
              is_before = false;
              return true;
            } else {
              return false;
            }
          });
          if (index > -1) {
            drag_focus_path.push(...[i, is_before ? index + 1 : index]);
            break;
          }
        }
      }
    }
    // 重置focus和anchor
    if (editor.current_cell.children[drag_focus_path[0]]) {
      // 这个判断条件是为了解决 全选表格，拖动到表格下一行，程序崩了的问题
      editor.selection.setCursorPosition(drag_focus_path);
    }
    // 粘贴后执行设置选区操作，将异步粘贴更改为同步 ，否则连续操作会有问题
    const afterPaste = async () => {
      await editor.paste(true);
      // 设置选区
      editor.selection.setSelectionByPath(
        drag_focus_path,
        editor.selection.focus,
        "model_path"
      );
      editor.render();
    };
    afterPaste().then(() => null);
  }

  static  isInSelectedArea({ editor, x, y }: {editor: Editor, x: number, y: number}) {
    const { multipleSelected } = editor.selection;
    const copyMultipleSelected = JSON.parse(JSON.stringify(multipleSelected));
    if (multipleSelected.length) {
      // 这里都不用清空选区 应该是在 pointerdown 的时候就清空了选区了
      for (let i = 0; i <multipleSelected.length; i++) {

        const current = multipleSelected[i];
        editor.selection.setSelectionByPath(current.start, current.end, "para_path", 0, false);
        if (XSelection.hasCaret(editor, x, y)) {
          editor.selection.multipleSelected = copyMultipleSelected;
          return true;
        }
      }
      editor.selection.multipleSelected = copyMultipleSelected;
      return false
    }
    return XSelection.hasCaret(editor, x, y);
  }

  static selectAll(editor: Editor) {
    editor.selection.anchor = PathUtils.getDocumentStart(editor.current_cell);
    editor.selection.focus = PathUtils.getDocumentEnd(editor.current_cell);
    editor.selection.setSelectionByPath(
      editor.selection.anchor,
      editor.selection.focus,
      "model_path"
    );
    editor.render();
  }

  static changeAlign(editor: Editor, direction: alignType) {
    // 分组如果锁定 则不能编辑
    if (!editor.operableOrNot(["cell", "group"])) return false;
    if (direction === "docuAlign") {
      if (!editor.selection.isCollapsed) {
        EditorHelper.setDocumentAlign(
          editor,
          editor.selection.start,
          editor.selection.end
        );
      } else {
        EditorHelper.setDocumentAlign(editor);
      }
      return true;
    }
    if (
      direction === "top" ||
      direction === "middle" ||
      direction === "bottom"
    ) {
      EditorHelper.inRowPosition(editor, direction);
      return true;
    }
    // 文字对齐方向
    let selected_para = editor.selection.selected_para_info.paragraphs;
    selected_para =
      selected_para.length === 0
        ? [editor.selection.getFocusParagraph()]
        : selected_para;
    selected_para.forEach((e) => {
      e.changeAlign(direction);
    });
    return true;
  }

  static inRowPosition(editor: Editor, direction: alignType) {
    if (editor.selection.isCollapsed) {
      const row = editor.selection.getFocusRow();
      for (let i = 0; i < row.children.length; i++) {
        const char = row.children[i];
        if (
          char.field_position !== "normal" &&
          isCharacter(char) &&
          char.field_id
        ) {
          const field = editor.getFieldById(char.field_id);
          field && field.changeFontStyle({ ...field?.style, align: direction });
        }
        const font = editor.fontMap.add({ ...char.font, align: direction });
        char.font = font;
      }
      row.paragraph.updateChildren();
    } else {
      const selectedParaInfo = editor.selection.selected_para_info;
      const offsetInStartParagraph = selectedParaInfo.start_end[0]; // 选区第一段偏移量，表格内为-1
      const offsetInEndParagraph = selectedParaInfo.start_end[1]; // 选区最后一段偏移量，表格内为-1
      const paragraphs = selectedParaInfo.paragraphs; // 选区段落
      if (paragraphs.length > 1) {
        for (let i = 0; i < paragraphs.length; i++) {
          let start: number = 0;
          let end: number = 0;
          const paragraph = paragraphs[i];
          const chars_length = paragraph.characters.length;
          if (i === 0) {
            // 第一段改变样式的character
            start = offsetInStartParagraph === -1 ? 0 : offsetInStartParagraph;
            end = chars_length;
          } else if (i === paragraphs.length - 1) {
            // 最后一段改变样式的character
            start = 0;
            end =
              offsetInEndParagraph === -1 ? chars_length : offsetInEndParagraph;
          } else {
            // 中间段落改变样式的character
            start = 0;
            end = chars_length;
          }
          for (let j = start; j < end; j++) {
            const char = paragraph.characters[j];
            if (char.field_position !== "normal") {
              const field = editor.getFieldById(char.field_id);
              field &&
                field.changeFontStyle({ ...field?.style, align: direction });
            }
            const font = editor.fontMap.add({ ...char.font, align: direction });
            char.font = font;
          }
          paragraph.updateChildren();
        }
      } else {
        for (let j = offsetInStartParagraph; j < offsetInEndParagraph; j++) {
          const char = paragraphs[0].characters[j];
          if (char.field_position !== "normal") {
            const field = editor.getFieldById(char.field_id);
            field &&
              field.changeFontStyle({ ...field?.style, align: direction });
          }
          const font = editor.fontMap.add({ ...char.font, align: direction });
          char.font = font;
        }
        paragraphs[0].updateChildren();
      }
    }
  }

  static setDocumentAlign(
    editor: Editor,
    start_path?: number[],
    end_path?: number[]
  ) {
    if (!start_path) {
      start_path = editor.selection.focus;
    }
    // 传递两个参数 一个为 开始路径  一个为结束路径 可为空
    const start_para_path = editor.modelPath2ParaPath(start_path);
    const start_para = editor.selection.getParagraphByPath(start_para_path);
    // 对齐的距离为开始路径的位置
    const padding_value =
      start_para.characters[start_para_path[start_para_path.length - 1]].left;
    if (end_path) {
      const end_para_path = editor.modelPath2ParaPath(end_path);
      // 选区 或者文本域设置
      const deal_cell = start_para.cell;
      for (let i = 0; i < deal_cell.paragraph.length; i++) {
        const para = deal_cell.paragraph[i] as Paragraph;
        if (start_para_path[start_para_path.length - 2] === para.para_index) {
          para.title_length = start_path[start_path.length - 1];
        }
        // 区间内的段落对齐方式变化
        if (
          start_para_path[start_para_path.length - 2] <= para.para_index &&
          end_para_path[end_para_path.length - 2] >= para.para_index
        ) {
          para.content_padding_left = padding_value;
          para.align = "docuAlign";
          para.updateChildren();
        }
      }
    } else {
      // 单独设置某段落的文档对齐方式
      start_para.title_length = start_path[start_path.length - 1];
      start_para.content_padding_left = padding_value;
      start_para.align = "docuAlign";
      start_para.updateChildren();
    }
    // 此处需要重置光标，解决设置字符对齐后光标位置不正确导致报错的问题
    editor.selection.setCursorPosition(
      editor.paraPath2ModelPath(start_para_path)
    );
    editor.update();
    editor.render();
  }

  static setVerticalAlign(editor: Editor, alignDirection: VerticalAlign) {
    // 设置单元格垂直居中 或者向下对齐
    // 可以是光标在一个点 可以是一个区域 选择的是表格
    if (editor.selection.isCollapsed) {
      const focus_para = editor.selection.getFocusParagraph();
      const focus_cell = focus_para.cell;
      if (focus_cell.parent) {
        focus_cell.setVerticalAlign(alignDirection);
      }
    } else {
      const selected_cells_box = editor.selection.selected_cells;
      for (const cellObj of selected_cells_box) {
        cellObj.cell.setVerticalAlign(alignDirection);
      }
    }

    editor.update();
    editor.scroll_by_focus();
    editor.render();
  }

  static getElementAbsolutePositionByViewPath(
    editor: Editor,
    view_path: number[],
    containers: (
      | Page
      | Cell
      | Table
      | Row
      | Character
      | Image
      | Widget
      | Line
      | Box
      | Button
    )[],
    x: number = 0,
    y: number = 0
  ): { x: number; y: number } {
    const path = view_path;
    const offset = path.shift()!;
    if (path.length > 0) {
      const current_container = containers[offset] as Row;
      if (current_container) {
        x += current_container.left;
        y += current_container.top;
        if (editor.current_cell.hf_part && isPage(containers[0])) {
          return this.getElementAbsolutePositionByViewPath(
            editor,
            path,
            editor.current_cell.children,
            x,
            y
          );
        } else {
          return this.getElementAbsolutePositionByViewPath(
            editor,
            path,
            current_container.children,
            x,
            y
          );
        }
      }
    } else {
      // 当前containers是char数组
      let current_container;

      if (containers[offset]) {
        current_container = containers[offset];

        x += current_container.left;
      } else {
        // containers 长度为空，
        if (containers.length) {
          current_container = containers[containers.length - 1];
          x += current_container.left + current_container.width;
        }
      }
    }
    return { x, y };
  }

  static getFieldsByPlaceholder(
    editor: Editor,
    placeholders: string[],
    fieldType: "normal" = "normal"
  ): Map<string, XField[]> {
    const map = new Map<string, XField[]>();
    const fields = editor.getAllFields();
    for (const field of fields) {
      const placeholder = placeholders[placeholders.indexOf(field.placeholder)];
      if (placeholder && field.type === fieldType) {
        if (map.has(placeholder)) {
          map.get(placeholder)?.push(field);
        } else {
          map.set(placeholder, [field]);
        }
      }
    }
    return map;
  }

  static   getFieldsBySelection(parameter: {editor: Editor}): (XField| BoxField)[] {
    const { editor } = parameter;
    const fields: (XField| BoxField)[] = [];
    const selection = editor.selection;
    const fieldIdMap = new Map();
    if (selection.multipleSelected.length) {
      for (let i = 0; i < selection.multipleSelected.length; i++) {
        const current = selection.multipleSelected[i];
        fields.push(...EditorHelper.getFieldsBySelectedInfo(editor, current.selectedAreas, current.selectedCellPath, fieldIdMap));
      }

    } else {
      fields.push(...EditorHelper.getFieldsBySelectedInfo(editor, editor.selection.selected_areas, editor.selection.selected_cells_path, fieldIdMap));
    }
    // 不是顺序的 因为排序还得调用计算属性看位置 算了
    return fields;
  }

  static getFieldsBySelectedInfo(
    editor: Editor,
    selectedAreas:{
      start_para_path: Path;
      end_para_path: Path;
      last_linebreak?: boolean; // 区域的最后一行是否包含换行符
    }[],
    selectedCellPath: Path[],
    fieldIdMap: Map<string, boolean>
  ): (XField| BoxField)[]{
    let cell = editor.current_cell;
    const fields: (XField| BoxField)[] = [];
    for (const area of selectedAreas){
      // selectedAreas 里边只存了段落 所以就只能是在表格外边或者只在单元格里边
      if (area.start_para_path.length > 2) {
        cell = editor.current_cell.paragraph[area.start_para_path[0]].children[area.start_para_path[1]] as Cell;
      }

      const pStartIndex = area.start_para_path[area.start_para_path.length - 2];
      const cStartIndex = area.start_para_path[area.start_para_path.length - 1];
      const pEndIndex = area.end_para_path[area.end_para_path.length - 2];
      const cEndIndex = area.end_para_path[area.end_para_path.length - 1];

      const pArr = cell.paragraph.slice(pStartIndex, pEndIndex + 1);
      for (let i = 0; i < pArr.length; i++) {
        const p = pArr[i] as Paragraph;
        let startIndex = 0;
        let endIndex = p.characters.length;
        if (i === 0) {
          startIndex = cStartIndex;
        }
        if (i === pArr.length - 1) {
          endIndex = cEndIndex;
        }
        for (let j = startIndex; j < endIndex; j++) {
          const character = p.characters[j];
          if (character.field_id && !fieldIdMap.has(character.field_id)) {
            fieldIdMap.set(character.field_id, true);
            const field = editor.getFieldById(character.field_id);
            field && (fields.push(field));
          }
        }
      }
    }
    for (const cellPath of selectedCellPath) {
      const cell = editor.current_cell.paragraph[cellPath[0]].children[cellPath[1]] as Cell;
      fields.push(...cell.fields);
    }
    return fields;
  }

  static findContinuousParagraphs({ paragraphs }: {paragraphs: Paragraph[]}): Paragraph[] {
    const res = [];
    const firstParagraph = paragraphs[0];
    const currentCell = firstParagraph.cell;
    for (let i = firstParagraph.para_index - 1; i >= 0; i--) {
      const currentParagraph = currentCell.paragraph[i];
      if (isParagraph(currentParagraph)) {
        if (
          (!currentParagraph.itemsWidth ||
              !currentParagraph.itemsWidth.length ||
              currentParagraph.itemsWidth[0] !== "x") &&
            currentParagraph.align !== "right"
        ) {
          break;
        }
        if (currentParagraph.findSpecifyCharacterIndex("：") !== -1) {
          currentParagraph.align !== "right" && res.unshift(currentParagraph);
          break;
        }
        currentParagraph.align === "docuAlign" && res.unshift(currentParagraph);
      } else {
        break;
      }
    }
    res.push(...paragraphs);
    const lastParagraph = paragraphs[paragraphs.length - 1];
    for (
      let i = lastParagraph.para_index + 1;
      i < currentCell.paragraph.length;
      i++
    ) {
      const currentParagraph = currentCell.paragraph[i];
      if (isParagraph(currentParagraph)) {
        if (
          ((!currentParagraph.itemsWidth ||
              !currentParagraph.itemsWidth.length) &&
              currentParagraph.align !== "right") ||
            currentParagraph.findSpecifyCharacterIndex("：") !== -1 ||
            currentParagraph.align === "left"
        ) {
          break;
        }
        currentParagraph.align !== "right" && res.push(currentParagraph);
      } else {
        break;
      }
    }
    return res;
  }

  static formatParagraph({ editor, paragraphs, docuAlignPosition = 0, left = 0, update = true } : {editor: Editor, paragraphs: Paragraph[], docuAlignPosition?: number, left?: number, update?: boolean}) {
    if (!paragraphs.length) return;
    // 1. 第一步：先找到属于一组的所有段落
    // 先看看插入的这些段落是否是独立的
    let firstParagraph = paragraphs[0];
    let index = firstParagraph.findSpecifyCharacterIndex("：");
    // 新插入的这些段落没有开头的话 才有可能不是独立的 才需要去找其他段落
    if (index === -1) {
      paragraphs = editor.findContinuousParagraphs(paragraphs);
    } else {
      // TODO 如果找到了 其实也得找到这一组处方的所有段落 也有可能往下有好几个处方 所以还是要继续往下找
    }

    // 2. 第二步：修改第一个段落的 content_padding_left
    firstParagraph = paragraphs[0];
    index = firstParagraph.findSpecifyCharacterIndex("：");
    if (index !== -1) { // 找到了就赋值 没找到也不用管了吧 也用不着重置为 0
      firstParagraph.title_length = index + 1;
      firstParagraph.content_padding_left = firstParagraph.characters[index + 1].right;
    }
    // 3. 第三步：计算每一列的宽度
    // 3.1 整理数据
    const allRowDatas = []; // 保存每一行的数据
    for (let i = 0; i < paragraphs.length; i++) {
      const currentParagraph = paragraphs[i];
      currentParagraph.content_padding_left = firstParagraph.content_padding_left;
      currentParagraph.align = "docuAlign";
      currentParagraph.updateChildren();
      const currentRowDatas = []; // 二维数组，保存这一行的数据
      const arr = []; // 每个选项的数组，装的是 character
      // 我可以顺便在这里边将数据组装好，整理出来每一列的数据
      for (let j = 0; j < currentParagraph.characters.length; j++) {
        const character = currentParagraph.characters[j];
        if (!isCharacter(character)) {
          continue;
        }
        if (character.left >= firstParagraph.content_padding_left) {
          if (character.value !== "\n") { // 这样的话必须每个选项之间只能有一个空格，而且换行之后后边不允许有空格
            arr.push(character);
          }
          if (character.value === " " || character.value === "\n") {
            arr.length && currentRowDatas.push([...arr]);
            arr.length = 0;
          }
        }
      }
      if (currentRowDatas.length > 0) {
        allRowDatas.push([...currentRowDatas]);
      }
    }
    // 3.2 计算每一列的宽度
    // 3.2.1 找到当前剩余的行宽是多少
    const pageWidth = editor.root_cell.width - editor.root_cell.padding_left - editor.root_cell.padding_right;
    const restWidth = pageWidth - firstParagraph.content_padding_left;
    // 3.2.2 计算每一行最大的列数，找到所有行里边最大的列数 并且计算好每一列的最大宽度
    const colMaxWidth: any[] = [];
    for (let i = 0; i < allRowDatas.length; i++) { // i 代表的是第几行
      const rowDatas = allRowDatas[i];
      for (let j = 0; j < rowDatas.length; j++) { // j 代表的是第几列
        let width = 0;
        for (let n = 0; n < rowDatas[j].length; n++) {
          if (rowDatas[j][n].value !== " ") { // 空格就不需要加了 如果打字的时候 空格宽度也挺大
            width += rowDatas[j][n].width;
          }
        }
        if (colMaxWidth[j] === undefined) {
          colMaxWidth[j] = width;
        } else if (width > colMaxWidth[j]) {
          colMaxWidth[j] = width;
        }
      }
    }
    if (colMaxWidth.length === 0) {
      for (const para of paragraphs) {
        para.title_length = 0;
        para.content_padding_left = 0;
        para.itemsWidth = [];
        for (const character of para.characters) {
          if (isCharacter(character) && character.value === " ") { // 如果已经放不下了，原来的宽度还那么大就不合适了 需要恢复原状
            character.width = character.ori_width;
          }
        }
        para.updateChildren();
      }
      editor.refreshDocument(); // 如果不调用这个 在文档开头插入中草药 打字太多的时候 样式就乱啦
      return;
    }
    // 3.2.3 计算每列之间的空隙
    const allColSumWidth = colMaxWidth.reduce((prev, current) => prev + current);
    const colGap = (restWidth - allColSumWidth) / colMaxWidth.length; // 每一列的宽度间隙
    if (allColSumWidth > restWidth && restWidth <= pageWidth) {
      if (index === -1) {// 如果已经装不下了，但是没有冒号也不需要插入换行 是真的放不下了 就取消所有排版就可以了
        for (const para of paragraphs) {
          para.title_length = 0;
          para.content_padding_left = 0;
          para.itemsWidth = [];
          for (const character of para.characters) {
            if (isCharacter(character) && character.value === " ") {
              // 如果已经放不下了，原来的宽度还那么大就不合适了 需要恢复原状
              character.width = character.ori_width;
            }
          }
          para.updateChildren();
        }
        editor.refreshDocument(); // 如果不调用这个 在文档开头插入中草药 打字太多的时候 样式就乱啦
        return;
      }
      // 缝隙太小 并且有文字占位 那么就整体下移一行 否则放不下就该文字重叠
      const paraPath = [firstParagraph.para_index, firstParagraph.title_length];
      editor.selection.setCursorPosition(editor.paraPath2ModelPath(paraPath));

      // 要先清空 itemsWidth 和 更改 align 否则下次循环就还是有 content_padding_left 然后第一行就不进行排版了
      for (const para of paragraphs) {
        para.content_padding_left = 0;
        para.align = "left"
        para.updateChildren();
      }
      editor.insertText("\n");
      if (firstParagraph.itemsWidth) {
        firstParagraph.itemsWidth.length = 0;
      }
      const paras = editor.current_cell.paragraph.slice(firstParagraph.para_index + 1, firstParagraph.para_index + paragraphs.length + 1) as Paragraph[];
      for (const para of paras) {
        para.content_padding_left = 0;
        para.align = "left"
      }

      // 如果不设置光标位置的话，当首行字数太多需要换行的时候就会有问题
      editor.selection.setCursorPosition(editor.paraPath2ModelPath([paras[paras.length - 1]?.para_index || 0, paras[paras.length - 1]?.characters?.length || 0]));
      editor.formatParagraph(paras);
      editor.refreshDocument();
      return;
    }

    // 3.3 计算每一列的真实宽度
    for (let i = 0; i < colMaxWidth.length; i++) {
      colMaxWidth[i] += colGap;
    }

    for (let i = 0; i < paragraphs.length; i++) {
      const currentParagraph = paragraphs[i];
      currentParagraph.itemsWidth = ["x"]; // 很重要 为了两个处方能保持一致
      const before = currentParagraph.children.length;
      currentParagraph.createRow({
        colMaxWidth
      });
      currentParagraph.cell.updateChildren({
        updateParagraphIndex: currentParagraph.para_index,
        updateRowCount: before,
        startRowIndex: currentParagraph.children[0].cell_index,
        callTypesetting: true
      });
    }
    // editor.selection.setCursorPosition(editor.paraPath2ModelPath([paragraphs[paragraphs.length - 1]?.para_index || 0, paragraphs[paragraphs.length - 1]?.characters?.length || 0]));
    update && editor.update();
    update && editor.render();
  }

  static formatBrush(editor: Editor) {
    editor.format_brush = true;
    if (!editor.selection.isCollapsed) {
      editor.internal.brush_selection = true;
      const start = editor.selection.start;
      const end = editor.selection.end;
      const start_row = editor.selection.getRowByPath(start);
      let element = null;
      for (
        let i = start[start.length - 1];
        i < start_row.children.length;
        i++
      ) {
        const char = start_row.children[i];
        if (char.field_position === "normal") {
          element = char;
          break;
        }
      }
      let font_style = null;
      if (isCharacter(element)) {
        font_style = element.font;
      }
      // 选区开始的位置是图片的情况
      if (isImage(element)) {
        const begin = start[start.length - 1];
        let over = start_row.children.length;
        // 如果选区开始和结束在同一行
        if (PathUtils.isSameRow(start, end)) {
          over = end[end.length - 1] + 1;
        }
        for (let i = begin; i < over; i++) {
          const sure_element = start_row.children[i];
          if (isCharacter(sure_element)) {
            font_style = sure_element.font;
            break;
          }
        }
      }
      if (font_style) {
        editor.contextState.setFontState(font_style);
      }
    } else if (editor.selection.isCollapsed) {
      editor.internal.brush_selection = false;
      editor.setParagraphState();
    }
  }

  static changeCellStyle({ editor, cells, style }: {editor: Editor, cells?: Cell[], style: any}) {
    if (!cells) {
      cells = [];
      if (editor.selection.isCollapsed) {
        const cell = editor.selection.getFocusCell();
        if (cell) {
          cells.push(cell);
        }
      } else {
        cells = editor.selection.selected_cells.map(({ cell }) => cell);
      }
    }
    if (!cells.length) return
    for (const cell of cells) {
      cell.style = { ...cell.style, ...style }
    }
    editor.render();
    return true;
  }

  static averageRowHeight({ editor }: {editor: Editor}) {
    const range = editor.selection.getCellRange();
    if (!range) return
    const { minRow, maxRow, table } = range;
    if (isTable(table)) {
      const rowSize = table.row_size.slice(minRow, maxRow + 1);
      const sum = rowSize.reduce((t, c) => t + c);
      const every = sum / rowSize.length;
      for (let i = 0; i < table.row_size.length; i++) {
        if (i >= minRow && i <= maxRow) {
          table.row_size[i] = every;
          table.min_row_size[i] = every;
        }
      }
      table.children.forEach((c) => c.typesetting());
      const newRowSize = table.row_size.slice(minRow, maxRow + 1);
      const max = Math.max(...newRowSize);
      const min = Math.min(...newRowSize);
      if (max !== min) {
        for (let i = 0; i < table.row_size.length; i++) {
          if (i >= minRow && i <= maxRow) {
            table.row_size[i] = max;
            table.min_row_size[i] = max;
          }
        }
      }
      if (editor.selection.anchor.length === 4) {
        editor.selection.anchor.splice(-1, 1, 0);
        editor.selection.anchor.splice(-2, 1, 0);
      }
      if (editor.selection.focus.length === 4) {
        editor.selection.focus.splice(-1, 1, 0);
        editor.selection.focus.splice(-2, 1, 0);
      }
      editor.selection.setSelectionByPath(editor.selection.anchor, editor.selection.focus, "model_path");
      editor.refreshDocument();
      return true;
    }
  }

  static averageColWidth({ editor }: {editor: Editor}) {
    const range = editor.selection.getCellRange();
    if (!range) return
    const { minCol, maxCol, table } = range;
    if (isTable(table)) {
      const colSize = table.col_size.slice(minCol, maxCol + 1);
      const sum = colSize.reduce((t, c) => t + c);
      const every = sum / colSize.length;
      for (let i = 0; i < table.col_size.length; i++) {
        if (i >= minCol && i <= maxCol) {
          table.col_size[i] = every;
        }
      }
      table.children.forEach((c) => c.typesetting());
      if (editor.selection.anchor.length === 4) {
        editor.selection.anchor.splice(-1, 1, 0);
        editor.selection.anchor.splice(-2, 1, 0);
      }
      if (editor.selection.focus.length === 4) {
        editor.selection.focus.splice(-1, 1, 0);
        editor.selection.focus.splice(-2, 1, 0);
      }
      editor.selection.setSelectionByPath(editor.selection.anchor, editor.selection.focus, "model_path");
      editor.refreshDocument();
      return true;
    }
  }

  static clearStyle(editor: Editor) {
    const font_style = editor.config.default_font_style;
    if (!editor.selection.isCollapsed) {
      editor.changeFontStyleBySelection(font_style);
      editor.changeAlign("left");
    } else {
      default_para_style.row_ratio = editor.config.row_ratio;
      const selected_para_style: ParagraphStyle = new ParaStyle(
        default_para_style
      );
      // 设置对应样式
      editor.contextState.setParagraphState(selected_para_style);
      editor.change_paragraph_style();
    }
    editor.update();
    editor.render();
  }

  static setSelectImageOrWidget(
    editor: Editor,
    container_info: any,
    focus_field: XField | BoxField | null
  ) {
    setFocusImage(null);
    const element = container_info.element;
    const box_widget = focus_field
      ? (focus_field as BoxField).box_widget
      : null;
    if (element) {
      const { model_path } = container_info;
      const offset = container_info.row.children.indexOf(element);
      // 获取元素真实坐标
      model_path[model_path.length - 1] = offset;
      if (element instanceof Image) {
        if (!editor.operableOrNot(["cell", "group"])) return false;
        const field = editor.selection.getFocusField();
        const table = editor.current_cell.children[model_path[0]];
        // TODO 应该在这儿判断吗? 应该在外部判断是否聚焦 然后该方法就只管聚焦更好 函数更纯
        if (!field && editor.view_mode === "form" && !editor.adminMode) {
          if (!(isTable(table) && table.editableInFormMode)) {
            return true;
          }
        }
        if (
          (!field || !field.readonly) &&
          !(isTable(table) && table.name === TABLE_NAME_FOR_PACS_IMAGE_LAYOUT)
        ) {
          // 只在没有设置文本域 或者 文本域没有设置只读的时候 才设置focusImage
          // pacs 表格里边的图片不能设置焦点(因为不让拖动)
          setFocusImage(element);
        }
        //给图片设置FocusElement
        const row = editor.selection.getRowByPath(editor.selection.focus);
        const view_path = editor.modelPath2viewPath(model_path)
        const page = editor.editFloatModelMode ? editor.pages[0] : editor.pages[view_path[0]];
        EditorHelper.setFocusElement(editor, field, row, page);
        const img_para_path = editor.modelPath2ParaPath(model_path);
        setImageParaPath(img_para_path);
        // 获取element的真实坐标后一位的坐标，设置光标位置
        const para_end_path = [...img_para_path];
        PathUtils.movePathCharNum(para_end_path, 1);
        editor.selection.setSelectionByPath(img_para_path, para_end_path);
        editor.internal.point_is_selected = true;
        return false;
      } else if (isWidget(element) || box_widget) {
        if (focus_field && box_widget) {
          (focus_field as BoxField).updateBoxChecked(!box_widget.selected);
          // editor.locatePathInField(focus_field, "start");
          editor.event.emit("boxChecked", {
            field: focus_field,
            element,
            checked: !!(focus_field as BoxField).box_checked,
          });
        } else {
          // 获取到model数据中对应的元素
          const model_row = editor.selection.getRowByPath(model_path);
          const model_element = model_row.children[offset];
          if (isWidget(model_element)) {
            model_element.selected = !model_element.selected;
          }
          // element.selected = !element.selected;
          editor.event.emit("boxChecked", {
            field: focus_field,
            element,
            checked: element.selected,
          });
        }
        editor.refreshDocument();
        return true;
      }
    }
  }

  static getCopiedEditor(editor: Editor, rawData?: any): Editor {
    const { editor2 } = initEditor(editor);
    if (rawData) {
      editor2.reInitRaw(rawData);
      editor2.refreshDocument();
    }
    return editor2;
  }

  static getPrintEditor(editor: Editor, rawData?: any) {
    const { editor2 } = initPrintEditor(editor, rawData);
    editor2.imageMap = new ImageMap(editor2);
    return editor2;
  }

  static getRawData(editor: Editor) {
    const other_info: any = editor.packSaveDocumentInfo({ isGetAllDocumentMeta: true });
    // const rawData = rawDataTrans.modelDataToRawData(
    //   editor.header_cell,
    //   editor.root_cell,
    //   editor.footer_cell,
    //   other_info
    // );
    other_info.extraInfo = {
      isGetRawData: true
    }
    const rawData = editor.event.emit(
      "modelData2RawData",
      editor.header_cell,
      editor.root_cell,
      editor.footer_cell,
      other_info
    );
    return getRawDataByConfig(editor, rawData);
  }

  static getAllBlobPromise(editor: Editor) {
    const { editor2, temporaryCanvas } = initPrintEditor(editor);

    const blobPromiseArr: Promise<Blob | unknown>[] = [];

    for (let i = 0, len = editor.pages.length; i < len; i++) {
      const page = editor2.pages[i];
      editor2.scroll_top = page.top;
      editor2.render();
      blobPromiseArr.push(editor.getSingleBlobPromise(temporaryCanvas));
    }
    return blobPromiseArr;
  }

  static getAbsoluteXYByPointer(editor: Editor, x: number, pointer_y: number) {
    const current_element = editor.getContainerInfoByPointHelper(
      x,
      pointer_y + editor.scroll_top
    );
    if (current_element) {
      const { view_path, row } = current_element;
      const absolute_x_y =
        editor.getElementAbsolutePositionByViewPath(view_path);
      if (row.parent.parent) {
        absolute_x_y.y += 1;
      } else {
        const previous = row.parent.children[row.cell_index - 1];
        if (previous && isTable(previous)) {
          absolute_x_y.y += 1;
        }
      }
      return absolute_x_y;
    } else {
      const y = pointer_y + editor.scroll_top;
      return { x, y };
    }
  }

  static insertPaginationSymbol(editor: Editor) {
    if (!editor.selection.isCollapsed) {
      editor.selection.clearSelectedInfo();
      editor.selection.setCursorPosition(editor.selection.start);
    }
    if (!PathUtils.isTablePath(editor.selection.focus)) {
      editor.caret.cache = null;
      // 光标行
      let focus_row = editor.selection.getFocusRow();
      if (focus_row) {
        const para_path = [...editor.selection.para_focus];
        // 参数为 text 行数 字符数 文字样式
        focus_row.paragraph.insertText(
          "\n",
          para_path[para_path.length - 1],
          editor.contextState.getFontState()
        );

        editor.selection.stepForward(1, editor.selection.getFocusRow());
        focus_row = editor.selection.getFocusRow();
        const paragraph = focus_row.paragraph;

        const preParagraph = paragraph.previousParagraph;
        if (isParagraph(preParagraph)) {
          preParagraph.page_break = true;
          preParagraph.updateChildren();
        }
        paragraph.updateChildren();
        editor.update();
        editor.scroll_by_focus();
        editor.render();
        return true;
      }
    }
  }

  static viewPath2ModelPath(editor: Editor, view_path: Path) {
    const path = [...view_path];
    // 获取根节点
    // TODO  暂时 editor.currentFloatModel!.children[0]editor.currentFloatModel!.children[0] 先写死 0 , 我是不是得改变一下获取 view_path 的地方 改成 editFloatModelMode 模式下专用的
    let container = editor.editFloatModelMode
      ? editor.currentFloatModel!.children[path[1]]
      : editor.pages[path[0]].children[path[1]];
    if (editor.current_cell.hf_part) {
      const page = editor.pages[path[0]];
      const hfPart = page[editor.current_cell.hf_part];
      container = hfPart.children[path[1]];
    }
    // 表格情况
    if (isTable(container)) {
      path.shift();

      path[0] = container.getOrigin().cell_index; // 原始表格在model中的索引

      if (path.length === 1) return path; // 此时路径表示的是一个table

      const current_split_cell = container.children[path[1]];

      const cell_origin = current_split_cell.getOrigin();

      path[1] = getCellIndex(cell_origin);

      if (path.length === 2) return path; // 此时路径表示的是一个cell

      // cell被拆分
      if (cell_origin.split_parts.length) {
        const split_cell_index = cell_origin.split_parts.findIndex(
          (item) => item.id === current_split_cell.id
        );

        path[2] =
          split_cell_index === 0
            ? path[2]
            : cell_origin.split_row_indexes[split_cell_index - 1] + path[2];
      }

      return path;
    }

    const cell_index = container.cell_index;
    path.splice(0, 2);

    path.unshift(cell_index);

    return path;
  }

  static modelPath2ViewPath(editor: Editor, path: Path) {
    if (editor.editFloatModelMode) {
      const index = editor.floatModels.findIndex(
        (floatModel) => floatModel === editor.currentFloatModel
      );
      return [index, ...path];
    }
    const view_path = path.slice(1);
    const rowOrTbl = editor.current_cell.children[path[0]];
    // 根容器是一个被拆分的table
    if (isTable(rowOrTbl) && rowOrTbl.split_parts.length) {
      const current_cell = rowOrTbl.children[path[1]];
      if (current_cell.split_parts.length) {
        // 当前cell被分割
        const origin_row_index = path[2];

        let current_cell_split_part_index: number = 0;

        let current_split_row_index!: number;

        if (current_cell.split_row_indexes.length) {
          // 存在被拆分的单元格子元素是空的情况
          for (let i = 0; i < current_cell.split_row_indexes.length; i++) {
            const current_part_row_index = current_cell.split_row_indexes[i];

            if (origin_row_index < current_part_row_index) {
              current_cell_split_part_index = i;

              current_split_row_index =
                i === 0
                  ? origin_row_index
                  : origin_row_index - current_cell.split_row_indexes[i - 1];

              break;
            }
            if (
              i === current_cell.split_row_indexes.length - 1 &&
              origin_row_index >= current_part_row_index
            ) {
              current_cell_split_part_index = i + 1;

              current_split_row_index =
                origin_row_index - current_part_row_index;
              break;
            }
          }
        } else {
          // 被拆分的单元格除第一个都是空单元格
          current_cell_split_part_index = 0;

          current_split_row_index = path[2];
        }
        const current_split_cell =
          current_cell.split_parts[current_cell_split_part_index];

        const current_page_number = current_split_cell.parent!.page_number;

        const current_table_index = current_split_cell.parent!.page_index;

        const current_split_cell_index = getCellIndex(current_split_cell);

        return [
          current_page_number - 1,
          current_table_index,
          current_split_cell_index,
          current_split_row_index,
          path[path.length - 1],
        ];
      } else {
        const current_row = current_cell.children[path[2]] as Row;

        const { split_table, current_cell_index } = editor.getSplitTable(
          rowOrTbl,
          current_row
        );
        // 当前cell未被分割
        const current_page_number: number = split_table.page_number;

        const current_table_index: number = split_table.page_index;

        return [
          current_page_number - 1,
          current_table_index,
          current_cell_index,
          path[path.length - 2],
          path[path.length - 1],
        ];
      }
    }

    const page_number = editor.internal.current_page
      ? editor.internal.current_page.number
      : rowOrTbl.page_number;

    const page_index = rowOrTbl.page_index;

    view_path.splice(0, 0, page_number - 1, page_index); // page_number 是从1开始的，此处减一
    return view_path;
  }

  static modelPath2ParaPath(editor: Editor, model_path: Path): Path {
    const container = editor.current_cell.children[model_path[0]];
    let para_path: Path = [];
    let char_index: number;
    let paragraph: Paragraph;
    // 当传入的为表格的model_path时
    if (isTable(container)) {
      para_path = para_path.concat([container.para_index, model_path[1]]);
      const cell = container.children[model_path[1]] as Cell;
      const cell_row = cell.children[model_path[2]] as Row;
      paragraph = cell_row.paragraph as Paragraph;
      let cell_char: Character | Image | Widget | Line | Box | Button | null =
        cell_row.children[model_path[3]];
      // 当用户将光标定位到行尾的时候，此时应获取下一行的第一个字符
      if (!cell_char) {
        const next_cell_row =
          paragraph.children[cell_row.row_index_in_para + 1];
        cell_char = next_cell_row ? next_cell_row.children[0] : null;
      }
      para_path.push(paragraph.para_index);
      char_index = paragraph.characters.indexOf(cell_char);
    } else {
      paragraph = (container as Row).paragraph;
      para_path.push(paragraph.para_index);
      let char: Character | Image | Widget | Line | Box | Button | null =
        container.children[model_path[1]];
      // 当char为null并且当前所在位置不是段落末尾处时
      if (!char) {
        const next_row = paragraph.children[container.row_index_in_para + 1];
        char = next_row ? next_row.children[0] : null;
      }
      char_index = paragraph.characters.indexOf(char);
    }
    // 在段落末尾处时
    if (char_index === -1) {
      char_index =
        paragraph.characters.length > 0 ? paragraph.characters.length - 1 : 0;
    }

    para_path.push(char_index);
    return para_path;
  }

  static isClickMobileSelection() {

  }
  static paraPath2ModelPath(

    editor: Editor,
    para_path: Path,
    current?: string
  ): Path {
    // 担心调用的地方没有editor所以传contnet header footer 在这里拿正确的cell
    let current_cell: Cell;
    if (current === "header") {
      current_cell = editor.header_cell;
    } else if (current === "footer") {
      current_cell = editor.footer_cell;
    } else if (current === "content") {
      current_cell = editor.root_cell;
    } else {
      current_cell = editor.current_cell;
    }
    const container = current_cell.paragraph[para_path[0]];
    let model_path: Path = [];
    let paragraph: Paragraph;
    let char: Character;
    if (isTable(container)) {
      model_path = model_path.concat([container.cell_index, para_path[1]]);
      const cell = container.children[para_path[1]];
      paragraph = cell.paragraph[para_path[2]] as Paragraph;
      char = paragraph.characters[para_path[3]];
    } else {
      paragraph = current_cell.paragraph[para_path[0]] as Paragraph;
      char = paragraph.characters[para_path[1]];
    }
    const para_rows = paragraph.children;
    if (!char || char.value === "\n") {
      let char_index = para_rows[para_rows.length - 1].children.length;
      if (!para_path[para_path.length - 1]) {
        char_index = 0;
      }
      return model_path.concat([
        para_rows[para_rows.length - 1].cell_index,
        char_index,
      ]);
    }
    for (let i = 0; i < para_rows.length; i++) {
      const row = para_rows[i];
      const char_index = row.children.indexOf(char);
      if (char_index > -1) {
        model_path = model_path.concat([row.cell_index, char_index]);
        break;
      }
    }
    return model_path;
  }

  static modelCellPath2viewCellPath(
    editor: Editor,
    model_path: Path
  ): Path[] | undefined {
    // TODO 不考虑嵌套表格
    const path = [...model_path];

    let view_path: Path[] = [];

    const current_table = editor.current_cell.children[path[0]];

    if (isTable(current_table)) {
      const current_cell = current_table.children[path[1]];

      if (current_table.split_parts.length) {
        // 表格被分割
        if (current_cell.split_parts.length) {
          // cell被分割
          for (let i = 0; i < current_cell.split_parts.length; i++) {
            const current_view_cell = current_cell.split_parts[i];

            const view_table = current_view_cell.parent!;

            const view_cell_index = getCellIndex(current_view_cell);

            const path = [
              view_table.page_number - 1,
              view_table.page_index,
              view_cell_index,
            ];

            view_path.push(path);
          }
        } else {
          const current_row = current_cell.children[model_path[2]] as Row;
          // table 被分割，cell未被分割
          const { split_table, current_cell_index } = editor.getSplitTable(
            current_table,
            current_row
          );

          const path = [
            split_table.page_number - 1,
            split_table.page_index,
            current_cell_index,
          ];

          view_path = [path];
        }
      } else {
        // 表格未被分割
        const cell_index = getCellIndex(current_cell);

        const path = [
          current_table.page_number - 1,
          current_table.page_index,
          cell_index,
        ];

        // 避免 编辑页眉页脚的时候出现 -1
        if (editor.is_edit_hf_mode) {
          path[0] = (editor.internal.current_page?.number || 1) - 1;
        }
        view_path = [path];
      }
      return view_path;
    }
  }

  static getBodyStr(editor: Editor) {
    const allParagraph = editor.current_cell.paragraph;
    let str = "";
    for (const item of allParagraph) {
      str += item.getStr(true, false, {
        excludeUnselected: true
      });
    }
    return str;
  }

  static addHighLight(editor: Editor, characters: Character[]) {
    editor.internal.current_has_temp_bg_color_characters = characters;
    characters.forEach((char) => {
      char.temp_bgColor = editor.config.comment.wordSelectedBgColor;
    });
  }

  static addList(editor: Editor, isOrder: boolean, paragraphs?: Paragraph[]) {
    // 分组如果锁定 则不能编辑
    if (!editor.operableOrNot(["cell", "group"])) return false;
    const sel_info =
      paragraphs || editor.selection.selected_para_info.paragraphs; // 选区段落
    if (sel_info.length) {
      for (const para of sel_info) {
        para.toListOrOrdinary(isOrder);
      }
    } else {
      // 光标行
      const paragraph = editor.selection.getFocusParagraph();
      paragraph.toListOrOrdinary(isOrder);
    }
    editor.update();
    editor.scroll_by_focus();
    editor.render();
    return true;
  }

  /**
   * 只有光标位置处已经是列表时,才能进行改变,否则就只能是走另外的函数(添加逻辑) 只修改光标位置处的一个列表
   * @param editor Editor
   * @param numStyle
   * @returns
   */
  static changeListNumStyle(
    editor: Editor,
    numStyle: "number" | "chinese"
  ): Boolean {
    // 分组如果锁定 则不能编辑
    if (
      editor.selection.getFocusGroup()?.lock ||
      !editor.operableOrNot(["cell"])
    )
      return false;
    const focusParagraph = editor.selection.getFocusParagraph();
    const configIndent = editor.config.listItemIndent;
    if (
      focusParagraph &&
      focusParagraph.islist &&
      isParagraph(focusParagraph) &&
      focusParagraph.isOrder
    ) {
      // 往上找,一旦找到层级为 1 并且 序号为 1 的就说明到头了 循环结束
      for (let i = focusParagraph.para_index; i >= 0; i--) {
        const currentParagraph = focusParagraph.cell.paragraph[i];
        if (isParagraph(currentParagraph)) {
          // 循环到层级是 1 并且序号是 1 就说明当前这个列表往上到头了 当前段落是该列表中的最后一个
          if (
            currentParagraph.level === 1 &&
            currentParagraph.list_index === 1
          ) {
            if (isNotNullAndUndefined(configIndent) &&  numStyle === "number") {
              currentParagraph.listItemIndent = configIndent
            }
            currentParagraph.listNumStyle = numStyle;
            currentParagraph.updateChildren();
            break;
          }
          if (currentParagraph.islist) {
            if (isNotNullAndUndefined(configIndent) &&  numStyle === "number") {
              currentParagraph.listItemIndent = configIndent
            }
            currentParagraph.listNumStyle = numStyle;
            currentParagraph.updateChildren();
          }
        }
      }

      // 往下找,一旦找到层级为 1 并且 序号为 1 的就说明循环到头了 结束循环 只是单纯的不是列表说明不了什么 因为有可能隔了几个段落 又有一个续上了
      for (
        let i = focusParagraph.para_index + 1;
        i < focusParagraph.cell.paragraph.length;
        i++
      ) {
        const currentParagraph = focusParagraph.cell.paragraph[i];
        if (isParagraph(currentParagraph)) {
          // 循环到当前不是列表 或者 是列表但是层级是 1 ,并且序号也是 1 (就说明是下一个列表了) 就停止 并且不包括当前段落
          if (
            currentParagraph.list_index === 1 &&
            currentParagraph.level === 1
          ) {
            break;
          }
          if (currentParagraph.islist) {
            if (isNotNullAndUndefined(configIndent) &&  numStyle === "number") {
              currentParagraph.listItemIndent = configIndent
            }
            currentParagraph.listNumStyle = numStyle;
            currentParagraph.updateChildren();
          }
        }
      }
      editor.update();
      editor.render();
      return true;
    }
    return false;
  }

  static reStartListIndex(editor: Editor) {
    // 分组如果锁定 则不能编辑
    if (editor.selection.getFocusGroup()?.lock) return false;
    if (editor.selection.getFocusCell()?.lock) return false;
    const focus_para = editor.selection.getFocusParagraph();
    if (!focus_para.islist && focus_para.level !== 1) return;
    focus_para.restart_list_index = !focus_para.restart_list_index;
    focus_para.updateChildren();
    editor.update();
    editor.scroll_by_focus();
    editor.render();
  }

  static addFourSpaces(
    editor: Editor,
    focus_row: Row,
    para_path: number[],
    text: string
  ) {
    // 文本域如果只读 则不能编辑
    const focus_field = editor.selection.getFocusField();
    if (focus_field && focus_field.isReadonly) {
      return false;
    }
    focus_row.paragraph.insertText(
      text,
      para_path[para_path.length - 1],
      editor.contextState.getFontState(),
      focus_field
    );
    editor.selection.stepForward(1, focus_row);
  }

  static unHighLight(editor: Editor, characters: Character[]) {
    characters.forEach((char) => {
      char.temp_bgColor = undefined;
    });
    editor.internal.current_has_temp_bg_color_characters = [];
  }

  // static updateHeaderFooterInfo (editor: Editor) {
  //   const headerFieldsInfo = getHeaderFieldMaxLength(editor.root_cell);
  //   editor.header_diff_info = {};
  //   for (let i = 0; i < editor.pages.length; i++) {
  //     const page = editor.pages[i];
  //     page.footer.initCellData();
  //     page.updateHeaderFooterShowInfo(headerFieldsInfo);
  //   }
  // }

  static setViewScale(editor: Editor, val: number = 1) {
    // let max_val =
    //   editor.init_canvas.width /
    //   editor.config.devicePixelRatio /
    //   editor.page_size.width;
    if (editor.isMobileTerminal()) {
      const max_val = 10;
      if (val < 0.1 || val > max_val) {
        return false;
      }
    }
    editor.viewScale = val;
    editor.updateCanvasSize();
    editor.render();
    return true;
  }

  static download(filename: string, text: string) {
    const pom = document.createElement("a");
    pom.setAttribute(
      "href",
      "data:text/plain;charset=utf-8," + encodeURIComponent(text)
    );
    pom.setAttribute("download", filename);
    if (document.createEvent) {
      const event = document.createEvent("MouseEvents");
      event.initEvent("click", true, true);
      pom.dispatchEvent(event);
    } else {
      pom.click();
    }
  }

  static getAbsolutePositionByViewPosition({ editor, view_x, view_y } : {editor: Editor, view_x: number, view_y: number}) {
    const x =
      Math.round(
        (view_x / editor.viewScale - editor.internal.view_scale_offset) * 10
      ) / 10;
    const y = Math.round((view_y / editor.viewScale) * 10) / 10;
    return { x, y };
  }

  static downloadFontFile(editor: Editor, isRareCharacter: boolean = false) {
    let data = prompt("数据");
    const family = prompt("字体");

    if (!(data && family)) return;

    const fontShapeArr = ["常规", "加粗", "倾斜", "加粗倾斜"];
    for (const fontShape of fontShapeArr) {
      console.log(fontShape, "正在处理的字形");
      const fontStyle = {
        family,
        bold: false,
        italic: false,
        height: 0,
      };

      if (fontShape === "加粗") {
        fontStyle.bold = true;
      } else if (fontShape === "倾斜") {
        fontStyle.italic = true;
      } else if (fontShape === "加粗倾斜") {
        fontStyle.bold = true;
        fontStyle.italic = true;
      }
      for (const fontHeight of fontHeightAsc) {
        fontStyle.height = fontHeight;
        const font = new Font(fontStyle);
        const chars = specialCharHandle.splitString(data);
        for (let i = 0; i < chars.length; i++) {
          const character = chars[i];
          const { width } = Renderer.measure(font, character);
          handleSaveFontFace(font, character, width);
        }
      }
      for (const fontHeight of fontHeightAsc) {
        fontStyle.height = fontHeight;
        const font = new Font(fontStyle);
        if (isRareCharacter) {
          data = specialCharHandle.replaceChar(data);
        }
        for (let i = 0; i < data.length; i++) {
          const character = isRareCharacter
            ? specialCharHandle.restoreChar(data[i])
            : data[i];
          const { width } = Renderer.measure(font, character);
          handleSaveFontFace(font, character, width);
        }
      }
    }

    editor.download(family, JSON.stringify(fontFaceObj));
  }

  static toggleEyeProtectionMode(
    editor: Editor,
    open: boolean = true,
    color: string = "rgb(199,237,204)"
  ) {
    if (open) {
      editor.config.page_color = color;
    } else {
      editor.config.page_color = "#ffffff";
    }
    editor.render();
  }

  static saveMultipleSelectionByCurrent({ editor }: {editor: Editor}) {
    const { multipleSelected, selected_areas,  selected_cells_path, para_start, para_end } = editor.selection;
    if (selected_areas.length || selected_cells_path.length) {
      multipleSelected.push({
        selectedAreas: selected_areas,
        selectedCellPath: selected_cells_path,
        start: para_start,
        end: para_end
      })

      multipleSelected.sort((a, b) => {
        // 首先我要在 a b 里边找到最后边的路径
        const [a1, a2, a3, a4] = a.end;
        const [b1, b2, b3, b4] = b.end;
        if (a1 !== b1) {
          return b1 - a1;
        }
        if (a2 !== b2) {
          return b2 - a2;
        }
        if (a3 !== b3) {
          return b3 - a3;
        }
        return b4 - a4;
      })
    }
  }


  static drawScrollBar(editor: Editor) {
    const parentDom = editor.init_canvas.parentNode as HTMLElement;
    if (!parentDom) return;
    Renderer.draw_rectangle(
      parentDom.offsetWidth - 15 + editor.scrollX,
      0,
      15,
      editor.init_canvas.height / editor.config.devicePixelRatio,
      "rgb(241,241,241)"
    );

    const top_start = [parentDom.offsetWidth + editor.scrollX - 7.5, 2.5];
    const top_break = [parentDom.offsetWidth + editor.scrollX - 12.5, 7.5];
    const top_end = [parentDom.offsetWidth + editor.scrollX - 2.5, 7.5];
    Renderer.drawTriangle(top_start, top_break, top_end, "rgb(192,192,192)");
    const bottom_start = [
      parentDom.offsetWidth + editor.scrollX - 7.5,
      editor.init_canvas.height / editor.config.devicePixelRatio - 2.5,
    ];
    const bottom_break = [
      parentDom.offsetWidth + editor.scrollX - 12.5,
      editor.init_canvas.height / editor.config.devicePixelRatio - 7.5,
    ];
    const bottom_end = [
      parentDom.offsetWidth + editor.scrollX - 2.5,
      editor.init_canvas.height / editor.config.devicePixelRatio - 7.5,
    ];
    Renderer.drawTriangle(
      bottom_start,
      bottom_break,
      bottom_end,
      "rgb(192,192,192)"
    );
    // 所有页面和页面间距加起来的高度
    const complete_height =
      ((editor.page_size.height + editor.config.page_margin_bottom) *
        editor.pages.length +
        editor.config.editor_padding_top) *
      editor.viewScale;
    // canvas高度
    const canvas_height =
      editor.init_canvas.height / editor.config.devicePixelRatio;
    // 滚动条高度
    const scroll_bar_height =
      (canvas_height * (canvas_height - 20)) / complete_height;
    // 滚动条滚动高度

    const scroll_height =
      ((editor.scroll_top * (canvas_height - scroll_bar_height - 20)) /
        (complete_height - canvas_height)) *
      editor.viewScale;
    Renderer.drawRoundRect(
      parentDom.offsetWidth + editor.scrollX - 13,
      10 + scroll_height,
      11,
      scroll_bar_height,
      5,
      "rgba(192,192,192)"
    );

    // Renderer.draw_rectangle(
    //   parentDom.offsetWidth + editor.scrollX - 13,
    //   10 + scroll_height,
    //   11,
    //   scroll_bar_height,
    //   "rgb(192,192,192)"
    // );
  }

  static drawSelectionCells(editor: Editor, selected_cells_path: any = editor.selection.selected_cells_path) {
    if (selected_cells_path.length) {
      Renderer.save();

      const view_cell_paths: Path[] = [];
      for (let i = 0; i < selected_cells_path.length; i++) {
        const model_cell_path = editor.paraPath2ModelPath(
          selected_cells_path[i]
        );

        const cell_paths = editor.modelCellPath2viewCellPath(model_cell_path)!;

        view_cell_paths.push(...cell_paths);
      }
      for (let i = 0; i < view_cell_paths.length; i++) {
        Renderer.save();

        const view_cell_path = [...view_cell_paths[i]];

        const page = editor.pages[view_cell_path[0]];

        Renderer.translate(page.left, page.top);

        let table = page.children[view_cell_path[1]];

        if (editor.current_cell.hf_part) {
          table = editor.current_cell.children[view_cell_path[1]];
        }

        Renderer.translate(table.left, table.top);

        const cell = table.children[view_cell_path[2]];

        Renderer.draw_rectangle(
          cell.left,
          cell.top,
          cell.width,
          cell.height,
          "rgba(118, 167, 250, 0.5)"
        );

        Renderer.restore();
      }
      Renderer.restore();
    }
  }

  static drawSelectionMobileEdit(editor: Editor) {
    if (!(editor.internal.VL.is_mobile_selection && editor.isMobileTerminal())) return
    const compareResult = this.compareAnchorAndFocusIsBig(editor.selection.anchor, editor.selection.focus)
    let direction = ["right", "left"]
    if (compareResult === "anchor") {
      direction = ["left", "right"]
    }
    if (editor.selection.selected_cells_path.length) {
      const anchorXY = { x: 0, y: 0, height: 0 }
      const focusXY = { x: 0, y: 0, height: 0 }
      if (editor.selection.anchor.length === 4) {
        const anchorTable = editor.getElementByModelPath([...editor.selection.anchor], editor).table;
        if (anchorTable) {
          anchorXY.x = editor.config.page_padding_left + editor.page_left
          anchorXY.height = anchorTable.children[0].children[0].height
          const viewPath = editor.modelPath2viewPath(anchorTable.start_path)
          anchorXY.y = editor.getElementAbsolutePositionByViewPath([...viewPath]).y
          if (compareResult === "anchor") {
            anchorXY.x = editor.page_left + editor.page_size.width - editor.config.page_padding_right
            anchorXY.y = editor.getElementAbsolutePositionByViewPath([...viewPath]).y + anchorTable.height - anchorXY.height
          }
        }
      }
      if (editor.selection.anchor.length < 4) {
        //计算坐标绘制anchor方块
        const anchorRow = editor.getElementByModelPath([...editor.selection.anchor], editor).row;
        if (anchorRow && anchorRow.height) {
          const anchorViewPath = editor.modelPath2viewPath(editor.selection.anchor)
          const anchor_xy = editor.getElementAbsolutePositionByViewPath(anchorViewPath)
          anchorXY.x = anchor_xy.x
          anchorXY.y = anchor_xy.y
          anchorXY.height = anchorRow.height
        }
      }
      if (editor.selection.focus.length === 4) {
        const focusTable = editor.getElementByModelPath([...editor.selection.focus], editor).table;
        if (focusTable) {
          focusXY.x = editor.page_left + editor.page_size.width - editor.config.page_padding_right
          focusXY.height = focusTable.children[0].children[0].height
          const viewPath = editor.modelPath2viewPath(focusTable.start_path)
          focusXY.y = editor.getElementAbsolutePositionByViewPath([...viewPath]).y + focusTable.height - focusXY.height
          if (compareResult === "anchor") {
            focusXY.x = editor.config.page_padding_left + editor.page_left
            focusXY.y = editor.getElementAbsolutePositionByViewPath([...viewPath]).y
          }
        }
      }
      if (editor.selection.focus.length < 4) {
        //计算坐标绘制anchor方块
        const focusRow = editor.getElementByModelPath([...editor.selection.focus], editor).row;
        if (focusRow && focusRow.height) {
          const focusViewPath = editor.modelPath2viewPath(editor.selection.focus)
          const focus_xy = editor.getElementAbsolutePositionByViewPath(focusViewPath)
          focusXY.x = focus_xy.x
          if (isRow(focusRow) && focusRow.linebreak && this.isEndOfRow(focusRow, editor.selection.focus) && compareResult === "focus" && (editor.selection.hasLastLinebreak || !focusRow.children.length)) {
            focusXY.x = focus_xy.x + focusRow.linebreak.width
          }
          focusXY.y = focus_xy.y
          focusXY.height = focusRow.height
        }
      }
      Renderer.draw_rectangle(focusXY.x - 1, focusXY.y, 2, focusXY.height, "rgb(65, 116, 234)");
      Renderer.draw_mobile_selection(focusXY.x, focusXY.y + focusXY.height, direction[0], editor)

      Renderer.draw_rectangle(anchorXY.x - 1, anchorXY.y, 2, anchorXY.height, "rgb(65, 116, 234)");
      Renderer.draw_mobile_selection(anchorXY.x, anchorXY.y + anchorXY.height, direction[1], editor)
    } else {
      this.drawMobileEdit(direction, editor)
    }
  }
  static drawMobileEdit(direction: any, editor: Editor) {
    const focusRow = editor.getElementByModelPath([...editor.selection.focus], editor).row;
    //计算坐标绘制focus方块
    if (focusRow && focusRow.height) {
      const focusViewPath = editor.modelPath2viewPath(editor.selection.focus)
      const focusXY = editor.getElementAbsolutePositionByViewPath(focusViewPath)
      let focusLeft = focusXY.x
      if (isRow(focusRow) && focusRow.linebreak && (this.isEndOfRow(focusRow, editor.selection.focus) && editor.selection.hasLastLinebreak || !focusRow.children.length)) {
        focusLeft = focusXY.x + focusRow.linebreak.width
      }
      Renderer.draw_rectangle(focusLeft - 1, focusXY.y, 2, focusRow.height, "rgb(65, 116, 234)");
      Renderer.draw_mobile_selection(focusLeft, focusXY.y + focusRow.height, direction[0], editor)
    }
    //计算坐标绘制anchor方块
    const anchorRow = editor.getElementByModelPath([...editor.selection.anchor], editor).row;
    if (anchorRow && anchorRow.height) {
      const anchorViewPath = editor.modelPath2viewPath(editor.selection.anchor)
      const anchorXY = editor.getElementAbsolutePositionByViewPath(anchorViewPath)
      let anchorLeft = anchorXY.x
      Renderer.draw_rectangle(anchorLeft - 1, anchorXY.y, 2, anchorRow.height, "rgb(65, 116, 234)");
      Renderer.draw_mobile_selection(anchorLeft, anchorXY.y + anchorRow.height, direction[1], editor)
    }
  }
  //通过路径判断是否点在段落结尾
  static isEndOfRow(row: Row, path: Path) {
    if (path[path.length - 1] === row.children.length) {
      return true
    }
    return false
  }

  // 绘制可复制标识符 完全抄的下边方法的逻辑 只不过进行了一点点的删减
  static drawReplicableIdentifiers(editor: Editor, areas: any) {
    if (!areas?.[0]?.start_para_path?.length) return
    if (!PathUtils.paraPathisValid(editor, areas[0].start_para_path)) return
    const { x, y } = this.getAbsoluteXYByParaPath(areas[0].start_para_path, editor);
    editor.internal.position = [x + 3, y + 1]
    Renderer.draw_rectangle(x + 3, y + 1, 3, 3, "rgba(30, 112, 250, 0.4)");
  }

  static drawSelectionAreas(
    editor: Editor,
    selectedAreas: any = editor.selection.selected_areas,
    color: string = "rgba(118, 167, 250, 0.5)"
  ) {
    const areas = selectedAreas;
    for (let j = 0; j < areas.length; j++) {
      const startPath: Path = editor.paraPath2ModelPath(
        areas[j].start_para_path as Path
      );

      const endPath: number[] = editor.paraPath2ModelPath(
        areas[j].end_para_path as Path
      );

      const last_linebreak = areas[j].last_linebreak;

      const start_index = startPath[startPath.length - 2];

      const end_index = endPath[endPath.length - 2];
      // 遍历行进行设置
      for (let i = start_index; i <= end_index; i++) {
        Renderer.save();

        const prefix_path = PathUtils.isTablePath(startPath)
          ? startPath.slice(0, 2)
          : [];
        // 此处只需获取到行
        const model_path = prefix_path.concat([i, 0]);

        // 将开始坐标与结束坐标转换为渲染模型坐标
        const view_path = editor.modelPath2viewPath(model_path);

        const page: any = editor.editFloatModelMode
          ? editor.floatModels[view_path[0]]
          : editor.pages[view_path[0]];

        // 单元格内特殊处理
        let table: Table | undefined, cell: Cell | undefined, row: Row;

        if (editor.current_cell.hf_part) {
          if (PathUtils.isTablePath(startPath)) {
            table = editor.current_cell.children[view_path[1]] as Table;
            cell = table.children[view_path[2]];
            row = cell.children[view_path[3]] as Row;
          } else {
            row = page[editor.current_cell.hf_part].children[
              view_path[1]
            ] as Row;
          }
        } else {
          if (PathUtils.isTablePath(startPath)) {
            table = page.children[view_path[1]] as Table;
            cell = table.children[view_path[2]];
            row = cell.children[view_path[3]] as Row;
          } else {
            row = page.children[view_path[1]] as Row;
          }
        }

        editor.editFloatModelMode
          ? Renderer.translate(
            editor.currentFloatModel?.originPosition[0] || 0,
            editor.currentFloatModel?.originPosition[1] || 0
          )
          : Renderer.translate(page.left, page.top);

        if (isRow(row) && row.linebreak && !row.children.length) {
          let left = row.left;

          let top = row.top;

          let right = row.right;

          const height = row.height;
          // 单元格内特殊处理
          if (table && cell) {
            left = table.left + cell.left + left;

            top = table.top + cell.top + top;

            right = table.left + cell.left + right;

            if (cell.set_cell_height.type === "scroll") {
              top -= cell.scroll_cell_top;
              const cell_top = table.top + cell.top;
              Renderer.get().rect(left, cell_top, cell.width, cell.height);
              Renderer.get().clip();
            }
          }
          // if (editor.internal.VL.is_mobile_selection && editor.isMobileTerminal()) {
          //   if (i === start_index && j === 0) {
          //     Renderer.draw_rectangle(left, top, 2, row.height, "blue");
          //     Renderer.draw_mobile_selection(left, top + height, "left", editor)
          //   } else if (i === end_index && j === areas.length - 1) {
          //     Renderer.draw_rectangle(left + row.linebreak.right - 1, top, 2, row.height, "blue");
          //     Renderer.draw_mobile_selection(left + row.linebreak.right, top + height, "right", editor)
          //   }
          // }
          Renderer.draw_rectangle(
            left,
            top,
            row.linebreak.right,
            height,
            color
          );
          Renderer.restore();

          continue;
        }

        let left: number = row.left;

        const height: number = row.height;

        let top: number = row.top;

        let right: number = row.children.length
          ? row.children[row.children.length - 1].right
          : row.width;
        // 如果是开始行
        if (i === start_index) {
          // 如果是光标定位到行尾
          if (PathUtils.isEndPathInRow(startPath, row as Row)) {
            left = right + left;
          } else {
            const start_char_index = startPath[startPath.length - 1];
            left = left + row.children[start_char_index].left;
          }
          // if (editor.internal.VL.is_mobile_selection && editor.isMobileTerminal() && j === 0) {
          //   let drawLeft = left
          //   let drawTop = top
          //   if (table && cell) {
          //     drawLeft = table.left + cell.left + left;
          //     drawTop = table.top + cell.top + top;
          //     if (cell.set_cell_height.type === "scroll") {
          //       drawTop -= cell.scroll_cell_top;
          //     }
          //   }
          //   Renderer.draw_rectangle(drawLeft - 1, drawTop, 2, height, "blue");
          //   Renderer.draw_mobile_selection(drawLeft, drawTop + height, "left", editor)
          // }
        } else {
          // 非第一行进行特殊处理，主要用于列表类型选区
          left = left + row.padding_left;
        }
        // 最后一行时
        if (i === end_index) {
          if (PathUtils.isStartPathInRow(endPath)) {
            right = 0;
          } else if (PathUtils.isEndPathInRow(endPath, row as Row)) {
            right = row.children[row.children.length - 1].right;
          } else {
            const end_char_index = endPath[endPath.length - 1];
            right = row.children[end_char_index].left;
          }
          // 如果最后一块区域最后一行选中行包含换行符标记，则进行绘制
          // (如果选区在下一段的开头位置，此时也包含换行符标记，但是此时不能进入以下判断)
          if (
            last_linebreak &&
            isRow(row) &&
            row.linebreak &&
            !PathUtils.isStartPathInRow(endPath)
          ) {
            right += row.linebreak.width;
          }
          // if (editor.internal.VL.is_mobile_selection && editor.isMobileTerminal() && j === areas.length - 1) {
          //   let drawLeft = left
          //   let drawTop = top
          //   let drawRight = right
          //   if (table && cell) {
          //     drawLeft = table.left + cell.left + left;
          //     drawTop = table.top + cell.top + top;
          //     drawRight = table.left + cell.left + right;
          //     if (cell.set_cell_height.type === "scroll") {
          //       drawTop -= cell.scroll_cell_top;
          //     }
          //   }
          //   const width: number = (drawRight === drawLeft ? 0 : drawRight - drawLeft) + row.left;
          //   Renderer.draw_rectangle(drawLeft + width - 1, drawTop, 2, height, "blue");
          //   Renderer.draw_mobile_selection(drawLeft + width, drawTop + height, "right", editor)
          // }
        } else {
          // 如果存在换行符 此换行符只有在本行全选或光标选至该行最后时使用
          if (isRow(row) && row.linebreak) {
            right = right + row.linebreak.width;
          }
        }

        // 单元格内特殊处理
        if (table && cell) {
          left = table.left + cell.left + left;

          top = table.top + cell.top + top;

          right = table.left + cell.left + right;

          if (cell.set_cell_height.type === "scroll") {
            top -= cell.scroll_cell_top;
            const cell_top = table.top + cell.top;
            Renderer.get().rect(left, cell_top, cell.width, cell.height);
            Renderer.get().clip();
            // console.log(top + height);
            // if (top < cell_top && top + height > cell_top) {
            //   Renderer.get().rect(left, cell_top, cell.width, top + height - cell_top);
            //   Renderer.get().clip();
            // } else if (top < cell_top + cell.height && top + height > cell_top + cell.height) {
            //   Renderer.get().rect(left, top, cell.width, cell_top + cell.height - top);
            //   Renderer.get().clip();
            // } else if (top + height < cell_top || top > cell_top + cell.height + height) {
            //   console.log(11111111);
            //   Renderer.restore();
            //   continue;
            // } ;
          }
        }
        const width: number = (right === left ? 0 : right - left) + row.left;
        Renderer.draw_rectangle(left, top, width, height, color);

        Renderer.restore();
      }
    }
  }

  static replaceContainerByRawData(map: Map<ReplaceContainerType, any>) {
    for (const [key, value] of map.entries()) {
      key.replaceWith(value);
    }
  }

  static setViewMode(editor: Editor, view_mode: ViewMode) {
    if (editor.view_mode === view_mode) return;
    // 必须得刷新 否则 editor 上的 formReadonly 就没法更正了 所以不能简单的优化 pacs 更新完了之后就有这个问题 updateFieldText 不生效了 因为 formReadonly 为 true l

    // // 简洁模式 和 表单模式 切换的时候是不需要进行刷新的
    // if (editor.view_mode === ViewMode.NORMAL && view_mode === ViewMode.FORM) {
    //   editor.view_mode = view_mode;
    //   return;
    // }
    // if (editor.view_mode === ViewMode.FORM && view_mode === ViewMode.NORMAL) {
    //   editor.view_mode = view_mode;
    //   return;
    // }

    editor.view_mode = view_mode;

    // 不能影响表单模式，现在的表单模式是正常的
    if (view_mode === ViewMode.VIEW) {
      editor.showFieldSymbol(false);
    } else {
      editor.showFieldSymbol(true);
    }
  }

  static wordStatistics(editor: Editor) {
    // 文档中 几行 几列
    const model_path = editor.selection.focus;
    const statistic_result: any = {};
    statistic_result.row_num = model_path[model_path.length - 2] + 1;
    statistic_result.col_num = model_path[model_path.length - 1] + 1;
    // 总页数 第几页
    const view_path = editor.modelPath2viewPath(model_path);
    statistic_result.page_all_num = editor.pages.length;
    statistic_result.page_num = view_path[0] + 1;
    // 页面中
    statistic_result.page_row_num = view_path[view_path.length - 2] + 1;
    statistic_result.page_col_num = view_path[view_path.length - 1] + 1;
    // 总字数 含空格
    let document_str: any = "";
    for (const item of editor.root_cell.paragraph) {
      document_str += item.getStr();
    }
    // document_str.replaceAll("\n", "");// 存在兼容性问题
    document_str = document_str.replace(/\n/g, "");
    statistic_result.word_nums = document_str.length;
    // 字数 去掉空格
    const document_without_space_str = document_str.replace(/\b/g, "");
    statistic_result.word_without_space_nums =
      document_without_space_str.length;
    // 段落数
    statistic_result.paragraph_num = editor.root_cell.paragraph.length;
    // 图片数
    // statistic_result.img_num = 0;
    return statistic_result;
  }

  // TODO 是不是就根据 rawData 来进行插入数据，rawData中是一整段的就按照 rawData 中整段的样式来插入，不是整段的，在段落样式中就按照插入位置段落的样式来
  // TODO 也就是说 rawData2ModelData 根据 rawData 来进行数据转换 rawData 中有的就有换行符，否则就没有，同时也不需要 hasLastLineBreak 了
  static insertTemplateData(
    editor: Editor,
    rawData: any,
    replaceHeader: boolean = false,
    replaceFooter: boolean = false,
    configItemUsedInRawData: any = [],
    option: {waterMarkUseNew?: boolean, waterMarkMerge?: boolean} = {}
  ) {
    rawData = useRawDataByConfig(rawData);
    let isReplace = false
    const newWaterMarks = editor.getWaterMarkModelData(rawData.waterMarks);
    if (option.waterMarkMerge) {
      editor.waterMarks.push(...newWaterMarks);
    } else if (option.waterMarkUseNew) {
      editor.waterMarks = newWaterMarks;
    }

    if (configItemUsedInRawData.length) {
      editor.config.handleConfig(editor, rawData, configItemUsedInRawData, true)
      // options.forEach((option:any)=>{
      //   if(option==="show_header_line"){
      //     editor.config.show_header_line=!!rawData.config.header_horizontal
      //     editor.raw.config.footer_horizontal=true
      //   }else if(option==="show_footer_line"){
      //     editor.config.show_footer_line=!!rawData.config.footer_horizontal
      //   }else if(option==="isReplace"){
      //     isReplace=true
      //   }else{
      //     if (Object.prototype.hasOwnProperty.call(editor.config, option)) {
      //       (editor.config as any)[option] = rawData.config[option];
      //     }
      //   }
      // })
    }
    const insertTemplateUseDefaultSetting =
      editor.config.insertTemplateUseDefaultSetting;

    if (
      insertTemplateUseDefaultSetting &&
      insertTemplateUseDefaultSetting.rowRatio
    ) {
      rawData.content.forEach((p: any) => {
        p.row_ratio = editor.config.row_ratio;
        if (p.type === "table") {
          const cells = p.cells;
          cells.forEach((cell: any) => {
            const children = cell.children;
            children.forEach((para: any) => {
              para.row_ratio = editor.config.row_ratio;
            });
          });
        }
      });
    }
    const root_cell: Cell = new Cell(
      editor,
      root_node.pos,
      root_node.colspan,
      root_node.rowspan,
      null,
      uuid("trans")
    );
    this.mergeTemplateFontMap(rawData.fontMap, editor);
    editor.internal.imageSrcObj = rawData.imageSrcObj ?? {};
    const cell = editor.getCellByRawNodeList(rawData.content, root_cell);
    if (!cell) return;
    const sel: XSelection = editor.selection;
    if (!sel.isCollapsed) {
      // 要在这里删除掉 否则 当段落内只有一个文本域 并且三击选中后 插入模板这里不删除就会报错 表单模式就会插入到文本域外边去 数据是乱的
      sel.editor.delete_backward();
    }
    const para_path = sel.para_start;
    const field = sel.getFocusField();
    const paragraphs = handleCopyData(
      root_cell,
      field,
      para_path,
      editor,
      true
    ); // 处理后复制出来的数据
    insertParagraphsHelper(
      sel,
      para_path,
      field,
      paragraphs,
      !isReplace,
      !isReplace,
      false,
      isReplace
    );
    if (replaceHeader) {
      editor.pages = [];
      const header_cell: Cell = new Cell(
        editor,
        root_node.pos,
        root_node.colspan,
        root_node.rowspan,
        null,
        editor.header_cell.id
      );
      header_cell.hf_part = editor.header_cell.hf_part;
      const headerCell = editor.getCellByRawNodeList(
        rawData.header,
        header_cell
      );
      if (!headerCell) return;
      editor.header_cell = headerCell;
    }
    if (replaceFooter) {
      editor.pages = [];
      const footer_cell: Cell = new Cell(
        editor,
        root_node.pos,
        root_node.colspan,
        root_node.rowspan,
        null,
        editor.footer_cell.id
      );
      footer_cell.hf_part = editor.footer_cell.hf_part;
      const footerCell = editor.getCellByRawNodeList(
        rawData.footer,
        footer_cell
      );
      if (!footerCell) return;
      editor.footer_cell = footerCell;
    }
    // 如果模板方向为横向，且当前方向不为横向 ，则自动将页面设置为横向
    if (rawData.config && rawData.config.direction === "horizontal") {
      editor.changePageDirection("horizontal");
    } else {
      editor.refreshDocument();
    }
    return true;
  }

  static insertHeaderOrFooterTemplateData(editor: Editor, rawData: any) {
    rawData = useRawDataByConfig(rawData);
    this.mergeTemplateFontMap(rawData.fontMap, editor);
    editor.internal.imageSrcObj = rawData.imageSrcObj ?? {};
    editor.pages = [];
    const header_cell: Cell = new Cell(
      editor,
      root_node.pos,
      root_node.colspan,
      root_node.rowspan,
      null,
      editor.header_cell.id
    );
    header_cell.hf_part = editor.header_cell.hf_part;
    const headerCell = editor.getCellByRawNodeList(rawData.header, header_cell);
    if (!headerCell) return;
    editor.header_cell = headerCell;

    editor.pages = [];
    const footer_cell: Cell = new Cell(
      editor,
      root_node.pos,
      root_node.colspan,
      root_node.rowspan,
      null,
      editor.footer_cell.id
    );
    footer_cell.hf_part = editor.footer_cell.hf_part;
    const footerCell = editor.getCellByRawNodeList(rawData.footer, footer_cell);
    if (!footerCell) return;

    // 如果模板方向为横向，且当前方向不为横向 ，则自动将页面设置为横向
    if (rawData.config && rawData.config.direction === "horizontal") {
      editor.changePageDirection("horizontal");
    } else {
      editor.refreshDocument();
    }
    return true;
  }

  // static dcXmlDataTrans (editor: Editor, xml: any) {
  //   console.time("数据转换耗时：");
  //   // 如果传入的xml字符串不为空
  //   if (xml) {
  //     let rawData = dcXmlHandle.dcXmlDataTrans(xml, editor);
  //     // 先将rawData转成字符串，判断其中的换行镖旗
  //     rawData = dcXmlHandle.handleTooManyLinebreak(rawData);
  //     editor.reInitRaw(rawData);
  //     editor.refreshDocument(true);
  //     // 将文本域中的特定字符替换成换行符
  //     editor.replaceAllFont(dcXmlHandle.linebreak_mark, "\n");
  //     if (dcXmlHandle.isExceptionMark === 1) {
  //       console.log("异常：有表格在文本域内，需特殊处理");
  //     }
  //     if (dcXmlHandle.isExceptionMark === 2) {
  //       console.log("异常：有表格嵌套，需特殊处理");
  //     }
  //   } else {
  //     editor.reInitRaw(editor.config.rawData);
  //     editor.refreshDocument(true);
  //   }
  //   // 内容校验
  //   // const dcBodyText = dcXmlHandle.dcBodyText.replace(/\n/g, "").replace(/\s/g, "").replace(/○/g, "");
  //   // const bodyText = editor.getBodyText().replace(/\n/g, "").replace(/\s/g, "");
  //   // if (dcBodyText.length !== bodyText.length) {
  //   // }
  //   dcXmlHandle.afterInitRawHandle();
  //   console.timeEnd("数据转换耗时：");
  //   return dcXmlHandle;
  // }

  static permitOperationValidation(editor: Editor): boolean {
    // 文本域边框不显示的情况下不允许编辑，否则会出错
    if ((editor.readonly || editor.formReadonly) && !editor.adminMode) {
      return false;
    }
    return true;
  }

  static clearDocument(editor: Editor, isForce: boolean = false) {
    if (isForce) {
      editor.reInitRaw(editor.config.rawData);
      editor.update();
      editor.render();
    } else {
      const para = new Paragraph(uuid("para"), editor.root_cell, null);
      const character = new Character(
        editor.fontMap.add(editor.config.default_font_style),
        "\n"
      );
      para.characters = [character];
      editor.root_cell.paragraph = [para];
      editor.root_cell.groups = [];
      editor.refreshDocument(true);
    }
  }

  static unlockEverything({ editor }: {editor: Editor}) {
    editor.view_mode = ViewMode.NORMAL;
    const fields = editor.getAllFields();
    const cells = editor.getAllCells();
    const groups = editor.getAllGroup();
    for (let i = 0; i < groups.length; i++) {
      const group = groups[i];
      group.lock = false;
      group.is_form = false;
    }
    for (let i = 0; i < cells.length; i++) {
      const cell = cells[i];
      cell.lock = false;
    }
    for (let i = 0; i < fields.length; i++) {
      const field = fields[i];
      field.readonly = 0;
      field.deletable = 1;
    }
  }

  static clearHeaderOrFooter(editor: Editor, type: string = "all") {
    const rawData = editor.getRawData();
    if (type === "header") {
      rawData.header = editor.config.rawData.header;
    } else if (type === "footer") {
      rawData.footer = editor.config.rawData.footer;
    } else if (type === "all") {
      rawData.header = editor.config.rawData.header;
      rawData.footer = editor.config.rawData.footer;
    }
    editor.reInitRaw(rawData);
    editor.update();
    editor.render();
  }

  static printCpp({ editor }: {editor: Editor}) {
    const jsonData = editor.assemblePageJson();
    const pom = document.createElement("a");
    pom.setAttribute(
      "href",
      "data:text/plain;charset=utf-8," +
      encodeURIComponent(JSON.stringify(jsonData))
    );
    pom.setAttribute("download", "testJSON.json");
    if (document.createEvent) {
      const event = document.createEvent("MouseEvents");
      event.initEvent("click", true, true);
      pom.dispatchEvent(event);
    } else {
      pom.click();
    }
  }

  //对比anchor和focus谁在前面
  static compareAnchorAndFocusIsBig(anchor: Path, focus: Path) {
    for (let i = 0; i < anchor.length; i++) {
      const num = anchor[i];
      if (num > focus[i]) {
        return "anchor"
      } else if (num < focus[i]) {
        return "focus"
      }
    }
  }

  static headerFooterHorizontal({ editor, header_show, footer_show }: {editor: Editor, header_show: boolean, footer_show: boolean}) {
    editor.config.show_header_line = header_show;
    editor.config.show_footer_line = footer_show;
    // 解决页脚横线设置后编辑内容后会还原成raw.config的设置
    editor.raw.config.footer_horizontal = footer_show;
    editor.render();
  }

  static saveInfo(editor: Editor, obj: any = {}) {
    editor.userLogin(editor.user);
    const { isGetAllDocumentMeta } = obj;
    let document_meta: any = {};
    if (isGetAllDocumentMeta) {
      document_meta = editor.document_meta;
    } else {
      for (const key in editor.document_meta) {
        if (key !== "traceInfo") {
          document_meta[key] = editor.document_meta[key];
        } else {
          document_meta[key] = [];
        }
      }
    }

    const save_other_info = {
      page_direction: editor.config.page_direction,
      document_meta,
      custom_meta: editor.custom_meta,
      header_horizontal: editor.config.show_header_line,
      footer_horizontal: editor.config.show_footer_line,
      rowLineType: editor.config.rowLineType,
      startPageNumber: editor.config.startPageNumber,
      shapes: editor.shapes,
      waterMarks: editor.waterMarks,
      page_info: {
        default_font_style: editor.config.default_font_style,
        page_size_type: editor.config.page_size_type,
        page_direction: editor.config.page_direction,
        row_ratio: editor.config.row_ratio,
        editor_padding_top: editor.config.editor_padding_top,
        page_margin_bottom: editor.config.page_margin_bottom,
        page_padding_left: editor.config.page_padding_left,
        page_padding_top: editor.config.page_padding_top,
        page_padding_right: editor.config.page_padding_right,
        page_padding_bottom: editor.config.page_padding_bottom,
        header_margin_top: editor.config.header_margin_top,
        footer_margin_bottom: editor.config.footer_margin_bottom,
        content_margin_header: editor.config.content_margin_header,
        content_margin_footer: editor.config.content_margin_footer,
        table_padding_horizontal: editor.config.table_padding_horizontal,
        page_size: editor.config.getPageSize(),
        contentBorder: editor.config.contentBorder,
      },
    };
    return save_other_info;
  }

  static checkSensitiveWord(
    editor: Editor,
    words: string[],
    style?: { bgColor?: string; color?: string }
  ) {
    // 先清空之前选中内容
    editor.clearSensitiveWordStyle();
    const handle_style: any = {};
    if (style) {
      // 设置临时样式
      style.bgColor && (handle_style.temp_word_bgColor = style.bgColor);
      style.color && (handle_style.temp_word_color = style.color);
    }
    const match_words: any[] = [];
    words.forEach((e) => {
      Search.searchGlobalText(
        e,
        "mark",
        editor.root_cell.paragraph,
        handle_style,
        match_words
      );
    });
    editor.render();
    return match_words;
  }

  static clearSensitiveWordStyle(editor: Editor) {
    const fontMap = editor.fontMap.get();
    for (const item of fontMap.entries()) {
      const v = item[1];
      if (v.temp_word_bgColor) {
        v.temp_word_bgColor = "";
      }
      if (v.temp_word_color) {
        v.temp_word_color = "";
      }
    }
    editor.render();
  }

  static mergeTemplateFontMap(fontMapOrField: any | XField, editor: Editor) {
    if (fontMapOrField) {
      if (isField(fontMapOrField)) {
        if (
          editor.config.insertTemplateUseDefaultSetting &&
          editor.config.insertTemplateUseDefaultSetting.family
        ) {
          fontMapOrField.style.family = editor.config.default_font_style.family;
        }
        if (
          editor.config.insertTemplateUseDefaultSetting &&
          editor.config.insertTemplateUseDefaultSetting.size
        ) {
          fontMapOrField.style.height = editor.config.default_font_style.height;
        }
      } else {
        for (const id in fontMapOrField) {
          const style = fontMapOrField[id];
          if (style) {
            if (
              editor.config.insertTemplateUseDefaultSetting &&
              editor.config.insertTemplateUseDefaultSetting.family
            ) {
              style.family = editor.config.default_font_style.family;
            }
            if (
              editor.config.insertTemplateUseDefaultSetting &&
              editor.config.insertTemplateUseDefaultSetting.size
            ) {
              style.height = editor.config.default_font_style.height;
            }

            editor.fontMap.add(style, id);
          }
        }
      }
    }
  }

  static recordVersionInfo(editor: Editor) {
    const version = editor.config.version;
    if (!editor.document_meta.versionList) {
      editor.document_meta.versionList = [];
      editor.document_meta.versionList.push({
        version,
        time: Date.now(),
      });
    } else {
      const lastVersion = editor.document_meta.versionList.pop();
      if (!lastVersion) {
        editor.document_meta.versionList.push({
          version,
          time: Date.now(),
        });
      } else {
        if (lastVersion.version === version) {
          editor.document_meta.versionList.push(lastVersion);
        } else {
          editor.document_meta.versionList.push(lastVersion, {
            version,
            time: Date.now(),
          });
        }
      }
    }
  }

  /**
   * 注意：该方法只在更新光标时调用一次，每次设置前清空焦点元素
   * @param editor
   * @param args
   */
  static setFocusElement(editor: Editor, ...args: any) {
    // 该方法一定注意不要多次调用
    editor.focusElement = {};
    for (let i = 0, len = args.length; i < len; i++) {
      const ele = args[i];
      if (isField(ele)) {
        editor.focusElement.field = ele;
      } else if (isRow(ele)) {
        editor.focusElement.row = ele;
        editor.focusElement.paragraph = ele.paragraph;
        let group_id = ele.paragraph.group_id;
        if (ele.parent.parent) {
          editor.focusElement.cell = ele.parent;
          editor.focusElement.table = ele.parent.parent;
          group_id = ele.parent.parent.group_id;
        }
        let group;
        if (group_id) {
          group = editor.selection.getGroupByGroupId(group_id);
        }
        if (group) {
          editor.focusElement.group = group;
        }
      } else if (isPage(ele)) {
        editor.focusElement.page = ele;
      }
    }
  }

  static setFieldsAsterisk({ editor, names, rule }: {editor: Editor, names?: string[], rule?: any}) {
    editor.config.fieldAsteriskNames = names || [];
    editor.config.fieldAsteriskRule = rule || {}
    editor.refreshDocument();
  }

  static judgeFocusEleEditable(editor: Editor) {
    if (
      editor.readonly ||
      editor.focusElement.field?.isReadonly ||
      editor.focusElement.cell?.lock ||
      editor.focusElement.group?.lock
    ) {
      return false;
    }
    return true;
  }

  static clearVersionInfo({ editor }: {editor: Editor}) {
    if (editor.document_meta.versionList) {
      editor.document_meta.versionList.length = 0;
    }
  }

  static sortAllFields({ editor }: {editor: Editor}) {
    const fields = editor.getAllFields(editor.current_cell);
    fields.sort(XField.sortFields);
    return fields;
  }

  static compareRawData(rawData1: any, rawData2: any, ignoreProps?: string[]) {
    if (typeof rawData1 !== "object") {
      rawData1 = JSON.parse(rawData1);
    }
    if (typeof rawData2 !== "object") {
      rawData2 = JSON.parse(rawData2);
    }
    const handleObjKey = function (obj: any) {
      let index = 0;
      for (const key in obj) {
        obj[index++] = obj[key];
        delete obj[key];
      }
    };
    // 还要处理两个josn的fontMap与imageSrcObj, 否则其中的key即为id,以下方法不适用
    handleObjKey(rawData1.fontMap);
    handleObjKey(rawData2.fontMap);
    handleObjKey(rawData1.imageSrcObj);
    handleObjKey(rawData2.imageSrcObj);
    const needIgnoreProps: string[] = [
      "id",
      "font_id",
      "src",
      "col_size",
      "row_size",
    ];
    if (ignoreProps instanceof Array) {
      needIgnoreProps.push(...ignoreProps);
    }
    return compareJsonIgnoreKey(rawData1, rawData2, needIgnoreProps);
  }

  static operableOrNot(editor: Editor, types: string[]) {
    if (!editor.adminMode) {
      for (let i = 0; i < types.length; i++) {
        const type = types[i];
        if (type === "cell" && editor.selection.getFocusCell()?.lock) {
          return false;
        } else if (type === "group" && editor.selection.getFocusGroup()?.lock) {
          return false;
        } else if (type === "field") {
          const focusField = editor.selection.getFocusField();
          if (focusField && focusField.isReadonly) {
            // 如果 焦点文本域是只读但是整个文本域都在选区范围内，则也可以操作
            if (
              !editor.selection.isCollapsed &&
              focusField.isCompleteField(
                editor.selection.selected_fields_chars.field_chars
              )
            ) {
              return true;
            }
            return false;
          }
        } else if (type === "editor" && editor.readonly) {
          return false;
        }
      }
    }
    return true;
  }

  static getAllImages(editor: Editor, assign_cell: Cell) {
    const imageList: any = [];
    const paragraphs: any = [];
    const allCells = editor.getAllCells(assign_cell);
    for (let i = 0; i < allCells.length; i++) {
      const cell = allCells[i];
      const paragraph = cell.paragraph;
      paragraph.forEach((para: any) => {
        if (isParagraph(para)) {
          paragraphs.push(para);
        }
      });
    }
    for (let i = 0; i < paragraphs.length; i++) {
      const paragraph = paragraphs[i];
      const imageMap: any = {};
      imageMap.paragraph = paragraph;
      imageMap.image = [];
      const characters = paragraph.characters;
      characters.forEach((char: any) => {
        if (isImage(char)) {
          imageMap.image.push(char);
        }
      });
      if (imageMap.image.length) {
        imageList.push(imageMap);
      }
    }
    return imageList;
  }

  static removeParagraphElement(
    paragraph: Paragraph,
    elements: Widget[] | Image[] | Character[] | Line[]
  ) {
    const editor = paragraph.cell.editor;
    for (let i = 0; i < elements.length; i++) {
      const element = elements[i];
      if (element.field_id) {
        const field_id = element.field_id;
        const field = editor.getFieldById(field_id);
        field?.removeElement(element);
        field?.reShowPlaceholder();
      }
      paragraph.removeElement(element);
      paragraph.updateChildren();
    }
    editor.update();
    editor.render();
  }

  static getPositionRelativeToPageLeftBottom(editor: Editor, text: string) {
    const res: {
      leftBottomX: number;
      leftBottomY: number;
      pageNumber: number;
    }[] = [];

    forEachFlatParagraph(editor.current_cell, (paragraph, cell, table) => {
      if (cell || table) return;
      const rows = paragraph.children;
      const positionArr = Search.getTextLocation(text, paragraph);
      for (const { start_index } of positionArr) {
        const end_index = start_index + text.length; // 用 positionArr 里边返回的 end_index 不对(最后一个空文本域的时候获取到的 end_index 值就不对),所以在这里加上
        const lastCharacter = paragraph.characters[end_index - 1]; // 因为直接取 end_index 就是取的紧挨着 text 的后边一个字符,如果下一个字符是换行符的话 row.children 里边不包括换行符,就找不到了
        if (!lastCharacter) continue;
        for (const row of rows) {
          if (row.children.find((character) => character === lastCharacter)) {
            const bottom = row.bottom;
            const left = row.left + lastCharacter.left;
            res.push({
              leftBottomX:
                (left + lastCharacter.width) * PIXEL_CONVERSION_FORMULA_RATIO,
              leftBottomY:
                (editor.pages[0].height - bottom) *
                PIXEL_CONVERSION_FORMULA_RATIO,
              pageNumber: row.page_number,
            });
          }
        }
      }
    });
    for (const page of editor.pages) {
      for (const table of page.children) {
        if (isTable(table)) {
          for (const cell of table.children) {
            for (const paragraph of cell.paragraph) {
              if (isParagraph(paragraph)) {
                const rows = paragraph.children;
                const positionArr = Search.getTextLocation(text, paragraph);
                for (const { start_index } of positionArr) {
                  const end_index = start_index + text.length; // 用 positionArr 里边返回的 end_index 不对(最后一个空文本域的时候获取到的 end_index 值就不对),所以在这里加上
                  const lastCharacter = paragraph.characters[end_index - 1]; // 因为直接取 end_index 就是取的紧挨着 text 的后边一个字符,如果下一个字符是换行符的话 row.children 里边不包括换行符,就找不到了
                  if (!lastCharacter) continue;
                  for (const row of rows) {
                    if (
                      row.children.find(
                        (character) => character === lastCharacter
                      )
                    ) {
                      const bottom = table.top + cell.top + row.bottom;
                      const left =
                        table.left + cell.left + row.left + lastCharacter.left;
                      res.push({
                        leftBottomX:
                          (left + lastCharacter.width) *
                          PIXEL_CONVERSION_FORMULA_RATIO,
                        leftBottomY:
                          (editor.pages[0].height - bottom) *
                          PIXEL_CONVERSION_FORMULA_RATIO,
                        pageNumber: table.page_number,
                      });
                    }
                  }
                }
              }
            }
          }
        }
      }
    }
    return res;
  }

  static newVersionTest(
    editor: Editor,
    rowNum: number,
    colNum: number
  ) {
    console.log("新版本测试")
    if (rowNum <= 0 || colNum <= 0) {
      editor.event.emit("message", "不能拆分成 0 行或者 0 列");
      return
    }
    const focusCell = editor.selection.getFocusCell()?.getOrigin();
    if (!focusCell) {
      editor.event.emit("message", "光标没在单元格内，不能拆分");
      return;
    }

    const startRowIndex = focusCell.start_row_index;
    const startColIndex = focusCell.start_col_index;
    const endRowIndex = focusCell.end_row_index;
    const endColIndex = focusCell.end_col_index;
    const table = focusCell.parent?.getOrigin();
    if (!table) return;

    // 处理 新单元格的 position colspan 和 rowspan ↓
    const newCells: Cell[] = [];

    const retainColspan = Math.max(focusCell.colspan - colNum + 1, 1);
    const retainRowspan = Math.max(focusCell.rowspan - rowNum + 1, 1);

    const initRowIndex = startRowIndex + (retainRowspan - 1);
    const initColIndex = startColIndex + (retainColspan - 1);
    for (let i = 0; i < rowNum; i++) {
      for (let j = 0; j < colNum; j++) {
        const isOriginCell = i === 0 && j === 0;
        const rowIndex = i === 0 ? startRowIndex : initRowIndex + i;
        const colIndex = j === 0 ? startColIndex : initColIndex + j;
        const cell = new Cell(editor, [rowIndex, colIndex], 1, 1, table);
        if (isOriginCell) {
          cell.position = [...focusCell.position];
          cell.rowspan = retainRowspan;
          cell.colspan = retainColspan;
          cell.paragraph = focusCell.paragraph;
          cell.paragraph.forEach((para) => {
            if (isParagraph(para)) {
              para.cell = cell;
            }
          });
          cell.handleFieldsAssignment(focusCell.fields);
        }

        if (i === 0) {
          cell.rowspan = retainRowspan;
        }
        if (j === 0) {
          cell.colspan = retainColspan;
        }

        !isOriginCell && cell.insertEmptyParagraph();
        newCells.push(cell);
      }
    }
    // 处理 新单元格的 position colspan 和 rowspan ↑


    const opacityRow = table.notAllowDrawLine.changeOpacityRow.find(position => position[0] === startRowIndex && position[1] === startColIndex)
    const opacityCol = table.notAllowDrawLine.changeOpacityCol.find(position => position[0] === startColIndex && position[1] === startRowIndex)

    if (focusCell.colspan >= colNum && focusCell.rowspan >= rowNum) {
      // 这是要拆分的单元格原来的行和列都比即将拆分的行和列大的情况
      // 这种情况下 表格的 cols_size 和 row_size 都包含了 所以 col_size 和 row_size 是不需要改变的 其他的线也是不需要改变的 只是修改当前单元格的线就可以了
      // 其他单元格的任何位置也都不需要改变
      for (let i = 0; i < table.children.length; i++) {
        if (table.children[i] === focusCell) {
          table.children.splice(i, 1);
          break;
        }
      }
      // 处理线 ↓ 跟下边代码大量重复 先实现功能 到时候再捋逻辑 整理代码
      // 我应该先处理一遍线 将原来的线的位置全部都挪一下
      A: for (let i = 0; i < table.notAllowDrawLine.row.length; i++) {
        const position = table.notAllowDrawLine.row[i];
        // 新增了几行几列 原来是不显示的线 现在有的需要显示了 所以要删除掉
        for (let j = 0; j < newCells.length; j++) {
          const cell = newCells[j];
          // 这里每个单元格的每条线都应该显示出来
          for (let m = 0; m < cell.colspan; m++) {
            // 处理上边线
            opacityRow && table.notAllowDrawLine.changeOpacityRow.push([cell.start_row_index, cell.start_col_index + m]);
            opacityRow && table.notAllowDrawLine.changeOpacityRow.push([cell.end_row_index + 1, cell.start_col_index + m]);
            if (position[0] === cell.start_row_index && position[1] === cell.start_col_index + m) {
              table.notAllowDrawLine.row.splice(i, 1);
              i--;
              continue A;
            }

            // 处理下边线
            if (position[0] === cell.end_row_index + 1 && position[1] === cell.start_col_index + m) {
              table.notAllowDrawLine.row.splice(i, 1);
              i--;
              continue A;
            }
          }
        }
      }
      B: for (let i = 0; i < table.notAllowDrawLine.col.length; i++) {
        const position = table.notAllowDrawLine.col[i];
        for (let j = 0; j < newCells.length; j++) {
          const cell = newCells[j];
          // 这里每个单元格的每条线都应该显示出来
          for (let m = 0; m < cell.rowspan; m++) {
            opacityCol && table.notAllowDrawLine.changeOpacityCol.push([cell.end_col_index + 1, cell.start_row_index + m]);
            opacityCol && table.notAllowDrawLine.changeOpacityCol.push([cell.start_col_index, cell.start_row_index + m]);
            // 处理右边线
            if (position[0] === cell.end_col_index + 1 && position[1] === cell.start_row_index + m) {
              table.notAllowDrawLine.col.splice(i, 1);
              i--;
              continue B;
            }
            // 处理左边线
            if (position[0] === cell.start_col_index && position[1] === cell.start_row_index + m) {
              table.notAllowDrawLine.col.splice(i, 1);
              i--;
              continue B;
            }
          }
        }
      }
      // 处理线 ↑ 先实现功能 到时候再捋逻辑 整理代码
      // 我要重新修改 newCells 里边的位置 因为原来的那一套位置都不对了
      table.children.push(...newCells);
      table.sortingCells();
      table.children.forEach(cell => cell.typesetting());
      editor.refreshDocument();
      return true;
    }
    const everyColSize = table.col_size[endColIndex] / (colNum - focusCell.colspan + 1);
    if (everyColSize < 20 && colNum > focusCell.colspan) {
      editor.event.emit("message", "列宽不足，不允许拆分");
      return;
    }

    const addRowNum = rowNum - focusCell.rowspan
    const addColNum = colNum - focusCell.colspan;
    // 我应该先处理一遍线 将原来的线的位置全部都挪一下
    O: for (let i = 0; i < table.notAllowDrawLine.row.length; i++) {
      const position = table.notAllowDrawLine.row[i];
      if (addRowNum > 0 && position[0] > endRowIndex) {
        position[0] += (addRowNum);
      }
      if (addColNum > 0 && position[1] > endColIndex) {
        position[1] += addColNum;
      }
      // 在下边
      // 新增了几行几列 原来是不显示的线 现在有的需要显示了 所以要删除掉
      for (let j = 0; j < newCells.length; j++) {
        const cell = newCells[j];
        for (let m = 0; m < cell.colspan; m++) {
          // 处理上边线
          if (position[0] === cell.start_row_index && position[1] === cell.start_col_index + m) {
            table.notAllowDrawLine.row.splice(i, 1);
            i--;
            continue O;
          }

          // 处理下边线
          if (position[0] === cell.end_row_index + 1 && position[1] === cell.start_col_index + m) {
            table.notAllowDrawLine.row.splice(i, 1);
            i--;
            continue O;
          }
        }
      }
    }
    C: for (let i = 0; i < table.notAllowDrawLine.col.length; i++) {
      // 所有竖线都应该往右挪
      // 我得针对每条线都单独做判断进行处理
      // 因为有的竖线 只需要挪竖线位置 position[0] 另外一个 position[1] 是不需要动的 比如没有新增行 或者 末尾行上边的 都只需要挪动 position[0] 就可以了
      // 有的竖线两个位置都需要挪 比如跟末尾行同一行的竖线 并且又在右边 既增加了行 又增加了列 这个线的两个位置都需要挪动
      // 有的竖线 只需要挪动行线 即 只增加了行 并没有增加列的时候
      const position = table.notAllowDrawLine.col[i];
      // 如果在末尾列号右边的竖线 不管哪一行列号都应该增加
      if (addColNum > 0 && position[0] > endColIndex) {
        position[0] += (addColNum);
        // 行号不应该改变 因为不存在需要改行号的情况 如果有 那应该属于新增的竖线 在新增的地方处理就行
      }
      if (addRowNum > 0 && position[1] > endRowIndex) {
        position[1] += addRowNum;
      }
      for (let j = 0; j < newCells.length; j++) {
        const cell = newCells[j];
        // 这里每个单元格的每条线都应该显示出来
        for (let m = 0; m < cell.rowspan; m++) {
          // 处理右边线
          if (position[0] === cell.end_col_index + 1 && position[1] === cell.start_row_index + m) {
            table.notAllowDrawLine.col.splice(i, 1);
            i--;
            continue C;
          }
          // 处理左边线
          if (position[0] === cell.start_col_index && position[1] === cell.start_row_index + m) {
            table.notAllowDrawLine.col.splice(i, 1);
            i--;
            continue C;
          }
        }
      }
    }
    for (let i = 0; i < table.notAllowDrawLine.changeOpacityRow.length; i++) {
      const position = table.notAllowDrawLine.changeOpacityRow[i];
      if (addRowNum > 0 && position[0] > endRowIndex) {
        position[0] += (addRowNum);
      }
      if (addColNum > 0 && position[1] > endColIndex) {
        position[1] += addColNum;
      }
    }
    for (let i = 0; i < table.notAllowDrawLine.changeOpacityCol.length; i++) {
      const position = table.notAllowDrawLine.changeOpacityCol[i];
      if (addColNum > 0 && position[0] > endColIndex) {
        position[0] += addColNum;
        // 行号不应该改变 因为不存在需要改行号的情况 如果有 那应该属于新增的竖线 在新增的地方处理就行
      }
      if (addRowNum > 0 && position[1] > endRowIndex) {
        position[1] += addRowNum;
      }
    }
    for (let i = 0; i < table.children.length; i++) {
      const cell = table.children[i];

      const cellStartRowIndex = cell.start_row_index;
      const cellStartColIndex = cell.start_col_index;
      const cellEndColIndex = cell.end_col_index;
      const cellEndRowIndex = cell.end_row_index;
      // 删除当前被拆分的单元格
      if (cellStartRowIndex === startRowIndex && cellStartColIndex === startColIndex) {
        table.children.splice(i, 1);
        i--
        continue;
      }

      // 因为合并行和合并列是并行的 所以一次只处理一种线就可以了
      // 合并列 有可能在上边合并 也有可能在下边合并
      if (cellStartColIndex <= endColIndex && cellEndColIndex >= endColIndex && colNum > focusCell.colspan) {
        cell.colspan += addColNum
        for (let j = cellStartRowIndex; j <= cellEndRowIndex; j++) {
          for (let m = 0; m < addColNum; m++) {
            table.notAllowDrawLine.col.push([endColIndex + 1 + m, cellEndRowIndex < endRowIndex ? j : (addRowNum > 0 ? j + addRowNum : j)])
            if (cell.rowspan > 1 && j < cellEndRowIndex) {
              // 有合并行的单元格 如果增加了列 那么横线也应该加上
              table.notAllowDrawLine.row.push([cellEndRowIndex < endRowIndex ? j + 1 : (addRowNum > 0 ? j + addRowNum + 1 : j + 1), endColIndex + 1 + m]);
            }
            // 合并列的单元格会多出来透明度的横线 上下两个边框都要往里追加
            for (let n = 1; n <= addColNum; n++) {
              let upRow = cellStartRowIndex;
              let downRow = cellEndRowIndex;
              if (cellStartRowIndex > endRowIndex && addRowNum > 0) {
                upRow += addRowNum;
                downRow += addRowNum;
              }
              // 上边框
              // 横线是否透明依赖于左边的横线是否透明
              const dependentUp = table.notAllowDrawLine.changeOpacityRow.find(p => p[0] === upRow && p[1] === endColIndex + n - 1)
              const absentUp = table.notAllowDrawLine.row.find(p => p[0] === upRow && p[1] === endColIndex + n - 1)
              if (dependentUp || (absentUp && opacityRow)) {
                table.notAllowDrawLine.changeOpacityRow.push([upRow, endColIndex + n])
              }
              const dependentDown = table.notAllowDrawLine.changeOpacityRow.find(p => p[0] === downRow + 1 && p[1] === endColIndex + n - 1)
              const absentDown = table.notAllowDrawLine.row.find(p => p[0] === downRow + 1 && p[1] === endColIndex + n - 1)
              if (dependentDown || (absentDown && opacityRow)) {
                // 下边框
                table.notAllowDrawLine.changeOpacityRow.push([downRow + 1, endColIndex + n])
              }
            }
          }
        }

        // // 列合并多出来行线
        //   for (let j = 0; j < colNum - focusCell.colspan; j++) {
        //     // 只有在单元格的上边和下边才有可能合并列 等于不可能合并列
        //     cellStartRowIndex > endRowIndex && table.notAllowDrawLine.changeOpacityRow.push([cellEndRowIndex + (rowNum - focusCell.rowspan) + 1, cellEndColIndex + j])
        //     cellEndRowIndex < startRowIndex && table.notAllowDrawLine.changeOpacityRow.push([cellStartRowIndex, cellEndColIndex + j])
        //   }
      }

      // 合并行 有可能在左边合并 也有可能在右边合并
      if (cellStartRowIndex <= endRowIndex && cellEndRowIndex >= endRowIndex && rowNum > focusCell.rowspan) {
        cell.rowspan += addRowNum
        for (let j = cellStartColIndex; j <= cellEndColIndex; j++) {
          for (let m = 0; m < addRowNum; m++) {
            table.notAllowDrawLine.row.push([endRowIndex + 1 + m, cellEndColIndex < endColIndex ? j : (addColNum > 0 ? j + addColNum : j)]);
            if (cell.colspan > 1 && j < cellEndColIndex) {
              // 有合并列的单元格 如果增加了行 那么竖线也应该加上
              table.notAllowDrawLine.col.push([cellEndColIndex < endColIndex ? j + 1 : (addColNum > 0 ? j + addColNum + 1 : j + 1), endRowIndex + 1 + m]);
            }
          }
        }
        // 行合并多出来列线
        // 合并行的单元格会多出来透明度的竖线 左右两个边框都要往里追加
        for (let n = 1; n <= addRowNum; n++) {
          let leftCol = cellStartColIndex;
          let rightCol = cellEndColIndex;
          if (cellStartColIndex > endColIndex && addColNum > 0) {
            leftCol += addColNum;
            rightCol += addColNum;
          }
          // 竖线是否透明 依赖于上边的线是否透明
          // 而且那条线还得是能绘制出来的线
          const dependentLeft = table.notAllowDrawLine.changeOpacityCol.find(p => p[0] === leftCol && p[1] === endRowIndex + n - 1)
          const absentLeft = table.notAllowDrawLine.col.find(p => p[0] === leftCol && p[1] === endRowIndex + n - 1)
          if (dependentLeft || (absentLeft && opacityCol)) {
            // 左边框
            table.notAllowDrawLine.changeOpacityCol.push([leftCol, endRowIndex + n])
          }
          const dependentRight = table.notAllowDrawLine.changeOpacityCol.find(p => p[0] === rightCol + 1 && p[1] === endRowIndex + n - 1)
          const absentRight = table.notAllowDrawLine.col.find(p => p[0] === rightCol + 1 && p[1] === endRowIndex + n - 1)
          if (dependentRight || (absentRight && opacityCol)) {
            // 右边框
            table.notAllowDrawLine.changeOpacityCol.push([rightCol + 1, endRowIndex + n])
          }
        }
      }
      // 这些单元格都可能重复的修改 修改完了位置 修改 rowspan 往右挪完了还可能往下挪
      if (cellStartColIndex > endColIndex && colNum > focusCell.colspan) {
        cell.position[1] += (colNum - focusCell.colspan)
      }
      if (cellStartRowIndex > endRowIndex && rowNum > focusCell.rowspan) {
        cell.position[0] += (rowNum - focusCell.rowspan);
      }
    }



    for (let j = 0; j < newCells.length; j++) {
      const cell = newCells[j];
      // 新创建的单元格里边的线都不能绘制
      for (let m = 0; m < cell.rowspan; m++) {
        const dependentUp = table.notAllowDrawLine.changeOpacityCol.find(p => p[0] === cell.start_col_index && p[1] === cell.start_row_index + m - 1)
        const absentUp = table.notAllowDrawLine.col.find(p => p[0] === cell.start_col_index && p[1] === cell.start_row_index + m - 1)
        if (dependentUp || (absentUp && opacityCol)) {
          table.notAllowDrawLine.changeOpacityCol.push([cell.start_col_index, cell.start_row_index + m])
        }
        const dependentDown = table.notAllowDrawLine.changeOpacityCol.find(p => p[0] === cell.end_col_index + 1 && p[1] === cell.start_row_index + m - 1)
        const absentDown = table.notAllowDrawLine.col.find(p => p[0] === cell.end_col_index + 1 && p[1] === cell.start_row_index + m - 1)
        if (dependentDown || (absentDown && opacityCol)) {
          table.notAllowDrawLine.changeOpacityCol.push([cell.end_col_index + 1, cell.start_row_index + m])
        }
        for (let n = 0; n < cell.colspan && m > 0; n++) {
          table.notAllowDrawLine.row.push([cell.start_row_index + m, cell.start_col_index + n])
        }
      }
      for (let m = 0; m < cell.colspan; m++) {
        const dependentLeft = table.notAllowDrawLine.changeOpacityRow.find(p => p[0] === cell.start_row_index && p[1] === cell.start_col_index + m - 1)
        const absentLeft = table.notAllowDrawLine.row.find(p => p[0] === cell.start_row_index && p[1] === cell.start_col_index + m - 1)
        if (dependentLeft || (absentLeft && opacityRow)) {
          table.notAllowDrawLine.changeOpacityRow.push([cell.start_row_index, cell.start_col_index + m])
        }
        const dependentRight = table.notAllowDrawLine.changeOpacityRow.find(p => p[0] === cell.end_row_index + 1 && p[1] === cell.start_col_index + m - 1)
        const absentRight = table.notAllowDrawLine.row.find(p => p[0] === cell.end_row_index + 1 && p[1] === cell.start_col_index + m - 1)
        if (dependentRight || (absentRight && opacityRow)) {
          table.notAllowDrawLine.changeOpacityRow.push([cell.end_row_index + 1, cell.start_col_index + m])
        }
        for (let n = 0; n < cell.rowspan && m > 0; n++) {
          table.notAllowDrawLine.col.push([cell.start_col_index + m, cell.start_row_index + n]);
        }
      }
    }




    // 最后 row_size 和 col_size 并没有修改 需要改一下
    if (rowNum > focusCell.rowspan) {
      const everyRowSize = table.row_size[endRowIndex];
      table.row_size.splice(endRowIndex, 1);
      table.min_row_size.splice(endRowIndex, 1);
      for (let i = 0; i < rowNum - focusCell.rowspan + 1; i++) {
        table.row_size.splice(endRowIndex, 0, everyRowSize);
        table.min_row_size.splice(endRowIndex, 0, 10);
      }
    }

    if (colNum > focusCell.colspan) {
      table.col_size.splice(endColIndex, 1);
      for (let i = 0; i < colNum - focusCell.colspan + 1; i++) {
        table.col_size.splice(endColIndex, 0, everyColSize);
      }
    }
    table.children.push(...newCells);
    table.sortingCells();
    table.children.forEach(cell => cell.typesetting());
    const rows = arrDeduplication(table.notAllowDrawLine.row);
    const cols = arrDeduplication(table.notAllowDrawLine.col);
    sort(rows);
    sort(cols);
    table.notAllowDrawLine.row = rows;
    table.notAllowDrawLine.col = cols;
    editor.refreshDocument();
    return true;
    // 新版 ↑
  }

  static splitCellNotHaveBeenMerged(
    editor: Editor,
    rowNum: number,
    colNum: number
  ) {
    if (EditorLocalTest.useLocal) {
      return this.newVersionTest(editor, rowNum, colNum);
    }
    // 新版 ↓
    // 从来没有合并过的单元格拆分 就是说要将被拆分的单元格给替换成 rowNum * colNum 个新的单元格就可以了 然后将所有数据都放到第一个单元格里边
    if (rowNum <= 0 || colNum <= 0) return;

    const focusCell = editor.selection.getFocusCell()?.getOrigin();
    if (!focusCell) return

    const startRowIndex = focusCell.start_row_index;
    const startColIndex = focusCell.start_col_index;
    const table = focusCell.parent?.getOrigin();
    if (!table) return;
    const everyColSize = table.col_size[startColIndex] / colNum;
    if (everyColSize < 20) {
      editor.event.emit("message", "列宽不足，不允许拆分");
      return;
    }

    // 处理 新单元格的 position colspan 和 rowspan ↓
    const newCells: Cell[] = [];
    let retainColspan = 1;
    let retainRowspan = 1;
    if (focusCell.colspan >= colNum && focusCell.rowspan >= rowNum) {
      retainColspan = focusCell.colspan - colNum + 1;
      retainRowspan = focusCell.rowspan - rowNum + 1;
    }
    const initRowIndex = startRowIndex + (retainRowspan - 1);
    const initColIndex = startColIndex + (retainColspan - 1);
    for (let i = 0; i < rowNum; i++) {
      for (let j = 0; j < colNum; j++) {
        let cell: Cell;
        if (i === 0 && j === 0) {
          // 这是原始单元格
          cell = new Cell(editor, [startRowIndex, startColIndex], 1, 1, table);
          cell.position = [...focusCell.position];
          cell.rowspan = retainRowspan;
          cell.colspan = retainColspan;
          cell.paragraph = focusCell.paragraph;
          cell.paragraph.forEach((para) => {
            if (isParagraph(para)) {
              para.cell = cell;
            }
          });
          cell.handleFieldsAssignment(focusCell.fields);
        } else {
          cell = new Cell(editor, [i === 0 ? startRowIndex : initRowIndex + i, j === 0 ? startColIndex : initColIndex + j], 1, 1, table);
        }
        if (i === 0) {
          cell.rowspan = retainRowspan;
        }

        if (j === 0) {
          cell.colspan = retainColspan;
        }

        cell.insertEmptyParagraph();
        newCells.push(cell);
      }
    }
    // 处理 新单元格的 position colspan 和 rowspan ↑

    if (focusCell.colspan >= colNum && focusCell.rowspan >= rowNum) {
      // 这是要拆分的单元格原来的行和列都比即将拆分的行和列大的情况
      // 这种情况下 表格的 cols_size 和 row_size 都包含了 所以 col_size 和 row_size 是不需要改变的 其他的线也是不需要改变的 只是修改当前单元格的线就可以了
      // 其他单元格的任何位置也都不需要改变
      for (let i = 0; i < table.children.length; i++) {
        if (table.children[i] === focusCell) {
          table.children.splice(i, 1);
          break;
        }
      }
      // 处理线 ↓ 跟下边代码大量重复 先实现功能 到时候再捋逻辑 整理代码
      // 我应该先处理一遍线 将原来的线的位置全部都挪一下
      A: for (let i = 0; i < table.notAllowDrawLine.row.length; i++) {
        const position = table.notAllowDrawLine.row[i];
        // 新增了几行几列 原来是不显示的线 现在有的需要显示了 所以要删除掉
        for (let j = 0; j < newCells.length; j++) {
          const cell = newCells[j];
          // 这里每个单元格的每条线都应该显示出来
          for (let m = 0; m < cell.colspan; m++) {
            // 处理上边线
            if (position[0] === cell.start_row_index && position[1] === cell.start_col_index + m) {
              table.notAllowDrawLine.row.splice(i, 1);
              i--;
              continue A;
            }

            // 处理下边线
            if (position[0] === cell.end_row_index + 1 && position[1] === cell.start_col_index + m) {
              table.notAllowDrawLine.row.splice(i, 1);
              i--;
              continue A;
            }
          }
        }
      }
      B: for (let i = 0; i < table.notAllowDrawLine.col.length; i++) {
        const position = table.notAllowDrawLine.col[i];
        for (let j = 0; j < newCells.length; j++) {
          const cell = newCells[j];
          // 这里每个单元格的每条线都应该显示出来
          for (let m = 0; m < cell.rowspan; m++) {
            // 处理右边线
            if (position[0] === cell.end_col_index + 1 && position[1] === cell.start_row_index + m) {
              table.notAllowDrawLine.col.splice(i, 1);
              i--;
              continue B;
            }
            // 处理左边线
            if (position[0] === cell.start_col_index && position[1] === cell.start_row_index + m) {
              table.notAllowDrawLine.col.splice(i, 1);
              i--;
              continue B;
            }
          }
        }
      }
      // 处理线 ↑ 先实现功能 到时候再捋逻辑 整理代码
      // 我要重新修改 newCells 里边的位置 因为原来的那一套位置都不对了
      table.children.push(...newCells);
      table.sortingCells();
      table.children.forEach(cell => cell.typesetting());
      editor.refreshDocument();
      return true;
    }

    // 我应该先处理一遍线 将原来的线的位置全部都挪一下
    O: for (let i = 0; i < table.notAllowDrawLine.row.length; i++) {
      const position = table.notAllowDrawLine.row[i];
      // TODO 新增了几行几列 原来是不显示的线 现在有的需要显示了 所以要删除掉
      for (let j = 0; j < newCells.length; j++) {
        const cell = newCells[j];
        // 这里每个单元格的每条线都应该显示出来
        // 处理上边线
        if (position[0] === cell.start_row_index && position[1] === cell.start_col_index) {
          table.notAllowDrawLine.row.splice(i, 1);
          i--;
          continue O;
        }

        // 处理下边线
        if (position[0] === cell.end_row_index + 1 && position[1] === cell.start_col_index) {
          table.notAllowDrawLine.row.splice(i, 1);
          i--;
          continue O;
        }
      }


      if (position[0] > startRowIndex) {
        // 在下边
        position[0] += (rowNum - 1);
        if (position[1] > startColIndex) {
          position[1] += (colNum - 1);
        }
      }
      if (position[0] < startRowIndex) {
        // 在上边
        if (position[1] > startColIndex) {
          position[1] += (colNum - 1);
        }
      }
    }
    C: for (let i = 0; i < table.notAllowDrawLine.col.length; i++) {
      const position = table.notAllowDrawLine.col[i];
      for (let j = 0; j < newCells.length; j++) {
        const cell = newCells[j];
        // 这里每个单元格的每条线都应该显示出来
        // 处理右边线
        if (position[0] === cell.end_col_index + 1 && position[1] === cell.start_row_index) {
          table.notAllowDrawLine.col.splice(i, 1);
          i--;
          continue C;
        }
        // 处理左边线
        if (position[0] === cell.start_col_index && position[1] === cell.start_row_index) {
          table.notAllowDrawLine.col.splice(i, 1);
          i--;
          continue C;
        }
      }

      if (position[0] > startColIndex) {
        // 说明该线在右边
        position[0] += (colNum - 1);
        if (position[1] > startRowIndex) {
          position[1] += (rowNum - 1);
        }
      }
      if (position[0] < startColIndex) {
        // 说明在左边
        if (position[1] > startRowIndex) {
          position[1] += (rowNum - 1);
        }
      }
    }
    for (let i = 0; i < table.notAllowDrawLine.changeOpacityRow.length; i++) {
      const position = table.notAllowDrawLine.changeOpacityRow[i];
      if (position[0] > startRowIndex) {
        // 在下边
        position[0] += (rowNum - 1);
        if (position[1] > startColIndex) {
          position[1] += (colNum - 1);
        }
      }
      if (position[0] < startRowIndex) {
        // 在上边
        if (position[1] > startColIndex) {
          position[1] += (colNum - 1);
        }
      }
    }
    for (let i = 0; i < table.notAllowDrawLine.changeOpacityCol.length; i++) {
      const position = table.notAllowDrawLine.changeOpacityCol[i];
      if (position[0] > startColIndex) {
        // 说明该线在右边
        position[0] += (colNum - 1);
        if (position[1] > startRowIndex) {
          position[1] += (rowNum - 1);
        }
      }
      if (position[0] < startColIndex) {
        // 说明在左边
        if (position[1] > startRowIndex) {
          position[1] += (rowNum - 1);
        }
      }
    }
    for (let i = 0; i < table.children.length; i++) {
      const cell = table.children[i];

      const cellStartRowIndex = cell.start_row_index;
      const cellStartColIndex = cell.start_col_index;
      const cellEndColIndex = cell.end_col_index;
      const cellEndRowIndex = cell.end_row_index;

      // 删除当前被拆分的单元格
      if (cellStartRowIndex === startRowIndex && cellStartColIndex === startColIndex) {
        table.children.splice(i, 1);
        i--
        continue;
      }
      // 是所有行大于等于被拆分单元格行 或者列大于等于被拆分单元格列 的单元格都要修改 position 而且有的还要修改 rowspan colspan

      // 这些单元格要进行列合并
      if (cellStartColIndex <= startColIndex && cellEndColIndex >= startColIndex) {
        cell.colspan += (colNum - 1)
        // 列合并的情况下
        for (let j = cellStartRowIndex; j <= cellEndRowIndex; j++) {
          for (let m = 1; m <= cell.colspan; m++) {
            const r = cellStartRowIndex < startRowIndex ? 1 : rowNum; // 在拆分单元格的上方和下方是不一样的
            j < cellEndRowIndex && table.notAllowDrawLine.row.push([j + r, cellStartColIndex + m]);
            m < cell.colspan && table.notAllowDrawLine.col.push([cellStartColIndex + m, j + r - 1]);
          }
        }
      }

      if (cellStartRowIndex <= startRowIndex && cellEndRowIndex >= startRowIndex) {
        cell.rowspan += (rowNum - 1)
        // 行合并的情况下 不考虑上边已经挪过线的位置了 就直接将合并单元格里边的所有的线都放进去 最后再去重和排序
        for (let j = cellStartColIndex; j <= cellEndColIndex; j++) {
          for (let m = 1; m <= cell.rowspan; m++) {
            const l = cellStartColIndex < startColIndex ? 1 : colNum; // 在拆分单元格的左边和右边是不一样的
            j < cellEndColIndex && table.notAllowDrawLine.col.push([j + l, cellStartRowIndex + m - 1]);
            m < cell.rowspan && table.notAllowDrawLine.row.push([cellStartRowIndex + m, j + l - 1]);
          }
        }
      }
      // 这些单元格都可能重复的修改 修改完了位置 修改 rowspan 往右挪完了还可能往下挪
      if (cellStartColIndex > startColIndex) {
        cell.position[1] += (colNum - 1)
      }
      if (cellStartRowIndex > startRowIndex) {
        cell.position[0] += (rowNum - 1);
      }
    }
    // 最后 row_size 和 col_size 并没有修改 需要改一下
    const everyRowSize = table.row_size[startRowIndex];

    table.row_size.splice(startRowIndex, 1);
    for (let i = 0; i < rowNum; i++) {
      table.row_size.splice(startRowIndex, 0, everyRowSize);
    }
    table.col_size.splice(startColIndex, 1);
    for (let i = 0; i < colNum; i++) {
      table.col_size.splice(startColIndex, 0, everyColSize);
    }
    table.children.push(...newCells);
    table.sortingCells();
    table.children.forEach(cell => cell.typesetting());
    const rows = arrDeduplication(table.notAllowDrawLine.row);
    const cols = arrDeduplication(table.notAllowDrawLine.col);
    sort(rows);
    sort(cols);
    table.notAllowDrawLine.row = rows;
    table.notAllowDrawLine.col = cols;
    editor.refreshDocument();
    return true;
    // 新版 ↑
    // 老版 ↓
    // const focusCell = editor.selection.getFocusCell();
    // const originStartPath = focusCell!.start_path;
    // const originPosition = focusCell!.position;
    // const table = focusCell?.parent;
    // for (let i = 0; i < rowNum; i++) {
    //   Table.insertRowOrCol(editor, Direction.down);
    // }
    // for (let i = 0; i < colNum; i++) {
    //   Table.insertRowOrCol(editor, Direction.right);
    // }
    // for (let i = 0; i < originPosition[0]; i++) {
    //   const startCell = table!.getCellByPosition(i, originPosition[1]);
    //   let startCellPath;
    //   if (startCell) {
    //     startCellPath = startCell.start_path;
    //   } else {
    //     continue;
    //   }
    //   const endCell = table!.getCellByPosition(i, originPosition[1] + colNum);
    //   if (endCell) {
    //     const endCellPath = endCell.start_path;
    //     editor.selection.setSelectionByPath(startCellPath, endCellPath);
    //     Table.mergeCells(editor);
    //   } else {
    //     continue;
    //   }
    // }

    // for (
    //   let i = originPosition[0] + rowNum + 1;
    //   i < table!.row_size.length;
    //   i++
    // ) {
    //   const startCell = table!.getCellByPosition(i, originPosition[1]);
    //   let startCellPath;
    //   if (startCell) {
    //     startCellPath = startCell.start_path;
    //   } else {
    //     continue;
    //   }
    //   const endCell = table!.getCellByPosition(i, originPosition[1] + colNum);
    //   if (endCell) {
    //     const endCellPath = endCell.start_path;
    //     editor.selection.setSelectionByPath(startCellPath, endCellPath, "model_path");
    //     Table.mergeCells(editor);
    //   } else {
    //     continue;
    //   }
    // }

    // for (let i = 0; i < originPosition[1]; i++) {
    //   const startCell = table!.getCellByPosition(originPosition[0], i);
    //   let startCellPath;
    //   if (startCell) {
    //     startCellPath = startCell.start_path;
    //   } else {
    //     continue;
    //   }
    //   const endCell = table!.getCellByPosition(originPosition[0] + rowNum, i);
    //   if (endCell) {
    //     const endCellPath = endCell.start_path;
    //     editor.selection.setSelectionByPath(startCellPath, endCellPath);
    //     Table.mergeCells(editor);
    //   } else {
    //     continue;
    //   }
    // }

    // for (
    //   let i = originPosition[1] + colNum + 1;
    //   i < table!.col_size.length;
    //   i++
    // ) {
    //   const startCell = table!.getCellByPosition(originPosition[0], i);
    //   let startCellPath;
    //   if (startCell) {
    //     startCellPath = startCell.start_path;
    //   } else {
    //     continue;
    //   }
    //   const endCell = table!.getCellByPosition(originPosition[0] + rowNum, i);
    //   if (endCell) {
    //     const endCellPath = endCell.start_path;
    //     editor.selection.setSelectionByPath(startCellPath, endCellPath);
    //     Table.mergeCells(editor);
    //   } else {
    //     continue;
    //   }
    // }
    // editor.selection.setCursorPosition(originStartPath);
    // editor.update();
    // editor.render();
    // return true;
    // 老版 ↑
  }

  static isMobileTerminal({ editor }: {editor: Editor}) {
    if (editor.print_mode) return false
    const ua = navigator.userAgent;
    const isIpad = (/macintosh|mac os x/i.test(ua) && window.screen.height > window.screen.width && !ua.match(/(iPhone\sOS)\s([\d_]+)/)) || ua.match(/(iPad).*OS\s([\d_]+)/);
    const res = (/Mobi/.test(ua) || /Android/i.test(ua) || /iPhone|iPad|iPod/i.test(ua));
    return res || isIpad;
    // 用下边这个会导致 mac 上的浏览器不能打字
    // return navigator.userAgent.match(
    //   /(phone|pad|pod|iPhone|iPod|ios|iPad|Macintosh|MacIntel|Android|Mobile|BlackBerry|IEMobile|MQQBrowser|JUC|Fennec|wOSBrowser|BrowserNG|WebOS|Symbian|Windows Phone)/i
    // )
  }

  static clickScrollBar(editor: Editor, x: number, y: number) {
    // 是否点击在了单元格滚动模式下的滚动条上
    if (editor.internal.cell_is_scroll) {
      if (editor.internal.move_in_cell_bar) {
        editor.internal.click_cell_bar = true;
        const { cell } = editor.getElementByPoint(x, y);
        if (!cell) return false;
        cell.scroll_cell_bar_top = y;
        return true;
      } else {
        editor.internal.click_cell_bar = false;
        return false;
      }
    }

    const parentDom = editor.init_canvas.parentNode as HTMLElement;

    const min_x = editor.getNeedXYbyXY(
      parentDom.offsetWidth - 15 + editor.scrollX,
      y
    ).x;
    const max_x = editor.getNeedXYbyXY(
      parentDom.offsetWidth + editor.scrollX,
      y
    ).x;
    // 所有页面和页面间距加起来的高度
    const complete_height =
      ((editor.page_size.height + editor.config.page_margin_bottom) *
        editor.pages.length +
        editor.config.editor_padding_top) *
      editor.viewScale;

    // canvas高度
    const canvas_height =
      editor.init_canvas.height / editor.config.devicePixelRatio;

    // 滚动条高度
    const scroll_bar_height =
      (canvas_height * (canvas_height - 20)) / complete_height;

    // 滚动条滚动高度
    const scroll_height =
      ((editor.scroll_top * (canvas_height - scroll_bar_height - 20)) /
        (complete_height - canvas_height)) *
      editor.viewScale;
    if (min_x < x && x < max_x) {
      // 鼠标在滚动条内
      if (
        10 + scroll_height / editor.viewScale < y &&
        y <
        10 +
        scroll_height / editor.viewScale +
        scroll_bar_height / editor.viewScale
      ) {
        editor.internal.click_scroll_bar = true;
        editor.internal.scroll_bar_top = y;
      }
      // 鼠标在滚动条上三角处
      if (y > 0 && y <= 10) {
        editor.scrollByPage("top", complete_height, canvas_height);
      }
      // 鼠标在滚动条下三角处
      if (
        (editor.init_canvas.height / editor.config.devicePixelRatio - 10) /
        editor.viewScale <
        y &&
        y <
        editor.init_canvas.height /
        editor.config.devicePixelRatio /
        editor.viewScale
      ) {
        editor.scrollByPage("bottom", complete_height, canvas_height);
      }
      // 鼠标在滚动条内上方空白处
      if (y > 10 && y < 10 + scroll_height / editor.viewScale) {
        editor.scrollByPage("top", complete_height, canvas_height);
      } else if (
        // 鼠标在滚动条内下方空白处
        10 +
        scroll_height / editor.viewScale +
        scroll_bar_height / editor.viewScale <
        y &&
        y <
        (editor.init_canvas.height / editor.config.devicePixelRatio - 10) /
        editor.viewScale
      ) {
        editor.scrollByPage("bottom", complete_height, canvas_height);
      }
      return true;
    }
    return false;
  }

  static drawCommentScrollBar(editor: Editor, page?: Page) {
    const COMMENT_LIST_WIDTH = Math.max(editor.config.comment.listWidth, COMMENT_LIST_MIN_WIDTH);
    if (page) {
      const scrollLeft = page.right + 1 + COMMENT_LIST_WIDTH - COMMENT_SCROLL_MARGIN; // 减 10 是为了有一点空
      const commentTotalHeight = page.commentTotalHeight;
      const commentAreaHeight = page.height;

      // 为啥需要减 20 不知道 但是 减 20 是正好的
      const scrollBarHeight = (commentAreaHeight * commentAreaHeight) / commentTotalHeight - 20;

      const scrollTop = commentAreaHeight / commentTotalHeight * page.scrollTop + page.top;

      if (editor.hold_mouse) {
        // console.log("这是按住拖动了");
      }

      Renderer.drawRoundRect(
        scrollLeft,
        scrollTop,
        COMMENT_SCROLL_BAR_WIDTH,
        scrollBarHeight,
        5,
        "rgba(172,172,172)"
      );
      return
    }
    const left = editor.page_left + editor.page_size.width + 286;
    const top = editor.scroll_top
      ? editor.scroll_top + 50
      : editor.pages[0].top + 50;
    const bottom =
      (editor.init_canvas.height / editor.config.devicePixelRatio - 50) / editor.viewScale + top;
    const barHeight =
      (editor.init_canvas.height / editor.config.devicePixelRatio - 50) / editor.viewScale;
    // 滚动条上三角
    Renderer.draw_rectangle(
      left,
      Math.round(top),
      15,
      barHeight,
      "rgb(241,241,241)"
    );


    // 所有的评论卡片的高度
    const totalHeight = editor.internal.totalCommentHeight;
    // canvas高度
    const canvasHeight =
      (editor.init_canvas.height / editor.config.devicePixelRatio - 50) / editor.viewScale
    // 滚动条高度
    const scrollBarHeight = (canvasHeight * canvasHeight) / totalHeight;
    // scrollBarHeight为滚动条被拖动的高度，scrollBarTop为滚动条上部的位置
    const scrollHeight = editor.internal.scrollBarMovedHeight;
    // 如果没有进行拖动，滚动条的初始位置应该是，后边所有的位置变化都应该在这个基础上进行
    const originTop = top;
    // 变化后的位置应该等于初始的位置加上拖动的距离，拖动后的位置为scrollBarTop,并且该值应该在鼠标松开后记录下来
    // 当作下一次拖动的初始位置
    let scrollBarTop;
    if (editor.internal.scrollBarTop) {
      // 如果移动过了，就是移动之后的顶部位置加滚动距离，scrollBarTop在鼠标松开的时候记录一次
      scrollBarTop = originTop + editor.internal.scrollBarTop + scrollHeight;
    } else {
      // 没被拖动就是初始位置加上移动距离
      scrollBarTop = originTop + scrollHeight;
    }
    // 控制滚动条的上边界
    scrollBarTop = scrollBarTop < top ? top : scrollBarTop;
    // 控制滚动条的下边界
    scrollBarTop =
      scrollBarTop > bottom - scrollBarHeight
        ? bottom - scrollBarHeight
        : scrollBarTop;
    // Renderer.drawRoundRect(x: number, y: number, width: number, height: number, radius: number, fillColor: string)
    if (
      editor.internal.cursorIsOnScrollBar ||
      editor.internal.cursorIsCrossScrollBar
    ) {
      Renderer.drawRoundRect(
        left + 2,
        Math.round(scrollBarTop),
        11,
        scrollBarHeight,
        5,
        "rgba(192,192,192)"
      );
    } else {
      Renderer.drawRoundRect(
        left + 4,
        Math.round(scrollBarTop),
        6,
        scrollBarHeight,
        5,
        "rgba(172,172,172)"
      );
    }
  }

  static judgeCursorIsOnCommentScrollBar(editor: Editor, x: number, y: number) {
    // 所有批注的高度
    const commentBox = editor.commentBox;
    let totalHeight = commentBox.length * 30;
    commentBox.forEach((comment: any) => {
      totalHeight += comment.height;
    });
    // canvas高度
    const canvasHeight =
      (editor.init_canvas.height / editor.config.devicePixelRatio - 50) / editor.viewScale
    // 滚动条高度
    const scrollBarHeight = (canvasHeight * canvasHeight) / totalHeight;
    // 滚动条滚动高度
    // const scrollHeight = editor.internal.scrollBarHeight;
    // 鼠标点击在滚动条上
    const left = editor.page_left + editor.page_size.width + 288;
    const right = left + 11;
    const scrollTop = editor.internal.scrollBarTop;
    const bottom = scrollTop + scrollBarHeight;

    if (x > left && x < right) {
      if (y > scrollTop && y < bottom) {
        return true;
      } else {
        return false;
      }
    } else {
      return false;
    }
  }

  static dragCommentScrollBar(editor: Editor) {
    // 总的应该是，传进来一个高度，该高度为鼠标在拖动过程中移动的距离
    // 评论总高度
    const totalHeight = editor.internal.totalCommentHeight;
    // // canvas的高度
    const canvasHeight =
      (editor.init_canvas.height / editor.config.devicePixelRatio - 50) / editor.viewScale
    // 滚动条滚动的高度和实际的长度的比例
    const ratio = totalHeight / canvasHeight;
    // 滚动条滚动高度
    return ratio;
  }
  static clearAllComment(editor: Editor) {
    const cell = editor.current_cell
    const allParas: any = []
    cell.paragraph.forEach((para) => {
      if (isParagraph(para)) {
        allParas.push(para)
      } else {
        const children = para.children
        children.forEach((child) => {
          child.paragraph.forEach((p) => {
            allParas.push(p)
          })
        })
      }
    })
    allParas.forEach((para: any) => {
      const children = para.characters
      children.forEach((child: any) => {
        if (child.comment_id) {
          child.comment_id = ''
        }
      })
    })
    editor.document_meta.commentsIDSet = {};
    editor.document_meta.abolishCommentIDSet = {};
    editor.refreshDocument()
  }

  /**
   * 光标在 批注列表里边 就返回所在的那一页 否则就返回 undefined
   * @param editor
   * @param x
   * @param y
   * @returns
   */
  static getCommentInfoByCursorByPosition(editor: Editor, x: number, y: number, isClick?: boolean): {page?: Page, comment?: CommentBox, isReplace?: boolean, isDelete?: boolean, isOpen?: boolean, isClose?: boolean, isOnScrollBar?: boolean, isSwitch?: boolean} {
    const topWithSrollTop = y + editor.scroll_top;
    let currentPage;
    let comment;
    let isReplace;
    let isDelete;
    let isOpen;
    let isClose;
    let isOnScrollBar;
    let isSwitch;
    const COMMENT_LIST_WIDTH = Math.max(editor.config.comment.listWidth, COMMENT_LIST_MIN_WIDTH);
    for (const page of editor.pages) {
      if (page.top <= topWithSrollTop && topWithSrollTop <= page.bottom && page.right <= x && x <= page.right + COMMENT_LIST_WIDTH) {
        currentPage = page;
        // TODO 没大明白 topWithScrollTop 不需要 减去 page.top 也修改了配置 结果是正确的
        const topRelativeToPage = topWithSrollTop + page.scrollTop; // 得是在相对于页面位置的基础上 再加上批注列表里边的滚动 就相对于在批注列表里边的位置了

        // 计算滚动条 ↓
        // 这些跟 drawCommentScrollBar 里边的逻辑大概是一样的 大部分都不用实时计算 在需要的时候计算下就行 可以做个缓存 后期优化吧
        const scrollLeft = page.right + 1 + COMMENT_LIST_WIDTH - COMMENT_SCROLL_MARGIN;
        const commentTotalHeight = page.commentTotalHeight;
        const commentAreaHeight = page.height;
        const scrollBarHeight = (commentAreaHeight * commentAreaHeight) / commentTotalHeight - 20;
        const scrollTop = commentAreaHeight / commentTotalHeight * page.scrollTop + page.top;
        if (x > scrollLeft && x < scrollLeft + COMMENT_SCROLL_BAR_WIDTH) {
          if (topWithSrollTop > scrollTop && topWithSrollTop < scrollTop + scrollBarHeight) {
            isOnScrollBar = true;
            editor.isOnScrollBar = true;
            break;
          }
        }
        // 计算滚动条 ↑

        const marginWidth = (COMMENT_LIST_WIDTH - COMMENT_WIDTH) / 2;
        const crossLeft = page.right + 1 + marginWidth + (COMMENT_WIDTH - COMMENT_TITLE_PADDING_RIGHT) - COMMENT_TITLE_CROSS_WIDTH;
        if (x > crossLeft && x < crossLeft + COMMENT_TITLE_CROSS_WIDTH ) {
          if (topRelativeToPage > page.top + COMMENT_TITLE_CROSS_TOP && topRelativeToPage < page.top + COMMENT_TITLE_CROSS_TOP + COMMENT_TITLE_CROSS_HEIGHT) {
            isClose = true;
            break;
          }
        }
        if (!editor.config.comment.hideSwitch) {
          if (x > crossLeft - COMMENT_SWITCH_DISTANCE_TO_CROSS - COMMENT_SWITCH_WIDTH && x < crossLeft - COMMENT_SWITCH_DISTANCE_TO_CROSS) {
            // 绘制 switch 的时候传参位置是 往左便宜 30 宽度是 20 也就是那边改的话 这里要同步的改 ... 这 ...
            if ( topRelativeToPage > page.top + COMMENT_TITLE_CROSS_TOP && topRelativeToPage < page.top + COMMENT_TITLE_CROSS_TOP + COMMENT_TITLE_CROSS_HEIGHT) {
              isSwitch = true;
              break;
            }
          }
        }
        for (let i = 0; i < page.commentBox.length; i++) {
          const c = page.commentBox[i];
          if (x > c.left && x < c.left + c.width) {
            const top = c.top + page.scrollTop; // 这是批注相对于页面的位置 也就是相对于批注列表里边的位置
            if (topRelativeToPage > top &&  topRelativeToPage < top + c.height) {
              if (isClick) {
                comment = c;
                if (topRelativeToPage > top + 10 && topRelativeToPage < top + 35) {
                  if (x > c.left + 180 && x < c.left + 204) {
                    isReplace = true;
                  } else if (x > c.left + 210 && x < c.left + 234) {
                    isDelete = true;
                  } else if (x > c.left + 240 && x < c.left + 265) {
                    isOpen = true;
                  }
                }
              }
              comment = c;
            }
          }
        }
        break;
      }
    }
    return { page: currentPage, comment, isReplace, isDelete, isOpen, isClose, isOnScrollBar, isSwitch }
  }

  static judgeIfCursorInCommentRange(editor: Editor, x: number) {
    if (!editor.is_comment_mode) return;
    const left = editor.page_left + editor.page_size.width;
    const right = left + 300;
    // const top = editor.scroll_top ? editor.scroll_top : editor.pages[0].top;
    // const bottom = editor.init_canvas.height / editor.config.devicePixelRatio - 50;
    if (x > left && x < right) {
      editor.internal.IsInCommentRange = true;
    } else {
      editor.internal.IsInCommentRange = false;
    }
  }

  static userLogin({ editor, userInfo }: {editor: Editor, userInfo?: any}) {
    if (!userInfo) return;
    editor.user = userInfo;
    if (!editor.document_meta.userInfo) {
      editor.document_meta.userInfo = [];
    }
    if (
      !editor.document_meta.userInfo.some((user: any) => user.id === userInfo.id)
    ) {
      editor.document_meta.userInfo.push(userInfo);
    }
  }

  static judgeImageAllLoad({ editor }: {editor: Editor}) {
    const imageMap = editor.imageMap.get();
    const images = imageMap.values();
    for (const val of images) {
      if (!val.isLoaded) {
        return false;
      }
    }
    return true;
  }

  static fillContentByJson(
    { rawData, jsonData, jsonType, isClear, editor } : {
      rawData: any,
    jsonData: any,
    jsonType: string,
    isClear: boolean,
    editor: Editor
    }
  ) {
    const oriScrollTop = editor.scroll_top;
    editor.internal.autoFill.fillContentByJson(
      editor,
      rawData,
      jsonData,
      jsonType,
      isClear
    );
    editor.scroll_top = oriScrollTop;
    editor.render();
    return true;
  }

  static setHighlightOrReplaceTextByParams(params: any, editor: Editor) {
    const currentCell = editor.current_cell;
    const paragraphs = currentCell.paragraph;
    const paraId = params.paraId;
    const targetText = params.targetText;
    const textIndex = params.targetIndex;
    const replaceText = params.replaceText;
    let targetParagraph;
    if (paraId) {
      paragraphs.forEach((para) => {
        if (isTable(para)) {
          const children = para.children;
          children.forEach((child) => {
            const paragraph = child.paragraph;
            paragraph.forEach((p) => {
              if (p.id === paraId) {
                targetParagraph = p;
                return;
              }
            });
          });
        } else {
          if (para.id === paraId) {
            targetParagraph = para;
          }
        }
      });
    }
    if (targetParagraph) {
      if (!params.replaceText) {
        editor.searchAll(
          targetText,
          true,
          undefined,
          undefined,
          targetParagraph,
          textIndex
        );
      } else {
        editor.replaceParagraphTextByIndex(
          targetParagraph,
          targetText,
          replaceText,
          textIndex
        );
      }
    }
  }
  static judgeClickSelectAllTableBtn(editor: Editor, x: number, y: number) {
    const leftBorder = editor.page_left + editor.config.page_padding_left;
    if (x <= leftBorder && x >= leftBorder - 10) {
      if (
        y + editor.scroll_top >= editor.internal.currentTableTop - 10 &&
        y + editor.scroll_top <= editor.internal.currentTableTop + 10 &&
        editor.internal.isCrossTable
      ) {
        editor.internal.isCrossTable = true;
        return true;
      }
    }
  }

  static getFieldContentByName(editor: Editor, fieldsName: string[], simpleRes: boolean, checkEmpty: boolean) {
    const result: any = {};

    const processField = (field: any) => {
      const curFieldInfo: any = { id: field.id, content: [] };
      const children = field.children;
      if (children.length) {
        let textBuffer = ''; // 用于存储连续的character类型的文本

        const flushTextBuffer = () => {
          if (textBuffer) {
            if (simpleRes) {
              curFieldInfo.content.push('text');
            } else {
              curFieldInfo.content.push({ type: 'text', value: textBuffer });
            }
            textBuffer = '';
          }
        };

        const processChild = (child: any) => {
          if (checkEmpty) {
            if (!isField(child) || isBoxField(child)) {
              curFieldInfo.content = true;
              return curFieldInfo;
            } else {
              curFieldInfo.content = false;
            }
          } else {
            if (isField(child)) {
              const nestedFieldInfo = processField(child);
              curFieldInfo.content.push(...nestedFieldInfo.content); // 展开并添加到当前 field 的 content 中
            } else if (isCharacter(child)) {
              if (child.value !== " ") {
                textBuffer += child.value;
              }
            } else {
              flushTextBuffer();
              if (isImage(child)) {
                if (simpleRes) {
                  curFieldInfo.content.push('image');
                } else {
                  curFieldInfo.content.push({ type: 'image', value: child.src, meta: child.meta });
                }
              } else if (isWidget(child)) {
                if (simpleRes) {
                  curFieldInfo.content.push(child.widgetType);
                } else {
                  curFieldInfo.content.push({ type: child.widgetType, value: child.selected ? 1 : 0 });
                }
              } else if (isButton(child)) {
                if (simpleRes) {
                  curFieldInfo.content.push('button');
                } else {
                  curFieldInfo.content.push({ type: 'button', value: child.value });
                }
              } else if (isBoxField(child)) {
                processBoxField(child);
              }
            }
          }
        };

        const processBoxField = (boxField: any) => {
          const createBoxFieldInfo = (field: any) => {
            if (simpleRes) {
              return "boxField";
            } else {
              const widget = field.children[0];
              if (isWidget(widget)) {
                return {
                  type: 'boxField',
                  text: field.text,
                  value: widget.selected ? 1 : 0,
                  boxMulti: field.box_checked,
                  formulaValue: field.formula_value,
                  displayType: field.display_type
                };
              }
            }
          };
          if (boxField.children.length && isBoxField(boxField.children[0])) {
            boxField.children.forEach((child: any) => {
              if (isBoxField(child)) {
                curFieldInfo.content.push(createBoxFieldInfo(child));
              }
            });
          } else {
            if (simpleRes) {
              curFieldInfo.content.push('boxField');
            } else {
              curFieldInfo.content.push(createBoxFieldInfo(boxField));
            }
          }
        };
        children.forEach(processChild);
        flushTextBuffer();
      } else if (checkEmpty && !children.length) {
        curFieldInfo.content = false;
        return curFieldInfo;
      }
      if (simpleRes) {
        curFieldInfo.content = [...new Set(curFieldInfo.content)];
      }
      return curFieldInfo;
    };

    fieldsName.forEach(fieldName => {
      result[fieldName] = [];
      const fields = editor.getFieldsByName(fieldName);
      if (fields.length) {
        fields.forEach((field: any) => {
          result[fieldName].push(processField(field));
        });
      }
    });

    return result;
  }

}
