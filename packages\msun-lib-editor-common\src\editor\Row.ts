import Paragraph from "./Paragraph";
import Character from "./Character";
import Cell from "./Cell";
import FlowRectangle from "./FlowRectangle";
import Renderer from "./Renderer";
import { isCharacter, isRow, nearest, numberTo<PERSON>hine<PERSON>, uuid } from "./Utils";
import { Config } from "./Config";
import Image from "./Image";
import Widget from "./Widget";
import Line from "./line";
import Box from "./Box";
import Group from "./Groups";
import { Path } from "./Path";
import { getCellIndex, isBox, isButton, isCell, isImage, isLine, isWidget } from "./Helper";
import XField from "./XField";
import Editor from "./Editor";
import Button from "./Button";
import { ElementInParagraph, RowLineType } from "./Definition";
import { FieldShowMode } from "./EditorConfig";
import Table from "./Table";

export default class Row extends FlowRectangle {
  id: string;

  paragraph!: Paragraph;

  parent: Cell;

  children: (Character | Image | Widget | Line | Box | Button)[] = [];

  linebreak: Character | null = null;

  cursor_position: number = 0; // 游标位置

  padding_vertical: number = 0;

  padding_left: number = 0;

  padding_top: number = 0;

  padding_right: number = 0;

  padding_bottom: number = 0;

  cell_index: number = 0; // cell中的index

  row_index_in_para: number = 0; // 段落中的row下标

  page_number: number = 0; // 该Row属于第几页 从 1 开始，0代表未赋初始值

  page_index: number = -1; // 该Row在当前页的下标 在page容器中的索引值，从 0 开始，-1代表未赋初始值

  para_id: string = "";

  tail_width: number;

  page_break: boolean = false;

  field_chars_in_row: any = {};// 储存row中所有的在不同文本域中的字符

  // 改成modelData
  get pre_row() {
    const originCell = this.parent.getOrigin()
    return originCell.children[originCell.children.findIndex((row)=>row.id === this.id) - 1]
  }

  get next_row() {
    const originCell = this.parent.getOrigin()
    return originCell.children[originCell.children.findIndex((row)=>row.id === this.id) + 1]
  }

  /**
   * 获取上一个容器
   */
  get previous_container() {
    return this.parent.children[this.cell_index - 1];
  }

  /**
   * 获取下一个容器
   */
  get next_container() {
    return this.parent.children[this.cell_index + 1];
  }

  /**
   * 获取当前行所在的group
   */
  get group(): Group | null | undefined {
    if (this.paragraph.group_id) {
      return this.parent.getGroupById(this.paragraph.group_id);
    } else {
      if (this.parent.parent) {
        const group_id = this.parent.parent.group_id;
        if (group_id) {
          return this.parent.parent.parent.getGroupById(group_id);
        }
      }
    }
    return null
  }

  /**
   * 判断当前行是否在页眉页脚内
   */
  get is_hf_part(): boolean {
    if (this.parent.parent) {
      return !!this.parent.parent.parent.hf_part;
    } else {
      return !!this.parent.hf_part;
    }
  }

  get start_path(): Path {
    const path: Path = [];
    if (this.parent.parent) {
      path.push(this.parent.parent.cell_index, getCellIndex(this.parent));
    }
    path.push(this.parent.children.indexOf(this), 0);
    return path;
  }

  get end_path(): Path {
    const path: Path = [];
    if (this.parent.parent) {
      path.push(this.parent.parent.cell_index, getCellIndex(this.parent));
    }
    path.push(this.parent.children.indexOf(this), this.children.length);
    return path;
  }

  static deleteWithCaretOrField(editor: Editor, field?: XField) {
    let para_is_empty = false;
    if (field) {
      // 开始坐标计算
      const field_start_path = field.start_para_path;
      const field_start_path_model = editor.paraPath2ModelPath(field_start_path);
      field_start_path_model[field_start_path_model.length - 1] = 0;
      const delete_start_path = field_start_path_model;
      // 结束坐标计算
      const field_end_path = field.end_para_path;
      const field_end_path_model = editor.paraPath2ModelPath(field_end_path);
      editor.selection.setCursorPosition(field_end_path_model);
      const focus_row = editor.selection.getFocusRow();
      field_end_path_model[field_end_path_model.length - 1] = focus_row.children.length;
      const delete_end_path = field_end_path_model;
      // 设置选区
      editor.selection.setSelectionByPath(delete_start_path, delete_end_path, "model_path");
    } else {
      const row = editor.selection.getFocusRow();
      const model_focus = editor.selection.focus;
      if (row.children.length) {
        const start_index = 0;
        const end_index = row.children.length;

        let start_path = [];
        let end_path = [];
        if (row.parent.parent) {
          start_path = [model_focus[0], model_focus[1], model_focus[2], start_index];
          end_path = [model_focus[0], model_focus[1], model_focus[2], end_index];
        } else {
          start_path = [row.cell_index, start_index];
          end_path = [row.cell_index, end_index];
        }
        editor.selection.setSelectionByPath(start_path, end_path, "model_path");
      }
    }
    editor.delete_backward();
    para_is_empty = editor.selection.getFocusParagraph().characters.length === 1;
    if (para_is_empty) {
      editor.delete_backward();
    }
  }

  constructor(parent: Cell, left: number, top: number, width: number) {
    super(left, top, width, 0);
    this.id = uuid("row");
    this.tail_width = width;
    this.parent = parent;
  }

  /**
   * @description: 原始数据 或者在以后的模板中，两个不同的节点内容，如果是\n结尾的，那么这个方法结束返回[]，如果是字符结尾，那么这里返回null
   * @param {Character} characters 为所有需要添加的数据对象
   * @return {*} [] | null
   */
  insertCharacters(characters: ElementInParagraph[], fieldMap: any): ElementInParagraph[] | null {
    this.linebreak = null;
    this.cursor_position = this.padding_left;
    let recordWidth = 0;
    let index = 0;
    const getFieldByMap = (fieldId: string) => {
      let field = fieldMap[fieldId]
      if (!field) {
        field = this.parent.getFieldById(fieldId);
        fieldMap[fieldId] = field
      }
      return field;
    }
    for (let i = 0; i < characters.length; i++) {
      const current_char = characters[i];
      let field;
      // 根据配置控制显示隐藏文本域边框,放在该处有效提升性能
      if (isCharacter(current_char) && current_char.field_position !== "normal") {
        const config = this.parent.editor.config;
        // this.group?.lock || this.is_hf_part 分别控制锁定分组与页眉不显示文本域边框字符与背景文本,改动后需要调整分组锁定与解锁接口及页眉页脚双击编辑与退出编辑接口
        if (!config.show_field_symbol ||
          (config.fieldShowMode !== FieldShowMode.normal && current_char.field_position !== "placeholder")) {
          current_char.width = current_char.draw_width = this.parent.editor.document_meta.fieldSymbolWidth === 0 ? 0.001 : 1;
        } else {
          // 还原字符、背景文本字符宽度
          current_char.width = current_char.draw_width = current_char.ori_width;
          if (current_char.field_id) {
            let boxField = getFieldByMap(current_char.field_id)
            if (boxField?.type === "box" && boxField.parent) {
              current_char.width = current_char.draw_width = 0.001;
            }
          }
        }
        // 如果char是placeholder且文本域边框为下划线类型，则宽度置为placeholder的宽
        if (current_char.field_position === "placeholder" && current_char.field_id) {
          field = getFieldByMap(current_char.field_id)
          if (field?.display_type === "line") {
            current_char.width = current_char.draw_width = current_char.ori_width;
          }
        }
      }
      let noAdjustSpacing = false;
      // 调整定宽文本域中每个字符宽度值
      if (current_char.field_id) {
        // 先根据id获取每个文本域
        field = getFieldByMap(current_char.field_id)
        if (!field) {
          // TODO 此处不应该为空，需查下该bug
          // console.error("此处不应该为空，需查下该bug");
          continue;
        }
        if (!this.parent.noWrap && (field.min_width > 0 || field.max_width > 0)) {
          field.setFieldMinOrMaxWidth(current_char, this);
          noAdjustSpacing = true;
        }
        const allParentFields = field.getAllParents()
        const result = allParentFields.some((e: any) => e.type === "anchor")
        //判断field的父文本域中是否含有锚点，如果有，把含有锚点的所有子文本域的before_type置为anchor
        field.judgeFieldNeedRecovery()
        if (field.type === "anchor") {
          current_char.font = this.parent.editor.fontMap.add(Object.assign({}, current_char.font, { height: 0 }))
          current_char.width = 0.00001;
        }

      }
      // 字符距离左边距
      if (this.cursor_position >= this.paragraph.content_padding_left && this.paragraph.itemsWidth?.length > 0 && (this.paragraph.itemsWidth[0] !== "x")) {
        if (current_char.value === " ") {
          current_char.width = this.paragraph.itemsWidth[index] - recordWidth;
          recordWidth = 0;
          index++;
        } else {
          recordWidth += current_char.width;
        }
      }
      const width = this.cursor_position + current_char.width - this.padding_left;
      if (current_char.value === "\n") {
        // 需要设置换行符左边距,否则影响选区功能
        current_char.left = this.cursor_position;
        // 遇到换行符的逻辑
        this.linebreak = current_char as Character;
        const row_have_image = this.children.findIndex((e) => (isImage(e)));
        if (row_have_image === -1) {
          this.padding_vertical = (this.height - this.height / this.paragraph.row_ratio) / 2;
        } else {
          this.padding_vertical = Config.img_margin;
        }
        return null;
      }
      if (isLine(current_char)) {
        this.height = current_char.height;
      }
      (current_char as Character).draw_width = current_char.width + 1; // 很窄的单元格,如果设置字体为初号,就会进 if 判断,如果 draw_width 不重新赋值,字就扁长了 本来 else 里边就有 所以抽出来 +1 是为了背景色不留空
      current_char.left = 0; // 加上这个以后 拖动表格线至最小时 排版不会错乱 因为不加 字符的left会用上一次的left 所以重置为0 下边会再计算
      // 该 Row 的宽度 减去 该 Row 的内边距 所剩的距离 不够装下一个字了 就应该换行了 就是要新增一行
      const v = current_char.width < 0.1 && i === characters.length - 2 ? 0.001 : 0;
      if (this.width - this.padding_left + v < width) {
        if (current_char.field_position === "end" && field?.min_width) {
          current_char.left = this.width;
          this.adjustHeight(current_char)
          const row_have_image = this.children.findIndex((e) => (isImage(e)));
          if (row_have_image === -1) {
            this.padding_vertical = (this.height - this.height / this.paragraph.row_ratio) / 2;
          } else {
            this.padding_vertical = Config.img_margin;
          }
          if (i === 0 && this.width < 0) {
            this.children.push(current_char);
            // 调整行高
            this.resetRowHight();
            i++; // i 为 0 的时候再那样截取就相当于原样返回了 就会死循环 所以加 +1
          }
          return characters.slice(i, characters.length);
        }
        if (this.children.length === 0) {
          this.children.push(current_char);
          // 调整行高
          this.resetRowHight();

          return characters.slice(i + 1, characters.length);
        }
        // 下一行以及以后的所有character(应该返回出去的character集合)：需要在下一行插入的内容，包括行首行尾非法字符
        let returnCharacters = this.adjustNoHeadTailChar(current_char);
        // 若返回数组不是空，当前行去掉returnCharacter这些数据，并调整字间距
        if (returnCharacters.length !== 0) {
          if (field) {
            // 先根据id获取每个文本域
            for (const key in this.field_chars_in_row) {
              if (field.id === key) {
                if (this.field_chars_in_row[key].meta.length > 1) {
                  this.field_chars_in_row[key].meta.splice(this.field_chars_in_row[key].meta.length - returnCharacters.length, returnCharacters.length);
                  // 换行后meta信息为空导致报错，需要删掉储存在field_chars_in_row对应key的数据
                  if (!this.field_chars_in_row[key].meta.length) {
                    delete this.field_chars_in_row[key];
                  }
                  break;
                }
              }
            }
          }
          this.children.splice(
            this.children.length - returnCharacters.length,
            returnCharacters.length
          );
        }
        // 计算当前行剩余空间
        this.tail_width = this.calculateRowAndCharWidth().D_value;
        // 调整字间距, 非字符对齐时调整
        if (this.paragraph.align !== "docuAlign" && !noAdjustSpacing) {
          this.adjustSpacing();
        }
        // 调整行高
        this.resetRowHight();
        // TODO 注意，解构性能太低，以下两行代码在处理300行内容时 一个耗时50+ms 一个耗时5ms以内
        // returnCharacters.push(...characters.slice(i, characters.length));
        returnCharacters = returnCharacters.concat(characters.slice(i, characters.length));

        const row_have_image = this.children.findIndex((e) => (isImage(e)));
        if (row_have_image === -1) {
          this.padding_vertical = (this.height - this.height / this.paragraph.row_ratio) / 2;
        } else {
          this.padding_vertical = Config.img_margin;
        }
        return returnCharacters;
      } else {
        // re设置字符位置
        current_char.left = this.cursor_position;
        // 在行里添加上当前元素
        this.children.push(current_char);
        // 如果字符上存在文本域id，则将row的字符按文本域id放到field_chars_in_row中，draw方法时使用
        if (current_char.field_id && (field?.display_type === "input" || field?.display_type === "line" || field?.display_type === "printInput")) {
          if (this.field_chars_in_row[current_char.field_id]) {
            this.field_chars_in_row[current_char.field_id].meta.push(current_char);
            this.field_chars_in_row[current_char.field_id].type = field?.display_type;
          } else {
            this.field_chars_in_row[current_char.field_id] = {
              meta: [current_char],
              type: field?.display_type
            };
          }
        }
        this.adjustHeight(current_char);
        // // 正常插入 更新剩余空间
        this.tail_width = this.calculateRowAndCharWidth().D_value;
        // 设置每个字符对象的top，之前是在render的时候设置的
        let offset = 0;
        if (isCharacter(current_char)) {
          offset = current_char.font.characterSpacing ? current_char.font.characterSpacing : 0;
        }
        this.cursor_position += current_char.width + offset;
      }
    }
    return null;
  }

  /**
   * 调整当前行行高
   * @param current_char 循环插入的时候第n个对象
   */
  adjustHeight(current_char: ElementInParagraph) {
    if (isCharacter(current_char)) {
      // 判断行高
      if (
        this.height < Math.round(current_char.height * this.paragraph.row_ratio)
      ) {
        // 调整行高
        this.height = Math.round(
          current_char.height * this.paragraph.row_ratio
        );
      }
    } else if (isImage(current_char)) {
      const page_size = this.parent.editor.config.getPageSize();
      // 计算元素拉伸后宽高与原来的比例
      const ratio = current_char.width / current_char.height;
      if (current_char.height > page_size.height - 300 && current_char.width <= this.width) {
        current_char.height = page_size.height - 300;
        current_char.width = current_char.height * ratio;
      } else if (current_char.height > page_size.height - 300 && current_char.width > this.width) {
        current_char.height = page_size.height - 300;
        current_char.width = current_char.height * ratio;
      } else if (current_char.height < page_size.height - 300 && current_char.width > this.width) {
        current_char.width = this.width;
        current_char.height = current_char.width / ratio;
      }
      // 判断行高
      if (this.height < current_char.height) {
        // 调整行高
        this.height = current_char.height;
      }
    } else if (isWidget(current_char) || isLine(current_char) || isBox(current_char)) {
      // 判断行高
      if (this.height < current_char.height) {
        // 调整行高
        this.height = current_char.height;
      }
    } else if (isButton(current_char)) {
      // 判断行高
      if (this.height < current_char.height) {
        // 调整行高
        this.height = current_char.height * this.parent.editor.config.row_ratio;
      }
    }
  }

  /**
   * 当前行插入超出行宽的时候，判断末尾字符并处理返回内容
   * @param current_char 当前字符对象
   * @returns returnCharacter不能再行首和行尾的对象
   */
  adjustNoHeadTailChar(
    current_char: Character | Image | Widget | Line | Box | Button
  ): (Character | Image | Widget | Line | Box | Button)[] {
    let returnCharacter: (Character | Image | Widget | Line | Box | Button)[] = [];
    let isNumTypesetting: boolean = true; // 是否数字排版
    /**
     * 修改后的逻辑：
     * 》行尾是 行尾非法字符
     *  》判断前一个字符一直到 非行尾非法字符 =》返回所有的行尾非法字符
     * 》行尾是 行首非法字符 或者 正常字符
     *    》当前字符行首非法字符 =》 判断前一个字符一直到 不是 行首非法字符 =》再前一个字符不是行尾非法字符 =》返回
     *    》当前字符不是行首非法字符 =》 返回空
     */
    // 获取行尾字符
    const row_end_character = this.children[this.children.length - 1];
    if ((Config.not_at_row_tail as any).includes(row_end_character.value)) {
      for (let i = this.children.length - 1; i >= 0; i--) {
        const character = this.children[i];
        if ((Config.not_at_row_tail as any).includes(character.value)) {
          returnCharacter.unshift(character);
        } else {
          break;
        }
      }
    } else {
      // 是否行首字符排版
      let IsCommaTypesetting = this.paragraph.IsCommaTypesetting && (Config.not_at_row_head as any).includes(current_char.value);
      if (this.parent.editor.document_meta.fieldSymbolEndNoNewLine) {
        IsCommaTypesetting = this.paragraph.IsCommaTypesetting && (Config.not_at_row_head as any).includes(current_char.value) && current_char.field_position === "normal";
      }
      isNumTypesetting = this.paragraph.isNumTypesetting && this.adjustValueIsNum(current_char.value);
      // 数字排版和行首字符排版至少开一个
      if (IsCommaTypesetting || isNumTypesetting) {
        for (let i = this.children.length - 1; i >= 0; i--) {
          const character = this.children[i];
          // 如果开启某种排版规则，当前行最后一个字符一定是要拽下去的
          returnCharacter.unshift(character);
          // 第一种情况，行首字符排版开启，数字排版不开启
          // 判断当前字符（行内最后一个字符）不是行首非法字符
          let mergeCondition = IsCommaTypesetting && !this.paragraph.isNumTypesetting && !(Config.not_at_row_head as any).includes(character.value)
          if (this.parent.editor.document_meta.fieldSymbolEndNoNewLine) {
            mergeCondition = IsCommaTypesetting && !this.paragraph.isNumTypesetting && !(Config.not_at_row_head as any).includes(character.value) && character.field_position === "normal"
          }
          if (mergeCondition) {
            if (
              i > 0 &&
              !(Config.not_at_row_tail as any).includes(this.children[i - 1].value)
            ) {
              break;
            } else {
              continue;
            }
          }
          // 行首字符排版关闭 数字排版开启
          if (!this.paragraph.IsCommaTypesetting && isNumTypesetting && this.adjustValueIsNum(character.value)) {
            if (
              i > 0 &&
              !this.adjustValueIsNum(this.children[i - 1]?.value)
            ) {
              break;
            } else {
              continue;
            }
          }

          // 行首字符排版开启 数字排版开启

          // 条件1 当前字符是数字 并且前一个字符不是数字也不是行尾非法字符
          const condition1 = (this.adjustValueIsNum(character.value) &&
            !this.adjustValueIsNum(this.children[i - 1]?.value) &&
            !(Config.not_at_row_tail as any).includes(this.children[i - 1]?.value)) ||
            !this.adjustValueIsNum(character.value);
          // 条件2 循环当前字符不是行首非法字符，并且前一个字符不是行尾非法字符
          let condition2 = !((Config.not_at_row_head as any).includes(character.value)) &&
            !(Config.not_at_row_tail as any).includes(this.children[i - 1]?.value);
          if (this.parent.editor.document_meta.fieldSymbolEndNoNewLine) {
            condition2 = !((Config.not_at_row_head as any).includes(character.value) && character.field_position === "normal") &&
              !(Config.not_at_row_tail as any).includes(this.children[i - 1]?.value);
          }
          // 终止条件
          // const breakCondition = condition1 || condition2;

          if (i > 0 && !condition1) {
            continue;
          }

          if (i > 0 && condition2) {
            break;
          } else {
            continue;
          }
        }
      } else {
        // 数字排版和行首字符排版都不开启
        return [];
      }
    }
    if (this.children.length - returnCharacter.length <= 2) {
      returnCharacter = [];
    }
    return returnCharacter;
  }

  /**
   * 确定字符是数字或者字母
   * @param val 待判断字符对象
   * @returns true / false
   */
  adjustValueIsNum(val: any) {
    return val ? ((val.charCodeAt() <= 57 && val.charCodeAt() >= 48) || (val.charCodeAt() <= 90 && val.charCodeAt() >= 65) || (val.charCodeAt() <= 122 && val.charCodeAt() >= 97) || val === ".") : false;
  }

  /**
   * 重置行高
   */
  resetRowHight() {
    this.height = 0;
    const children = this.linebreak
      ? this.children.concat(this.linebreak)
      : this.children;
    for (let i = 0; i < children.length; i++) {
      const element = children[i];
      this.adjustHeight(element);
    }
  }

  /**
   * 上一行末尾字符移到下一行时 调整该行间距
   * @returns
   */
  adjustSpacing() {
    if (
      !this.children[0] ||
      !(
        this.width - this.children[this.children.length - 1].right >
        this.children[this.children.length - 1].width / 4
      )
    ) {
      return;
    }
    this.recoverySpacing();

    // 调整间距
    const variable = this.tail_width / this.children.length;
    for (let i = 1; i < this.children.length; i++) {
      const element = this.children[i] as Character;
      element.left += variable * i;
      element.draw_width = element.width + Math.ceil(this.tail_width / this.children.length);
    }
    (this.children[0] as Character).draw_width = this.children[0].width + Math.ceil(this.tail_width / this.children.length)
    ;
  }

  /**
   * 上一行行间距不满，添加字符时 调整间距至紧凑状态 恢复间距
   */
  recoverySpacing() {
    // 调整间距
    for (let i = 1; i < this.children.length; i++) {
      const element = this.children[i] as Character;
      element.left = this.children[i - 1].right;
    }
  }

  /**
   * 计算该行字符实际宽度和行宽的差值
   * @returns 返回当前字符的宽度和行剩余宽度
   */
  calculateRowAndCharWidth() {
    let chars_width = 0;
    let D_value = 0;
    this.children.forEach((e) => {
      chars_width += e.width;
    });
    // 所有字符的宽度
    chars_width += this.padding_left;
    // 行宽和字符宽度的差
    D_value = this.width - chars_width; // TODO 应该再减去 this.padding_right 吧 Row 上好像从来都没考虑过右边距
    return { D_value, chars_width };
  }

  /**
   * 判断具体位置是否在垂直行内
   * @param y 纵坐标
   * @returns true\false
   */
  contain_vertical(y: number) {
    if (this.parent.set_cell_height.type === "scroll") {
      y += this.parent.scroll_cell_top;
    }
    return this.top <= y && y <= this.bottom;
  }

  /**
   * 判断具体位置是否在水平行内
   * @param x 行坐标
   * @returns true\false
   */
  contain_horizontal(x: number) {
    return this.left <= x && x <= this.right;
  }

  /**
   * 目标位置是否在该行内
   * @param x x轴坐标
   * @param y y轴坐标
   * @returns true\flase
   */
  contain(x: number, y: number) {
    return this.contain_horizontal(x) && this.contain_vertical(y);
  }

  /**
   * 返回行内指定偏移量字符的x坐标
   * @param offset
   */
  x_by(offset: number) {
    let x = 0;
    const length = this.children.length;
    if (length > 0) {
      const child = this.children[offset < length ? offset : length - 1];

      x = child.left;

      if (offset >= length) {
        // 当偏移量指向末尾时，坐标要添加最后一个字符的宽度
        x += child.width;
      }
    }

    return x;
  }

  /**
   * 当前行中与x坐标最近的字符索引
   * @param x 文档中的x坐标
   */
  offset_near(x: number): number {
    if (this.children.length === 0) {
      return 0;
    }

    const xs = this.children.map((child) => child.left);

    // 增加一个末尾的数值

    const child = this.children[this.children.length - 1];

    xs.push(child.left + child.width);

    return nearest(xs, x);
  }

  /**
   * 当前行中与x坐标最近的字符索引
   * @param x 文档中的x坐标
   */
  arraw_offset_near(x: number): number {
    if (this.children.length === 0) {
      return 0;
    }
    // 单元格x轴坐标
    let offset_left = 0;

    // 如果是单元格内
    if (this.parent && this.parent.parent && this.parent.position[1] > 0) {
      offset_left = this.parent.left;
    }
    const xs = this.children.map((child) => child.left + offset_left);

    // 增加一个末尾的数值

    const child = this.children[this.children.length - 1];

    xs.push(child.left + child.width + offset_left);

    return nearest(xs, x);
  }

  /**
   * 查找行内最高元素
   * @returns 行内最高元素
   */
  getHighestCharacter() {
    let character: any;
    for (let i = 0; i < this.children.length; i++) {
      const element = this.children[i];
      if (!character) {
        character = element;
      }
      if (character && element.height > character.height) {
        character = element;
      }
    }
    if (this.children.length === 0) {
      character = this.linebreak;
    }

    return character;
  }

  /**
   * 查找行内最宽元素
   * @returns 行内最宽元素
   */
  getWidestCharacter() {
    let character: Character | Image | Widget | Line | Box | Button | null = null;
    for (let i = 0; i < this.children.length; i++) {
      const element = this.children[i];
      if (!character) {
        character = element;
      }
      if (character && element.width > character.width) {
        character = element;
      }
    }
    return character;
  }

  /**
   * 复制行内数据和行上的属性
   * @param parent 行的父级元素 单元格
   * @returns 复制后的新行
   */
  copy(parent: Cell = this.parent) {
    const row = new Row(parent, this.left, this.top, this.width);
    row.cell_index = this.cell_index;
    row.height = this.height;
    row.padding_left = this.padding_left;
    row.paragraph = this.paragraph;
    row.padding_vertical = this.padding_vertical;
    row.row_index_in_para = this.row_index_in_para;
    row.field_chars_in_row = this.field_chars_in_row;
    row.linebreak = this.linebreak;
    row.children = this.children;

    return row;
  }

  /**
   * 区分行内元素
   * @returns 区分结果
   */
  distinguishContent() {
    if (!this.children.length) return null;
    const distinguish_result: any = [];
    let str: string = ""; // 相同样式的文字
    let font: any = ""; // 当前字体参考样式，值为前一个字符的样式
    for (let i = 0; i < this.children.length; i++) {
      const char = this.children[i];
      if (isCharacter(char)) {
        if (char.font.id === font.id) {
          str += char.value; // 相同字号的拼接到一起
          // 当前字符是行内最后一个字符
          i === this.children.length - 1 && distinguish_result.push({ type: "string", content: str, font: char.font });
        } else {
          // str变量中有值，只需要赋值，不需要记录结果。
          str.length !== 0 && distinguish_result.push({ type: "string", content: str, font: char.font });
          font = char.font;
          str = char.value; // 不同字体的更新字符串
        }
      } else if (isWidget(char)) {
        // 复选框前面如果是文字，那么先存下来，并将变量清空
        str.length !== 0 && distinguish_result.push({ type: "string", content: str, font: font });
        str = "";
        // 结果中填入复选框
        distinguish_result.push({ type: "Widget", content: char });
      } else if (isImage(char)) {
        str.length !== 0 && distinguish_result.push({ type: "string", content: str, font: font });
        str = "";
        distinguish_result.push({ type: "image", content: char });
      } else if (isBox(char)) {
        str.length !== 0 && distinguish_result.push({ type: "string", content: str, font: font });
        str = "";
        distinguish_result.push({ type: "Box", content: char });
      }
    }
    return distinguish_result;
  }

  /**
   * 画行 渲染到页面上
   */
  draw() {
    Renderer.save();
    const editor = this.parent.editor;
    if (editor.print_mode) {
      if (editor.group_print&&!(this.group&&this.group.meta.print)) {
        Renderer.restore();
        return
      }
    }
    if (editor.config.rowLineType === RowLineType.SOLID) {
      if (this.parent.getLocation() === "root" && !this.parent.parent) {
        Renderer.draw_line([this.left, this.bottom], [this.right, this.bottom], "#000", 1, 0.5);

        if (!editor.config.rowLineTypeExplain.excludesEmpty) { // 有的医院可能不想要下半页的行线
          // 有半页的情况下没有 row 但是也有绘制完全
          let bottom = this.bottom;
          const rowHeight = editor.internal.rowHeight;
          if (this.cell_index === this.parent.children.length - 1 && rowHeight) {
            const lastPage = editor.pages[editor.pages.length - 1];
            const lastPageFooterTop = lastPage.footer.footer_outer_top;
            while (true) {
              bottom += rowHeight;
              if (Math.ceil(bottom) >= lastPageFooterTop) {
                break;
              }
              Renderer.draw_line([this.left, bottom], [this.right, bottom], "#000", 1, 0.5);
            }
          }
        }
      }
    }
    if (this.parent.set_cell_height.type === "scroll") {
      Renderer.translate(this.left, this.top - this.parent.scroll_cell_top);
      const scroll_top = this.top - this.parent.scroll_cell_top;
      const scroll_bottom = this.bottom - this.parent.scroll_cell_top;
      // 最后一行top滚动后大于cell高度时不绘制,第一行bottom滚动后小于0时不绘制
      if (scroll_top >= this.parent.height || scroll_bottom <= 0) {
        Renderer.restore();
        return;
        // 上方一行卡在cell最上部时进行裁剪
      } else if (scroll_top < 0 && scroll_bottom > 0) {
        Renderer.get().rect(0, this.parent.scroll_cell_top - this.top, this.width, scroll_bottom);
        Renderer.get().clip();
        // 下方一行卡在cell最下部时进行裁剪
      } else if (scroll_top < this.parent.height && scroll_bottom > this.parent.height) {
        Renderer.get().rect(0, 0, this.width, this.parent.height - scroll_top);
        Renderer.get().clip();
      }
    } else {
      Renderer.translate(this.left, this.top);
    }

    if (
      this.paragraph &&
      this.paragraph.islist &&
      this.row_index_in_para === 0
    ) {
      if (this.paragraph.isOrder) {
        this.drawOrderListSymbol();
      } else {
        this.drawListSymbol();
      }
    }

    for (let i = 0; i < this.children.length; i++) {
      const character = this.children[i];
      if (character.field_position === "normal" && character.width === 0) {
        continue
      }
      const nextChar = this.children[i + 1];
      if (editor.config.fieldShowMode === FieldShowMode.showBgColor) {
        Renderer.draw_field_bgColor(character, nextChar, this);
      }
      if (isCharacter(character)) {
        let isEndChar = false
        if (i === this.children.length - 1) {
          isEndChar = true
        }
        let field = null
        if (character.field_position !== "normal") {
          // 判断字符所在文本域展示格式是否为input格式如果是文本域边框不绘制
          if (character.field_position === "start" || character.field_position === "end") {
            if (character.field_id) {
              field = this.parent.getOrigin().getFieldById(character.field_id);
              if (field?.meta.changed) {
                const focusField = editor.selection.getFocusField()
                if (focusField === field) {
                  Renderer.drawArc(field.end_sym_char.right - 5, field.end_sym_char.bottom + 8, 2, 0, Math.PI * 2, "red", 1, false, "solid")
                }
              }
              if (character.field_position === "end" && field?.showPoint) {
                Renderer.drawArc(field.end_sym_char.right - 4, 6, 2, 0, Math.PI * 2, "blue", 1, false, "solid")
                Renderer.drawArc(field.end_sym_char.right - 4, this.height - 6, 2, 0, Math.PI * 2, "blue", 1, false, "solid")
              }

              if (editor.view_mode !== "view" && field?.type === "anchor") {
                if (character.field_position === "end") {
                  Renderer.draw_box(character.left, this.height - this.padding_vertical, "rgb(104,151,187)");
                } else {
                  continue;
                }
              }
              if (character.field_position === "end" && field?.name && editor.formula_mode && editor.internal.formula_name !== field?.name && this.parent.getLocation() === "root") {
                const editor_width = 7;
                Renderer.draw_field_editor(
                  character.right - editor_width,
                  character.top + character.height * 0.2 - editor_width,
                  editor_width,
                  editor_width
                );
              }
              if (field?.display_type === "input" || field?.display_type === "printInput") {
                continue;
              }
            }
          }
          if (editor.config.fieldShowMode !== FieldShowMode.normal && !editor.print_mode) {
            if (character.field_position === "placeholder") {
              if (character.height <= 0) {
                continue
              }
              // 此处可以控制背景文本的字体样式
              Renderer.draw_character(
                character,
                this.height,
                this.height,
                this.padding_vertical,
                this.parent.editor,
                isEndChar
              );
              continue;
            } else if (editor.config.show_field_symbol) { // 不是背景文本 又要展示边框
              if (editor.config.fieldShowMode === FieldShowMode.mergeSymbol) {
                // const editable = this.parent.getFieldById(character.field_id!)?.editable;
                // const originEditable = this.parent.origin?.getFieldById(character.field_id!)?.editable;
                // if (!editable && !originEditable) {
                //   continue;
                // }
                // 重叠边框进行合并，如果是结束和开始边框连续则不绘制
                if (character.value && character.field_position === "end" && nextChar && nextChar.field_position === "start" && nextChar.value) {
                  i++;
                  continue;
                }
                if (character.value) {
                  Renderer.draw_field_symbol(character, this, this.parent.editor);
                }
              }
              if (editor.config.fieldShowMode === FieldShowMode.shadowBorder) {
                if (character.value) {
                  Renderer.draw_field_symbol(character, this, this.parent.editor);
                }
              }
              continue;
            } else {
              continue;
            }
          }
          // 如果当前为简洁模式则不绘制文本域边框字符
          if ((!editor.config.show_field_symbol ||
            (editor.document_meta.keepSymbolWidthWhenPrint && editor.print_mode))) {
            continue;
          }
        }
        if (character.height <= 0) {
          continue
        }
        // 绘制上标
        if (character.font.script === 1) {
          let real_height = this.height - character.height / 2;
          const charHight = Math.round(character.height * this.paragraph.row_ratio);
          if (charHight !== real_height) {
            if (character.font.align === "top") {
              real_height = this.padding_vertical * 2 + character.height / 2;
            } else if (character.font.align === "middle") {
              real_height = this.height / 2 + this.padding_vertical;
            }
          }

          Renderer.draw_character(
            character,
            real_height,
            this.height,
            this.padding_vertical,
            this.parent.editor,
            isEndChar
          );
        } else {
          let real_height = this.height;
          const charHight = Math.round(character.height * this.paragraph.row_ratio);
          if (charHight !== real_height) {
            if (character.font.align === "top") {
              real_height = this.padding_vertical * 2 + character.height;
            } else if (character.font.align === "middle") {
              real_height = this.height / 2 + this.padding_vertical + character.height / 2;
            }
          }

          Renderer.draw_character(
            character,
            real_height,
            this.height,
            this.padding_vertical,
            this.parent.editor,
            isEndChar
          );
        }

      } else if (isImage(character)) {
        if (character.height <= 0) {
          continue
        }
        Renderer.save();
        const img_map = editor.imageMap.get();
        let real_height = this.height;
        const charHight = character.height + Config.img_margin * 2;
        if (charHight !== real_height) {
          if (character.font.align === "top") {
            real_height = character.height;
          } else if (character.font.align === "middle") {
            real_height = this.height / 2 + character.height / 2;
          }
        }
        Renderer.draw_image(
          img_map.get(character.src),
          character.left,
          real_height,
          character.width,
          character.height
        );
        if (character.show && !character.meta.non_editable) {
          character.draw(this.height);
        }
        if (editor.view_mode === "person") {

          if (character.font.strikethrough) {
            Renderer.draw_horizontal(
              character.height / 2,
              character.left,
              character.left + character.width,
              "red"
            );
          } else if (character.font.dblUnderLine) {
            Renderer.draw_horizontal(
              this.height,
              character.left,
              character.left + character.width,
              "rgb(24, 144, 255)"
            );
            Renderer.draw_horizontal(
              this.height - 2,
              character.left,
              character.left + character.width,
              "rgb(24, 144, 255)"
            );
          }
        }

        Renderer.restore();
      } else if (isWidget(character)) {
        if (character.height <= 0) {
          continue
        }
        let real_height = this.height - character.height - this.padding_vertical;
        if (character.font.align === "top") {
          real_height = this.padding_vertical;
        } else if (character.font.align === "middle") {
          real_height = this.height / 2 - character.height / 2;
        }
        // 绘制复选框
        if (character.widgetType === "checkbox") {
          Renderer.drawCheckBox(
            character,
            character.left,
            real_height,
            character.height,
            this.parent.editor.print_mode ? ((character.border === "dotted" || character.border === "dashed") ? "none" : character.border) : character.border,
            character.font.temp_valid_color || "#000",
            this.parent.editor
          );
          if (editor.view_mode === "person") {
            if (character.font.strikethrough) {
              Renderer.draw_horizontal(
                real_height + character.height / 2,
                character.left,
                character.left + character.width,
                "red"
              );
            } else if (character.font.dblUnderLine) {
              Renderer.draw_horizontal(
                this.height,
                character.left,
                character.left + character.width,
                "rgb(24, 144, 255)"
              );
              Renderer.draw_horizontal(
                this.height - 2,
                character.left,
                character.left + character.width,
                "rgb(24, 144, 255)"
              );
            }
          }
        } else if (character.widgetType === "radio") {
          // 绘制单选框
          Renderer.drawRadio(
            character,
            character.left,
            real_height,
            character.height,
            character.font.temp_valid_color || "#000",
          );
          if (editor.view_mode === "person") {
            if (character.font.strikethrough) {
              Renderer.draw_horizontal(
                real_height + character.height / 2,
                character.left,
                character.left + character.width,
                "red"
              );
            } else if (character.font.dblUnderLine) {
              Renderer.draw_horizontal(
                this.height,
                character.left,
                character.left + character.width,
                "rgb(24, 144, 255)"
              );
              Renderer.draw_horizontal(
                this.height - 2,
                character.left,
                character.left + character.width,
                "rgb(24, 144, 255)"
              );
            }
          }
        } else if (character.widgetType === "caliper") {
          Renderer.drawCaliper(
            character,
            character.left,
            (this.height - character.height) / 2
          );
        }
      } else if (isLine(character)) {
        if (character.height <= 0) {
          continue
        }
        let real_height = this.height;
        if (character.font.align === "top") {
          real_height = character.line_height;
        } else if (character.font.align === "bottom") {
          real_height = this.height * 2 - character.line_height * 2;
        }
        // 绘制水平线
        Renderer.draw_line([0, real_height / 2], [this.width, real_height / 2], character.color, 1, character.line_height, character.form);
      } else if (isBox(character)) {
        if (editor.config.show_field_symbol) {
          // 绘制BOX
          Renderer.draw_box(
            character.left,
            this.height - this.padding_vertical
          );
        }
      } else if (isButton(character)) {
        if (character.height <= 0) {
          continue
        }
        if (editor.print_mode) {
          continue;
        }

        let real_height = this.height;
        if (character.height + 6 !== this.height) {
          if (character.font.align === "top") {
            real_height = this.padding_vertical * 2 + character.height;
          } else if (character.font.align === "middle") {
            real_height = this.height / 2 + character.height / 2 + this.padding_vertical;
          }
        }

        Renderer.draw_button(
          character,
          real_height,
          this.padding_vertical
        );
      }
    }
    this.drawFieldShowType();

    Renderer.restore();
  }

  getcharacter(char: any, para: Paragraph) {
    const charIndex = para.characters.findIndex((e) => e === char);
    if (charIndex - 1 >= 0) {
      return para.characters[charIndex - 1];
    }
  }

  // 绘制field各类型的文本域边框
  drawFieldShowType() {
    for (const key in this.field_chars_in_row) {
      const chars = this.field_chars_in_row[key].meta;
      const type = this.field_chars_in_row[key].type;
      const first_char = chars[0];
      const last_char = chars[chars.length - 1];
      let start_x = first_char.left - 2;
      let end_x = last_char.right + 2;
      let hasStart = false;
      let hasEnd = false;
      if (first_char.field_position === "start") {
        start_x = first_char.left + first_char.width / 2;
        hasStart = true;
      }
      if (last_char.field_position === "end") {
        end_x = last_char.right - last_char.width / 2;
        hasEnd = true;
      }
      if (!hasStart) {
        start_x = 0;
      }
      if (!hasEnd) {
        end_x = this.width;
      }
      // 系统打印预览时不展示input边框
      if ((!this.parent.editor.print_mode && type === "input") || type === "printInput") {
        // 获取chars.height中具有最大高度的值
        const maxChar = chars.reduce((prev: Character, current: Character) => { return (prev.height > current.height) ? prev : current; }, 0);
        if (!maxChar) continue;
        const { y, height } = this.getFieldShowTypeParam(maxChar, type)
        Renderer.save();
        // 2和4表示绘制的矩形上下间距2px
        Renderer.draw_stroke_rect(start_x + 0.5, y, end_x - start_x, height, this.parent.editor.config.field_input_symbol_color);
        Renderer.restore();
      } else if (type === "line") {
        // 获取chars.height中具有最大高度的值
        const maxChar = this.children.reduce((prev: any, current: any) => { return (prev.height > current.height) ? prev : current; }, 0);
        if (!maxChar) continue;
        const { y } = this.getFieldShowTypeParam(maxChar, type)
        Renderer.save();
        Renderer.draw_line([start_x, y], [end_x, y], "black", 1);
        Renderer.restore();
      }
    }
    // 如果当前行为空行，并且上一行存在field_chars_in_row， 则直接绘制
    if (!this.children.length && !this.linebreak && isRow(this.pre_row)) {
      const preHasCharRow = this.getPreRowNotEmpty(this);
      if(!preHasCharRow) return;
      const char = preHasCharRow.children[preHasCharRow.children.length - 1];
      if (isCell(char) || !char || !char.field_id) {
        return
      }
      const field = this.parent.getOrigin().getFieldById(char.field_id)!;
      const maxChar = (preHasCharRow as Row).children.reduce((prev: any, current: any) => { return (prev.height > current.height) ? prev : current; }, 0);
      // 获取chars.height中具有最大高度的值
      const { y } = this.getFieldShowTypeParam(maxChar, field.display_type)
      if (field?.display_type === "line") {
        // 往前一直找到有字符的一行
        Renderer.save();
        Renderer.draw_line([0, y], [this.width, y], "black", 1);
        Renderer.restore();
      }
      if (field?.display_type === "input" || field?.display_type === "printInput") {
        const { y, height } = this.getFieldShowTypeParam(maxChar, field.display_type)
        Renderer.save();
        // 2和4表示绘制的矩形上下间距2px
        Renderer.draw_stroke_rect(0.5, y, this.width, height, this.parent.editor.config.field_input_symbol_color);
        Renderer.restore();
      }
    }
  }

  getFieldShowTypeParam(maxChar: any, type: string) {
    if (type === "line") {
      const y = this.height - (this.height - maxChar.height) / 2
      return { y, height: 0 }
    } else if (type === "input" || type === "printInput") {
      let height = 1.4 * maxChar.height + this.padding_vertical / 2 - 4;
      let y = this.height - height - 2.5;
      if (isImage(maxChar)) {
        height = maxChar.height - 3;
        y = this.height - height;
      }
      return { y, height }
    }
    return { y: 0, height: 0 }
  }

  getPreRowNotEmpty(row: Row): Row | Table | null {
    if(!row){
      return  null;
    }
    const preRow = row.pre_row;
    if (preRow?.children.length) {
      return preRow
    } else {
      return this.getPreRowNotEmpty(preRow as Row)
    }
  }

  /**
   * 画无序列表的图标
   */
  drawListSymbol() {
    let x = (this.paragraph.level - 1) * this.paragraph.listLevelDislocation.chinese_num_width;
    let y = 0;
    let highest_character = this.getHighestCharacter();
    if (!(isCharacter(highest_character))) {
      highest_character = this.paragraph.lastCharacter;
    }
    // 无序列表图标的高度
    y = this.height - this.padding_vertical - highest_character.font.height / 2;
    // 无序列表图标的半径
    const radius = highest_character.font.height * 3 / 5;
    if (this.paragraph.align === "right" || this.paragraph.align === "center") {
      // 实现有序列表序号位置准确，在居中展示以及居右展示
      x = this.children[0] ? this.children[0].left - this.padding_left : this.width / 2 - this.padding_left;
    }
    x += radius / 2;
    if (this.paragraph.title_length === 0) x += this.paragraph.content_padding_left;
    Renderer.drawListSymbol(x, y, radius, this.paragraph.level);
  }

  /**
   * 有序列表的图标 包括数字等
   */
  drawOrderListSymbol() {
    let x = (this.paragraph.level - 1) * this.paragraph.listLevelDislocation.chinese_num_width;
    if (this.paragraph.title_length === 0) x += this.paragraph.content_padding_left;
    if (this.paragraph.align === "center") {
      // 实现有序列表序号位置准确，在居中展示
      x = this.children[0] ? this.children[0].left - this.padding_left : this.width / 2 - this.padding_left;
    } else if (this.paragraph.align === "right") {
      x = this.children[0] ? this.children[0].left - this.padding_left : this.width - this.padding_left;
    }
    const y = this.height - this.padding_vertical;
    let highest_character = this.getHighestCharacter();
    if (!(isCharacter(highest_character))) {
      highest_character = this.paragraph.lastCharacter;
    }
    // 设置有序列表的序号
    let secondSymbol: string | number = "";
    const type = this.paragraph.level % 2; // 级别
    if (type === 0) {
      let sign = this.paragraph.list_index % 26; // 字符ascll
      const times = this.paragraph.list_index / 26; // 倍数 代表几个字符
      sign = times > 0 && sign === 0 ? 26 : sign;
      for (let j = 0; j < times; j++) {
        secondSymbol = secondSymbol + String.fromCharCode(sign + 96);
      }
    } else {
      secondSymbol =
        this.paragraph.listNumStyle === "chinese"
          ? numberToChinese(this.paragraph.list_index)
          : this.paragraph.list_index + "";
    }
    const fontStyleOfSymbol = this.parent.editor.config.default_font_style;
    fontStyleOfSymbol.height = highest_character ? highest_character.height : fontStyleOfSymbol.height;
    const font = this.parent.editor.fontMap.add(fontStyleOfSymbol);
    if (this.paragraph.listNumStyle === "chinese") {
      secondSymbol = secondSymbol + "、";
    } else {
      secondSymbol = secondSymbol + ".";
    }
    Renderer.draw_text(font, secondSymbol, x, y, this.parent.editor);
  }

  getAllImages() {
    const imageArr: any = [];
    this.children.forEach((child) => {
      if (isImage(child)) {
        imageArr.push(child);
      }
    });
    return imageArr;
  }
}
