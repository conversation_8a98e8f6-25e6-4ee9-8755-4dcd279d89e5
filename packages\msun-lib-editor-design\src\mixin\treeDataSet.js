import dataDefi from "../../dataDefin.json";
import tableFillExample from "../../tableFillExample.json";
const dataSetMixIn = {
  data() {
    return {
      showDataSetModal: false,
      isShowRealValue: false, // 显示真实值
      isInitExtendBtn: false,
      isPrintDesign: false, // 是打印平台文档设计器
      dataSetInfo: {
        title: "数据集",
        optionTitle: "字段",
        optionTip: `字段前的图标代表该字段值对应的类型,可通过拖拽或者点击插入文本域,字段会自动与文本域绑定并自动设置文本域类型`,
        dataSets: [],
      },
      dictSetInfo: {
        title: "字典集",
        optionTitle: "选项",
        optionTip:
          "选项前图标绿色代表与字段存在关联关系，可通过拖拽直接插入一组选项，如果排版不符合要求则展开后一项一项插入;黑色图标则需要将字典选项拖拽到字段文本域中才能自动绑定字段并转换为相对应的选择项。",
        dataSets: [],
      },
      descDataInfo: {},
      oriInfo: {},
      oriDataSet: {},
      allDataSetTree: {},
      allDictSetTree: {},
      sameCodeChildrenMap: {},
      allDataItemCodeMap: {},
      initRightTreeData: [],
      sysInfo: {
        userSysId: "",
        userId: "001",
        userName: "众阳健康管理员",
        deptId: "",
        deptName: "",
        deptCode: "",
        hospitalName: "众阳健康测试医院",
        hospitalId: "10000",
        systemName: "打印平台",
        host: "",
      },
      sysInfoDesc: {
        userSysId: "用户系统ID",
        userId: "用户ID",
        userName: "用户名称",
        deptId: "科室ID",
        deptName: "科室名称",
        deptCode: "科室编码",
        hospitalId: "医院ID",
        hospitalName: "医院名称",
        systemName: "系统名称",
        host: "客户端IP",
      },
    };
  },
  created() {
    if (this.rightMenuConfig.treeData) {
      this.dictSetInfo.optionTip = "";
      this.dictSetInfo.optionTitle = "文本域";
      this.initRightTreeData = this.rightMenuConfig.treeData;
    }
  },
  mounted() {
    if (this.instance && this.instance.localTest.useLocal) {
      this.initDataSet(dataDefi);
    }
    // this.rightMenuConfig["draggable"] = true;
    // this.rightMenuConfig["treeData"] = initTreeData;
  },
  methods: {
    openManageDataSetModal() {
      this.showDataSetModal = true;
    },
    updateDataSet(newDataSet) {
      if (this.oriInfo) {
        this.oriInfo.data = newDataSet;
      }
      this.initDataSet(this.oriInfo);
      setTimeout(() => {
        this.instance.editor.updateCanvasSize();
      }, 100);
      this.closeDataSetModal();
    },
    closeDataSetModal() {
      this.showDataSetModal = false;
    },
    extendBaseBtnUsedByDataSet() {
      if (this.isInitExtendBtn) {
        return;
      }
      this.isInitExtendBtn = true;
      // 存在诸多问题，先注释
      // this.baseStartBtn.push(
      //   { type: "line" },
      //   {
      //     type: "icon",
      //     icon: "icon-huyanmoshi",
      //     title: "显示文本域真实值",
      //     key: "showRealVal",
      //     func: this.showRealValue,
      //   }
      // );
    },
    showRealValue() {
      this.isShowRealValue = !this.isShowRealValue;
      this.customSelected["showRealVal"] = this.isShowRealValue;
      this.$forceUpdate();
      const { editor } = this.instance;
      if (this.isShowRealValue) {
        this.fillContentByReceiveJsonData(editor);
      } else {
        this.clearFilledContent(editor);
      }
    },
    determineType(value) {
      if (value !== null && typeof value === "object") {
        value = value.value;
      }
      if (value !== null) {
        // 尝试将字符串解析为数字
        if (
          String(value).indexOf(".") > -1 &&
          /^[0-9]+(\.[0-9]+)?$/.test(value)
        ) {
          return "number";
        }
        // 尝试将字符串解析为日期
        const parsedDate = new Date(value);
        if (!isNaN(parsedDate) && parsedDate.toString() !== "Invalid Date") {
          return "date";
        }
      }
      // 默认为字符串
      return "string";
    },
    initDataSet(dataSets) {
      this.oriInfo = dataSets;
      if (!dataSets) {
        return;
      }
      const editorLocalDataSet = localStorage.getItem("EditorLocalDataSet");
      if (editorLocalDataSet) {
        dataSets.data = Object.assign(
          {},
          dataSets.data,
          JSON.parse(editorLocalDataSet)
        );
      }
      this.isPrintDesign = true;
      this.dataSetInfo.dataSets = [];
      this.dictSetInfo.dataSets = [];
      dataSets.data["系统变量"] = this.sysInfo;
      dataSets.data["系统变量-desc"] = this.sysInfoDesc;
      this.handleInnerVar(dataSets);
      this.oriDataSet = dataSets.data;
      this.dataColumnType = dataSets.dataColumnType;
      if (dataSets.data && Object.keys(dataSets.data).length) {
        this.extendBaseBtnUsedByDataSet();
      }
      const data = Object.assign({}, this.oriDataSet, this.dataColumnType);
      let initDataSetId = "";
      let initDictSetId = "";
      for (const key in data) {
        // 处理字段说明数据集
        if (key.endsWith("-desc")) {
          this.handleDescSets(key);
        }
      }
      for (const key in data) {
        if (!key.endsWith("-dict") && !key.endsWith("-desc")) {
          this.dataSetInfo.dataSets.push({
            name: key,
            value: key,
          });
          if (!initDataSetId) {
            initDataSetId = key;
          }
          this.handleDesignFields(key);
        }
      }
      for (const key in data) {
        if (key.endsWith("-dict")) {
          if (!initDictSetId) {
            initDictSetId = key;
          }
          this.dictSetInfo.dataSets.push({
            name: key,
            value: key,
          });
          this.handleDictSets(key);
        }
        // 处理字段说明数据集
        if (key.endsWith("-desc")) {
          this.handleDescSets(key);
        }
      }
      this.handleDataSetChildren();
      this.leftPanelConfig.treeData = this.allDataSetTree[initDataSetId];
      if (initDictSetId) {
        this.rightMenuConfig.treeData = this.allDictSetTree[initDictSetId];
      } else {
        this.rightMenuConfig.treeData = this.initRightTreeData;
      }
    },
    handleInnerVar(dataSets) {
      const sysVar = this.instance.sysVariables;
      const editorInnerVar = {};
      const editorInnerVarDesc = {};
      for (const key in sysVar) {
        if (key === "page_number") {
          editorInnerVar[sysVar[key]] = 0;
          editorInnerVarDesc[sysVar[key]] = "页";
        }
        if (key === "page_count") {
          editorInnerVar[sysVar[key]] = 0;
          editorInnerVarDesc[sysVar[key]] = "总页数";
        }
        if (key === "serial_number") {
          editorInnerVar[sysVar[key]] = 1;
          editorInnerVarDesc[sysVar[key]] = "序号";
        }
        if (key === "system_time") {
          editorInnerVar[sysVar[key]] = "2024-03-15";
          editorInnerVarDesc[sysVar[key]] = "系统时间";
        }
      }
      // 处理内置变量
      dataSets.data["内置变量"] = editorInnerVar;
      dataSets.data["内置变量-desc"] = editorInnerVarDesc;
    },
    handleDataSetChildren() {
      const dataSet = this.allDataSetTree;
      for (const key in dataSet) {
        const list = dataSet[key];
        for (let i = 0, len = list.length; i < len; i++) {
          const item = list[i];
          const children = this.sameCodeChildrenMap[item.code];
          if (children) {
            item.children = JSON.parse(JSON.stringify(children));
          }
        }
      }
    },
    handleDictSets(dataSetId) {
      const data = this.oriDataSet[dataSetId];
      const treeData = [];
      if (data) {
        this.handleDictTreeData(dataSetId, treeData, data);
        this.allDictSetTree[dataSetId] = treeData;
        this.$forceUpdate();
      }
    },
    // 处理描述字段
    handleDescSets(dataSetId) {
      let data = this.oriDataSet[dataSetId];
      if (data) {
        dataSetId = dataSetId.replace("-desc", "");
        if (Array.isArray(data)) {
          data = data[0];
        }
        for (const key in data) {
          this.descDataInfo[dataSetId + ">" + key] = data[key];
        }
      }
    },
    groupBy(array, key) {
      return array.reduce((result, item) => {
        (result[item[key]] = result[item[key]] || []).push(item);
        return result;
      }, {});
    },
    handleDictTreeData(dataSetId, treeData, data) {
      const dictObj = this.groupBy(data, "name");
      dataSetId = dataSetId.replace("-dict", "");
      for (const key in dictObj) {
        const code = dataSetId + ">" + key;
        const children = dictObj[key].map((ele) => {
          return {
            title: ele.label + "-" + ele.value,
            code: code + ">" + ele.value,
            optionType: ele.optionType,
            value: ele.value,
            pCode: code,
          };
        });
        let vsField = false;
        if (this.allDataItemCodeMap[code]) {
          this.sameCodeChildrenMap[code] = children;
          vsField = true;
        }
        const node = {
          title: key,
          code,
          vsField,
          pCode: dataSetId,
          slots: { icon: vsField ? "dict1" : "dict0" },
          children,
        };
        treeData.push(node);
      }
      this.allDictSetTree[dataSetId] = treeData;
    },
    judgeDataSetIsEmpty(dataSet) {
      if (!dataSet) return false;
      if (Array.isArray(dataSet)) {
        return !dataSet.length;
      } else if (typeof dataSet === "object") {
        return !Object.keys(dataSet).length;
      }
      return false;
    },
    handleDesignFields(dataSetId) {
      let data;
      if (
        this.oriDataSet &&
        !this.judgeDataSetIsEmpty(this.oriDataSet[dataSetId])
      ) {
        data = this.oriDataSet[dataSetId];
      } else if (this.dataColumnType && this.dataColumnType[dataSetId]) {
        data = this.dataColumnType[dataSetId];
      }
      const treeData = [];
      if (data) {
        this.handleTreeData(dataSetId, treeData, data);
        this.allDataSetTree[dataSetId] = treeData;
        this.$forceUpdate();
      }
    },
    handleChildTreeData(parentCode, pNode, item) {
      for (let i = 0; i < item.length; i++) {
        const ele = item[i];
        const node = {
          title: ele.name,
          code: parentCode + ">" + i,
          value: ele.value,
          pCode: parentCode,
        };
        pNode.push(node);
      }
    },
    handleTreeData(parentCode, pNode, item) {
      if (Array.isArray(item)) {
        item = item[0];
      }
      for (const key in item) {
        const fieldType = this.determineType(item[key]);
        const code = parentCode + ">" + key;
        const desc = this.descDataInfo[code] ? this.descDataInfo[code] : "";
        const node = {
          title: desc ? desc + "(" + key + ")" : key,
          code,
          desc,
          pCode: parentCode,
          fieldType,
          slots: { icon: fieldType },
          children: [],
        };
        this.allDataItemCodeMap[node.code] = true;
        pNode.push(node);
        if (!item[key]) {
          continue;
        }
        if (typeof item[key] === "object") {
          if (Array.isArray(item[key].children)) {
            this.handleChildTreeData(
              node.code,
              node.children,
              item[key].children
            );
          } else {
            this.handleTreeData(node.code, node.children, item[key]);
          }
        } else {
          node["value"] = item[key];
        }
      }
    },
    dragDrop() {
      if (!this.dragInfo) {
        return;
      }
      const { data, key, arr } = this.dragInfo;
      this.clickTreeMenu(data, key, arr);
      this.dragInfo = null;
    },
    treeMenuDragstart(data, key, arr) {
      this.dragInfo = {
        data,
        key,
        arr,
      };
    },
    clickTreeMenu(data, key, arr) {
      const code = data.code ? data.code : data.title ? data.title : "";
      const { editor } = this.instance;
      let insertedField = editor.insertField({
        type: "normal",
        placeholder: data.title,
        name: code,
      });
      const focusField = editor.selection.getFocusField();
      if (
        focusField &&
        focusField.type === "box" &&
        data.vsField === undefined
      ) {
        // 如果字段拖拽位置是box，则将替换box关联字段
        focusField.parent_box.name = code;
        this.$editor.info(`已替换关联字段为【${code}】`);
      }
      if (typeof insertedField === "object") {
        let pData = null;
        if (arr) {
          pData = arr[arr.length - 2];
          editor.focus();
        }
        this.instance.showFloatBtn(insertedField, data, pData);
      }
    },
    test() {
      const editor = this.instance.editor;
      function callback() {
        return new Promise((resolve) => {
          resolve([]);
        });
      }
      editor.handleSourceListBySourceId(callback);

      // const cells = editor.getGroupCells();
      // console.log(cells, "所有的单元格");
      // // 校验 ↓
      // editor.validFields();
      // 校验 ↑

      // const field = editor.getFieldById("t1");
      // const data = field.getDescription();
      // // const target = editor.getFieldById("t2");
      // // target.recoveryByDescription(data);
      // console.log(data, "描述信息");
      // editor.insertImage(
      //   "https://gips0.baidu.com/it/u=3808212212,1411671053&fm=3042&app=3042&f=JPEG&wm=1,huayi,0,0,13,9&wmo=0,0&w=480&h=640",
      //   { meta: { imageType: 1 } }
      // );
      // this.instance.openCommentList({
      //   hideDeleteOperationBtn: true,
      //   hideReplaceOperationBtn: true,
      //   hideDate: true,
      //   useNewTitle: "数据来源",
      // });
      // editor.insertWarterMarkBy({
      //   imageSrc:
      //     "http://gips2.baidu.com/it/u=3944689179,983354166&fm=3028&app=3028&f=JPEG&fmt=auto?w=1024&h=1024",
      //   fieldId: "warterMark",
      // });
      // const text = editor.getBodyText();
      // console.log(text, "获取到的body text");

      // const tables = editor.root_cell.paragraph.map(t => t?.col_size?.length && t).filter(Boolean);
      // console.log(editor.checkTablesIsRegular(tables));
      // 先删除原插入的文本域，然后重新插入一个
      // let field = editor.insertField();
      // // field.maxHeight = 50;
      // if (field) {
      //   field.setNewText("1.111111111111111111111111");
      //   editor.updateFieldText({ fields: [field], append: true });
      //   editor.insertText("\n");
      //   editor.refreshDocument();
      //   field.setNewText("2.222222222222222222222222");
      //   editor.updateFieldText({ fields: [field], append: true });
      //   this.$editor.info(field.text);
      // }
      // editor.updateCellsAttr({
      //   cells: tables[0].children,
      //   attr: { lock: true },
      // });
      // this.instance.editor.fillContentByJson(null, tableFillExample);
    },
    fillContentByReceiveJsonData(editor) {
      // editor.fillContentByJson(null, tableFillExample);
      editor.fillContentByJson(null, this.oriDataSet, "dataSet");
    },
    clearFilledContent(editor) {
      editor.fillContentByJson(null, this.oriDataSet, "dataSet", true);
    },
    handleDataSetSelect(value) {
      this.leftPanelConfig.treeData = this.allDataSetTree[value];
      this.$forceUpdate();
    },
    handleDictSetSelect(value) {
      this.rightMenuConfig.treeData = this.allDictSetTree[value];
      this.$forceUpdate();
    },
    getRealValueByFileName(name) {
      const nameArr = name.split(">");
      let data = this.oriDataSet;
      for (let i = 0; i < nameArr.length; i++) {
        const key = nameArr[i];
        data = data[key];
        if (Array.isArray(data)) {
          data = data[0];
        }
        if (!data) return;
      }
      if (Array.isArray(data.children)) {
        let vals = data.value;
        if (typeof data.value === "string") {
          vals = data.value.split(",");
        }
        if (Array.isArray(vals)) {
          vals = vals.map((item) => String(item));
          const selItem = data.children.find((item) =>
            vals.includes(String(item.value))
          );
          if (selItem) {
            data.value = selItem.name;
          }
        }
      }
      return data;
    },
    searchFieldByKeyText(text, resolve) {
      const resList = [];
      const allData = this.allDataSetTree;
      for (const dataSetId in allData) {
        const array = allData[dataSetId];
        for (let i = 0, len = array.length; i < len; i++) {
          const item = array[i];
          if (item.title.toLowerCase().indexOf(text.toLowerCase()) > -1) {
            resList.push({
              name: item.pCode + ">" + item.title,
              node: item,
              type: "field",
            });
          }
        }
      }
      resolve(resList);
    },
    insertFieldByQuickInputSelect(item) {
      this.clickTreeMenu(item.node);
    },
    saveLocalData() {
      if (!this.instance) return;
      const editor = this.instance.editor;
      let counter =
        parseInt(localStorage.getItem("designEditorSaveCounter")) || 1;
      // 创建一个键名
      let key = `designEditorSaveData${counter}`;
      editor.config.getDataType = 1;
      // 写入数据到localStorage
      localStorage.setItem(key, editor.getRawData());
      editor.config.getDataType = 0;
      this.$editor.success(`已暂存至本地${counter}`);
      // 更新计数器，确保循环在1到10之间
      counter = (counter % 10) + 1;
      // 将计数器存储到localStorage
      localStorage.setItem("designEditorSaveCounter", String(counter));
    },
  },
};
export default dataSetMixIn;
