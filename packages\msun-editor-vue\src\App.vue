<!-- eslint-disable vue/valid-v-model -->
<template>
  <a-config-provider :locale="locale">
    <div
      ref="editor_vue"
      id="editor-vue-x"
      :style="{ position: isShowCommentList ? 'relative' : 'static' }"
    >
      <monitor
        v-if="isDebugMode"
        :visible="isDebugMode"
        :editorMonitor="editorMonitor"
      ></monitor>
      <template v-if="showFloatMenu">
        <template v-for="(num, i) in 2">
          <floatingMenu
            :key="'floatingMenu' + num"
            v-if="floatingMenuCount > i"
            :menuCount="floatingMenuCount"
            :init-info="initFloatMenuInfo"
            :floatMenuList="floatMenuList"
            @btnClick="exeFloatMenuCommand"
            @startDrag="floatMenuStartDrag"
          ></floatingMenu>
        </template>
      </template>
      <shapeFloating
        v-show="showShapeFloating"
        ref="shapeFloat"
        :init-info="initShapeFloat"
        @shapeMode="shapeMode"
      >
      </shapeFloating>
      <waterMarkFloating
        v-show="showWaterMarkFloating"
        ref="waterMarkFloat"
        :init-info="initWaterMarkFloat"
        @waterMarkImage="insertWaterMarkImage"
        @customWaterMarkImage="insertCustomWaterMarkImage()"
        @waterMarkModel="waterMarkModel"
      ></waterMarkFloating>
      <div
        :id="editorId"
        ref="content"
        @scroll="handleScroll"
        class="editor-content-x"
        @contextmenu.prevent="rightClick($event)"
      ></div>
      <!--右键菜单-->
      <right-click-menu
        :config="currentRightMenuConfig"
        :rightMenuEvent="rightMenuEvent"
        :data="listOfFinalShow"
        :editorId="editorId"
        v-show="isShowRightClickMenu"
        @cancelRightMenu="cancelRightMenu"
        @handleRightClickMenuSelected="handleRightClickMenuSelected"
        ref="rightMenu"
      />
      <!--痕迹对比弹框-->
      <contrast
        :style="{
          left: fieldTipStyle.left + 'px',
          top: fieldTipStyle.top + 'px',
          position: 'absolute',
          backgroundColor: '#e6f7ff',
        }"
        v-show="show_contrast_tip"
        :tip_content="contrast_content"
      ></contrast>
      <system-print
        ref="system_print"
        :editorId="editorId"
        :show="showSystemPrint"
        :print-config="localConfig?.print"
        :printerList="printerList"
        :localConfigOther="localConfig?.other"
        @updatePrinterList="updatePrinterList"
        @submit="previewSystemPrint"
        @cancel="cancelPreviewSystemPrint"
        @savePrintConfig="savePrintConfig"
        @savePrintPreviewWindowSizeType="savePrintPreviewWindowSizeType"
      ></system-print>
      <immediately-print-config
        ref="immediatelyPrintConfig"
        :show="showImmediatelyPrintConfig"
        @submit="immediatePrintSetting"
        @cancel="closeImmediatePrintPageSettingModal"
      ></immediately-print-config>
      <field-date-picker
        ref="field_date_picker"
        :editorId="editorId"
        :show="showFieldDatePicker"
        @submit="replaceFieldText"
        :style="{
          position: 'absolute',
          left: fieldPanelOffset.left + 'px',
          top: fieldPanelOffset.top + 'px',
        }"
      />
      <van-popup
        v-if="isMobile1"
        v-model="mobileDatePickerShow"
        position="bottom"
        :style="{ height: '40%' }"
      >
        <van-datetime-picker
          v-model="currentDate"
          type="datetime"
          @confirm="mobileDatePickerConfirm"
          @cancel="mobileDatePickerCancel"
        />
      </van-popup>
      <field-select
        ref="field_select"
        :editorId="editorId"
        :show="showFieldSelectPanel"
        @select="replaceFieldText"
        :position="fieldPanelOffset"
        :separatorGroups="separatorGroups"
      />
      <field-number-select
        ref="field_number_select"
        :position="fieldPanelOffset"
        :visible="isShowNumberSelect"
        @select="replaceNumberFieldText"
      ></field-number-select>
      <field-search-box
        ref="fieldSearchBox"
        :visible="showFieldSearchBox"
        :position="fieldPanelOffset"
        :quickSelectIndex="quickSelectIndex"
        @select="replaceFieldText"
      ></field-search-box>
      <fieldProperty
        ref="fieldProperty"
        :editorId="editorId"
        :show="showFieldPropModalWin"
        @submit="changeFieldProps"
        @cancel="cancelFieldProps"
        :separatorGroups="separatorGroups"
        :replaceFormatList="replaceFormatList"
        @changeStayTop="changeStayTop"
        @openFieldAdvanced="openFieldAdvanced"
        :stayTop="stayTop"
        :curFieldID="curFieldID"
        :isInsert="isInsert"
        :reg="reg"
      />
      <fieldAutomation
        ref="fieldAuto"
        :show="showFieldAutomation"
        :sessionMove="true"
        @submit="fieldAutomationSubmit"
        @cancel="fieldAutomationCancel"
      ></fieldAutomation>
      <field-cascade
        ref="cascade"
        :show="showCascadeOption"
        :width="modal_width"
        :sessionMove="true"
        @submit="cascadeSubmit"
        @cancel="cascadeCancel"
      ></field-cascade>
      <ruler-editor
        ref="rulerEditor"
        :show="showRulerEditorModal"
        @submit="rulerEditorSubmit"
        @test="testRulerEditor"
        @cancel="rulerEditorCancel"
        :replaceRule="replaceRule"
        :sessionMove="true"
        :formattedText="formattedText"
      ></ruler-editor>
      <field-formula
        :show="showFieldFormula"
        ref="fieldFormulaEdit"
        @cancel="closeFieldFormula"
        @submit="submitFieldFormula"
        @changeStayTop="changeStayTop"
        :stayTop="stayTop"
      ></field-formula>
      <addChioceField
        ref="choiceField"
        :editorId="editorId"
        :isShowChoiceModal="isShowChoiceModal"
        @openFieldAdvanced="openFieldAdvanced"
        @submit="insertChioceModal"
        @cancel="closeChioceModal"
      ></addChioceField>
      <medicalCalcFormula
        ref="medicalCalcFormula"
        :editorId="editorId"
        :show="showMedicalCalcFormula"
        @submit="medicalCalcFormulaSubmit"
        @cancel="medicalCalcFormulaCancel"
      />
      <input-select
        :id="'inputSelect' + editorId"
        v-show="showInputSelect"
        :showInputSelect="showInputSelect"
        :index="choiceIndex"
        :selectComSentencesList="selectComSentencesList"
        :selectText="selectText"
        :editorId="editorId"
        @changeSelectIndex="changeSelectIndex"
        ref="inputSelect"
        :style="{
          position: 'absolute',
          left: inputLocation.left + 'px',
          top: inputLocation.top + 'px',
        }"
      ></input-select>
      <!--查找替换-->
      <search-and-replace
        ref="search_and_replace"
        :editorId="editorId"
        :show="showSearchAndReplace"
        :resultData="resultData"
        :search="search"
        :showTipModal="showTipModal"
        :showCurrentGroupReplaceAll="showCurrentGroupReplaceAll"
        @closeTip="closeTipModal"
        @changeCaseSensitive="changeCaseSensitive"
        @replaceOne="replaceOne"
        @replaceAll="replaceAll"
        @currentGroupReplaceAll="currentGroupReplaceAll"
        @searchAll="searchAll"
        @searchNextOne="searchNextOne"
        @cancel="searchAndReplaceCancel"
      ></search-and-replace>

      <imageEditing
        :show="showImageEditing"
        ref="image_editing"
        :editorId="editorId"
        :imageMeta="imageMeta"
        :fontSizeList="fontSizeList"
        :fontTypeList="fontTypeList"
        @submit="confirmImageEditingModal"
        @cancel="closeImageEditingModal"
      ></imageEditing>
      <chartSetting
        :show="showChartSetting"
        :chartType="chartType"
        :xAxisData="xAxisData"
        :yAxisData="yAxisData"
        :chartKeys="chartKeys"
        @updateChartType="onUpdateChartType"
        @submit="confirmChartEditingModal"
        @cancel="closeChartEditingModal"
      ></chartSetting>
      <ButtonPopup
        ref="buttonPopup"
        :show="showButton"
        :editorId="editorId"
        @submit="buttonSubmit"
        @cancel="closeButton"
      >
      </ButtonPopup>
      <!--医学表达式列表-->
      <medical-formula-list
        ref="formulaList"
        :editorId="editorId"
        :show="showMedicalFormulaList"
        :src="formulaSrcList"
        @submit="choiceFormula"
        @cancel="cancelFormula"
      />
      <!--医学表达式-->
      <medical-formula
        ref="formulaImage"
        :editorId="editorId"
        :show="showMedicalFormula"
        :type="formulaType"
        :meta="meta"
        @submit="insertFormulaImage"
        @cancel="cancelFormulaImage"
      />
      <waterMark
        :show="showWaterMark"
        @submit="waterMarkSubmit"
        @cancel="closeWaterMark"
      ></waterMark>
      <customWaterMark
        ref="customWaterMark"
        :show="showCustomWaterMark"
        @submit="customWaterMarkSubmit"
        @cancel="closeCustomWaterMark"
      ></customWaterMark>
      <modal
        title="插入表格"
        :show="isShowModal"
        @cancel="closeInsertTblModal"
        @submit="confirmInsertTbl"
        :width="modal_width"
      >
        请输入行：
        <a-input-number :precision="0" v-model="row_num" :min="1" />
        <br />
        <div style="height: 10px"></div>
        请输入列：
        <a-input-number :precision="0" v-model="col_num" :min="1" />
        <div>
          <label>开启分页：</label>
          <a-switch v-model="insertTableWithNewPage" />
        </div>
      </modal>
      <modal
        title="卡尺"
        :show="isShowCaliper"
        @cancel="closeCaliper"
        @submit="confirmCaliper"
        :width="modal_width"
      >
        <div
          style="
            display: flex;
            justify-content: space-around;
            line-height: 32px;
          "
        >
          卡尺高度：
          <a-input-number :precision="0" v-model="caliper_height" :min="10" />
          <br />
          <div style="height: 10px"></div>
          卡尺最大值：
          <a-input-number :precision="0" v-model="caliper_num" :min="2" />
          <br />
        </div>
        <div style="display: flex; line-height: 32px; margin: 10px 0 0 12px">
          <div style="">卡尺间距：</div>
          <a-input-number
            :precision="0"
            v-model="caliper_sp"
            :min="5"
            style="margin-left: 23px"
          />
        </div>
      </modal>
      <modal
        title="拆分单个单元格"
        :show="isShowSplitCellModal"
        @cancel="closeSplitCellModal"
        @submit="confirmSplitCellModal"
        :width="modal_width"
      >
        请输入列：
        <a-input-number :precision="0" v-model="split_col_num" :min="1" />
        <br />
        <div style="height: 10px"></div>
        请输入行：
        <a-input-number :precision="0" v-model="split_row_num" :min="1" />
      </modal>

      <!-- 表格属性 -->
      <modal
        title="表格属性"
        :show="isShowTableAttr"
        @cancel="closeTableAttr"
        @submit="confirmTableAttr"
        :width="modal_width"
      >
        修改表格name值为：
        <a-input
          placeholder="请输入name值"
          style="width: 130px"
          v-model="tableName"
        />
        设置最小行高：
        <a-input-number id="inputNumber" v-model="minRowSize" />
        <br />
        <div class="editorTablePropChange">
          <label>开启分页：</label>
          <a-switch v-model="tableNewPage" />
          <label class="editorOpenTABSkipMode">开启TAB键列跳转：</label>
          <a-switch v-model="tableSkipMode" />
          <label class="editorOpenTABSkipMode">表格线撑满一页：</label>
          <a-switch v-model="fullPage" />
        </div>
        <div class="editorTablePropChange">
          <label>网格线：</label>
          <a-select
            ref="select"
            v-model="tableRowLineType"
            style="width: 80px"
            dropdownClassName="xeditor-input-up"
          >
            <a-select-option value="tableRowLineTypeNone"
              >不展示</a-select-option
            >
            <a-select-option value="tableRowLineTypeSolid"
              >实线</a-select-option
            >
          </a-select>
        </div>
        <a-checkbox v-model="editableInFormMode" class="editorTablePropChange"
          >是否允许表单模式下可编辑</a-checkbox
        >
        <a-checkbox v-model="tableFiexedStyle" class="editorTablePropChange"
          >是否固定样式</a-checkbox
        >
      </modal>
      <!-- 分数 -->
      <modal
        title="分数"
        :show="isShowFraction"
        @cancel="closeFraction"
        @submit="confirmFraction"
        :width="modal_width"
      >
        整数：
        <a-input
          placeholder="请输入整数"
          style="width: 130px"
          v-model="fractionInt"
        />
        分子：
        <a-input
          placeholder="请输入分子"
          style="width: 130px"
          v-model="fractionNumerator"
        />
        分母：
        <a-input
          placeholder="请输入分母"
          style="width: 130px"
          v-model="fractionDenominator"
        />
      </modal>
      <modal
        title="水平线属性"
        :show="isShowLineModal"
        @cancel="closeLineModal"
        @submit="insertLine"
        :width="300"
      >
        <div>
          <div style="display: flex">
            <div style="line-height: 32px">线宽：</div>
            <a-input-number v-model="lineWidth" :min="0" />
            <div style="line-height: 32px; margin-left: 50px">颜色：</div>
            <colorPicker
              style="margin-top: 9px; cursor: pointer"
              v-model="lineColor"
              v-on:change="handleLineColorChange"
            />
          </div>
          <div style="display: flex; margin-top: 10px">
            <div style="line-height: 32px">类型：</div>
            <a-select
              class="prop-select"
              v-model="lineType"
              dropdownClassName="xeditor-input-up"
            >
              <a-select-option value="solid">直线</a-select-option>
              <a-select-option value="dash">虚线</a-select-option>
            </a-select>
          </div>
        </div>
      </modal>
      <!-- 单元格属性 -->
      <modal
        title="单元格属性"
        :show="isShowCellAttr"
        @cancel="closeCellAttr"
        @submit="confirmCellAttr"
        :width="400"
      >
        <div>
          <div class="editor-cellPaddingSet">
            上边距:
            <a-input-number v-model="padding_top" />
            下边距:
            <a-input-number v-model="padding_bottom" />
          </div>
          <div class="editor-cellPaddingSet">
            左边距:
            <a-input-number v-model="padding_left" />
            右边距:
            <a-input-number v-model="padding_right" />
          </div>
          <div class="editor-cellPaddingSet">
            <span style="line-height: 32px">网格线:</span>
            <a-select
              v-model="cellRowLineType"
              style="width: 92px; padding-left: 3px"
              dropdownClassName="xeditor-input-up"
            >
              <a-select-option value="cellRowLineTypeNone"
                >不展示</a-select-option
              >
              <a-select-option value="cellRowLineTypeSolid"
                >实线</a-select-option
              >
            </a-select>
            背景色：
            <icon-common
              icon="icon-masaike1"
              style="cursor: pointer"
              v-show="showNoCellBgColor"
              @iconClick="handleNoCellBgColorClick"
            ></icon-common>
            <colorPicker
              style="cursor: pointer"
              v-model="cellBgColor"
              v-on:change="handleChangeBgColor"
              v-show="!showNoCellBgColor"
              ref="cellBgColorPicker"
            />
            <a-button
              style="margin-left: 5px"
              size="small"
              @click="handleNoCellBgColorBtnClick"
              >无背景</a-button
            >
          </div>
        </div>
        <div class="editor-cellHeightSet">
          <div style="line-height: 32px" class="editor-cellHeightSet">
            单元格高度：
          </div>
          <a-input-number
            v-model="cell_height"
            style="width: 178px"
            class="editor-cellHeightSet"
          />
        </div>
        <div class="editor-cellHeightSet">
          <a-checkbox
            @change="handleCellLockChange"
            :checked="setCellLock"
            class="editor-cellHeightSet"
            >锁定当前单元格</a-checkbox
          >
          <a-checkbox
            @change="cellNoWrap = !cellNoWrap"
            :checked="cellNoWrap"
            class="editor-cellHeightSet"
            >不换行模式</a-checkbox
          >
          <a-checkbox
            @change="aggregationMode = !aggregationMode"
            :checked="aggregationMode"
            class="editor-cellHeightSet"
            >聚合模式</a-checkbox
          >
        </div>
      </modal>
      <modal
        title="页面设置"
        :show="isShowPageConfigSetting"
        @cancel="closePageConfigSetting"
        @submit="confirmPageConfigSetting"
        :width="modal_width"
      >
        <div class="pageConfigSetting">
          <div class="lineModal">
            <label class="pageSetLabel">上边距:</label>
            <a-input-number class="prop-select-page" v-model="pagePaddingTop" />
            <label class="pageSetLabel">下边距:</label>
            <a-input-number
              class="prop-select-page"
              v-model="pagePaddingBottom"
            />
          </div>
          <div class="lineModal">
            <label class="pageSetLabel">左边距:</label>
            <a-input-number
              class="prop-select-page"
              v-model="pagePaddingLeft"
            />
            <label class="pageSetLabel">右边距:</label>
            <a-input-number
              class="prop-select-page"
              v-model="pagePaddingRight"
            />
          </div>
          <div class="lineModal">
            页面方向:
            <a-select
              class="prop-select-page"
              v-model="pageDirection"
              dropdownClassName="xeditor-input-up"
            >
              <a-select-option :value="'vertical'">纵向</a-select-option>
              <a-select-option :value="'horizontal'">横向</a-select-option>
            </a-select>
            <label class="pageSetLabel">纸张类型:</label>
            <a-select
              class="prop-select-page"
              v-model="pageType"
              dropdownClassName="xeditor-input-up"
            >
              <a-select-option :value="'A4'">A4</a-select-option>
              <a-select-option :value="'A5'">A5</a-select-option>
              <a-select-option :value="'B5'">B5</a-select-option>
              <a-select-option :value="'B5(JIS)'">B5(JIS)</a-select-option>
              <a-select-option :value="'16K'">16K</a-select-option>
              <a-select-option :value="'16K(BIG)'">16K(BIG)</a-select-option>
              <a-select-option :value="'custom'">自定义</a-select-option>
            </a-select>
          </div>
          <div class="lineModal" v-if="pageType === 'custom'">
            <label class="pageSetLabel">宽度(cm):</label>
            <a-input-number class="prop-select-page" v-model="pageSizeWidth" />
            <label class="pageSetLabel">高度(cm):</label>
            <a-input-number class="prop-select-page" v-model="pageSizeHeight" />
          </div>
        </div>
      </modal>
      <!--字体设置-->
      <font-settings
        :show="isShowWordModal"
        :fontSizeList="fontSizeList"
        :fontTypeList="fontTypeList"
        :effect="isMarkType"
        @cancel="closeWordModal"
        @submit="confirmWordModal"
        :initFontStyle="init_font_style"
        :noColorFlag="noColorFlag"
      ></font-settings>

      <paragraph
        ref="paragraph"
        :editorId="editorId"
        :isShowParaAttrModal="isShowParaAttrModal"
        :isShowListModal="isShowListModal"
        @submit="confirmParaAttrModal"
        @cancel="closeParaAttrModal"
      ></paragraph>
      <group
        ref="group"
        :editorId="editorId"
        :isShowGroupModal="isShowGroupModal"
        :modifyProperty="modifyProperty"
        @submit="confirmGroupModal"
        @cancel="closeGroupModal"
      ></group>
      <fieldTip
        :style="{
          left: fieldTipStyle.left - 5 + 'px',
          top: fieldTipStyle.top - 62 + 'px',
          position: 'absolute',
        }"
        v-show="fieldTipMsg"
        :fieldTipTitle="fieldTipTitle"
        :fieldTipMsg="fieldTipMsg"
      ></fieldTip>
      <debuggingTool
        ref="debuggingTool"
        :editorId="editorId"
        :versionInfo="versionInfo"
        :debugInputModalInfo="debugInputModalInfo"
        :showDebugInputModal="showDebugInputModal"
        :show="showDebuggingTool"
        :debugButtons="debugButtons"
        @cancel="openDebuggingTool"
        @cancelChildModal="cancelDebugChildModal"
        @handleChildModalOk="handleDebugChildModalOk"
      />

      <comment
        :editorId="editorId"
        :visible="isShowCommentList"
        :comments="comments"
        :select-id="selectCommentId"
        :hideReplaceBtn="hideReplaceBtn"
        @editComment="openCommentEditModal"
        @close="closeCommentList"
        @delete="deleteComment"
        @replace="replaceContent"
        @select="selectComment"
      ></comment>
      <comment-edit
        ref="commentEdit"
        :show="isOpenCommentEditModal"
        :comment="curComment"
        :allComments="comments"
        @submit="submitComment"
        @cancel="closeCommentEditorModal"
      ></comment-edit>
      <setting-tool
        ref="settingTool"
        :show="isOpenSettingTool"
        @cancel="closeSettingTool"
        @submit="changeEditorConfig"
      ></setting-tool>
      <!-- <cus-comment-edit
        :show="isOpenCusCommentEditModal"
        :comment="curCusComment"
        @submit="submitCusComment"
        @cancel="closeCusCommentEditorModal"
      ></cus-comment-edit> -->
      <div id="newEditorToBratchPrint" v-show="false"></div>
      <float-button
        ref="editorFloatButton"
        :show="floatBtnShow"
        :btnData="floatBtnData"
        :floatBtnPosition="floatBtnPosition"
        @floatBtnClick="floatBtnClick"
      ></float-button>
      <mobile-button
        :show="VL.is_mobile_button"
        :btnData="mobileBtnData"
        :mobilePosition="mobilePosition"
        @mobileBtnClick="mobileBtnClick"
      ></mobile-button>
      <qrCodeModal
        :show="isShowQrCodeModal"
        :qrCodeInfo="qrCodeInfo"
        @submit="insertQrCode"
        @cancel="closeQrCodeModal"
      >
      </qrCodeModal>
      <!-- <div id="testRight" v-show="testRight">这是测试右键菜单</div> -->
      <!-- 移动端的悬浮球 -->
      <mobileFloatBall
        @toggleInputDOM="toggleInputDOM"
        :text="mobileFloatBallText"
        ref="mobileFloatBall"
      >
      </mobileFloatBall>
      <mobilePrompt
        :show="mobilePromptShow"
        :options="options"
        ref="mobilePromptRef"
      ></mobilePrompt>
    </div>
  </a-config-provider>
</template>
<script>
import "./global-component";
import { keepDecimal } from "./assets/js/utils";
// import "vant-green/lib/index.css";
import zhCN from "ant-design-vue/lib/locale-provider/zh_CN";
import modal from "./components/common/modal.vue";
import comment from "./components/comment/comment.vue";
import qrCodeModal from "./components/qrCodeModal.vue";
import commentEdit from "./components/comment/commentEdit.vue";
// import cusCommentEdit from "./components/comment/cusCommentEdit.vue";
import initEditor from "msun-editor";
import rightClickMenu from "./components/rightClickMenu.vue";
import fieldMixIn from "./mixin/fieldMixIn";
import OCRClient from "./mixin/OCRClient.js";
import formulaMixIn from "./mixin/formulaMixIn";
import rightMenuMixIn from "./mixin/rightMenuMixIn";
import fieldAdvancedMixin from "./mixin/fieldAdvancedMixin";
import print from "./mixin/print";
import searchAndReplaceMixin from "./mixin/searchAndReplaceMIxin";
import editorEventMixIn from "./mixin/editorEvent";
import traceContrastMixIn from "./mixin/traceContrast";
import extMixIn from "./mixin/ext";
import paragraph from "./components/paragraph/paragraph.vue";
import inputSelect from "./components/inputSelect.vue";
import inputSelectMixIn from "./mixin/inputSelectMixIn";
import group from "./components/group/group.vue";
import paragraphMixIn from "./mixin/paragraph";
import groupMixin from "./mixin/group";
import fontSettings from "./components/font.vue";
import showTableLine from "./mixin/showTableLine";
import addChioceField from "./components/choose/addChioceField.vue";
import choiceMixIn from "./mixin/chioce";
import systemPrint from "./components/systemPrint.vue";
import floatingMenu from "./components/floatingMenu.vue";
import waterMark from "./components/waterMark.vue";
import contrast from "./components/contrast/contrast.vue";
import fieldTip from "./components/field/fieldTip.vue";
import fieldFormula from "./components/field/fieldFormula.vue";
import contrastTip from "./mixin/contrastTip";
import config from "../package.json";
import debuggingMixIn from "./mixin/debugging";
import commentMixIn from "./mixin/comment";
import cusComment from "./mixin/cusComment";
import imageEditing from "./components/imageEditing.vue";
import chartSettings from "./components/chartSettings.vue";
import imageEditingMixIn from "./mixin/imageEditingMixIn";
import chartSettingMixIn from "./mixin/chartSettingMixIn";
import ButtonPopup from "./components/buttonPopup.vue";
import buttonPopupMixIn from "./mixin/buttonPopup";
import floatingMenuMixIn from "./mixin/floatingMenuMixIn";
import medicalConfig from "./config";
import localConfigSet from "./mixin/localConfigSet";
import Monitor from "./components/monitor.vue";
import shapeFloating from "./components/shapeFloating.vue";
import waterMarkFloating from "./components/waterMarkFloating.vue";
import settingTool from "./components/settingTool/settingTool.vue";
import mobileFloatBall from "./components/mobile/mobileFloatBall.vue";
import mobilePrompt from "./components/mobile/mobilePrompt.vue";
import settingToolMixin from "./mixin/settingTool";
import floatBtnMixIn from "./mixin/floatBtnMixIn";
import qrCodeMixIn from "./mixin/qrCode";
import tableMixIn from "./mixin/tableMixIn";
import fractionMixIn from "./mixin/fractionMixIn";
import mobilePromptMixin from "./mixin/mobile/mobilePromptMixin";
import mobileEventMixin from "./mixin/mobile/mobileEventMixin";
import mobileButton from "./components/mobile/mobileButton.vue";
import customWaterMark from "./components/customWaterMark.vue";
import fieldCascade from "./components/field/fieldCascade.vue";
import fieldAutomation from "./components/field/fieldAutomation.vue";
import rulerEditor from "./components/rulerEditor.vue";
import rulerEditorMixIn from "./mixin/rulerEditorMixIn";
import immediatelyPrintConfig from "./components/immediatelyPrintConfig.vue";
import immediatelyPrintConfigMixIn from "./mixin/immediatelyPrintConfigMixIn";
import grfTrans from "./mixin/grfTrans";
import iconCommon from "./components/common/iconCommon.vue";
import mammoth from "mammoth";

// import { testMobileData } from "./testMobileData";
export default {
  name: "msunEditorVue",
  mixins: [
    settingToolMixin,
    fieldMixIn,
    formulaMixIn,
    rightMenuMixIn,
    print,
    paragraphMixIn,
    groupMixin,
    searchAndReplaceMixin,
    showTableLine,
    editorEventMixIn,
    choiceMixIn,
    inputSelectMixIn,
    contrastTip,
    traceContrastMixIn,
    extMixIn,
    debuggingMixIn,
    commentMixIn,
    cusComment,
    imageEditingMixIn,
    chartSettingMixIn,
    buttonPopupMixIn,
    floatingMenuMixIn,
    localConfigSet,
    floatBtnMixIn,
    qrCodeMixIn,
    tableMixIn,
    fractionMixIn,
    mobilePromptMixin,
    mobileEventMixin,
    fieldAdvancedMixin,
    rulerEditorMixIn,
    immediatelyPrintConfigMixIn,
    grfTrans,
  ],
  props: ["upConfig", "parent"],
  components: {
    settingTool,
    Monitor,
    rightClickMenu,
    modal,
    fontSettings,
    paragraph,
    group,
    fieldTip,
    systemPrint,
    addChioceField,
    inputSelect,
    contrast,
    comment,
    commentEdit,
    // cusCommentEdit,
    imageEditing,
    chartSetting: chartSettings,
    ButtonPopup,
    floatingMenu,
    fieldFormula,
    shapeFloating,
    waterMarkFloating,
    qrCodeModal,
    waterMark,
    mobileFloatBall,
    mobilePrompt,
    mobileButton,
    customWaterMark,
    fieldCascade,
    fieldAutomation,
    rulerEditor,
    immediatelyPrintConfig,
    iconCommon,
  },
  provide() {
    return {
      parentInstance: this,
    };
  },
  data() {
    return {
      formattedText: "",
      tableRowLineType: "tableRowLineTypeNone",
      cellRowLineType: "cellRowLineTypeNone",
      initShapeFloat: {
        x: 100,
        y: 250,
      },
      initWaterMarkFloat: {
        x: 100,
        y: 250,
      },
      curFieldID: "",
      showShapeFloating: false,
      showWaterMarkFloating: false,
      showWaterMark: false,
      showCustomWaterMark: false,
      url: "",
      savedRange: null, // 保留当前选择的范围
      openFileType: "load",
      noColorFlag: false,
      pdfLoading: false,
      insertMarkImage: false,
      imagePopUpFrame: true,
      isShowCaliper: false,
      focusOffset: 0,
      versionInfo: config,
      caliper_height: 50,
      caliper_num: 10,
      caliper_sp: 15,
      editorId: "",
      testRight: false,
      triggerFlag: false,
      showInputSelect: false,
      isShapeMode: false,
      showFieldFormula: false,
      formula: "",
      hideReplaceBtn: false,
      is_touch: true, // 节流用
      timer: null, // 节流用
      is_move: false, // 手指滑动
      start_y: 0, // 手指滑动
      distance_y: 0, // 手指滑动
      frameId: 1, // 手指滑动
      waterMark: false,
      lineWidth: 0.8,
      lineColor: "#000",
      lineType: "solid",
      isShowNumberSelect: false,
      inputLocation: {
        left: 0,
        top: 0,
      },
      separatorGroups: [
        { index: 0, separator: "，" },
        { index: 1, separator: "；" },
        { index: 2, separator: "、" },
        { index: 3, separator: "%" },
      ],
      locale: zhCN,
      modal_width: 600,
      // editor: null,
      // instance: null,
      // curClickInfo: {}, //当前点击能够获取到的信息
      init_font_style: {},
      fontSizeList: [],
      fontTypeList: [],
      print_parameter: {
        type: 2,
        application: "RawPrintPlugin",
        keyword: "initPlugin",
        content: {
          pluginName: "RawPrintPlugin",
          pluginVersion: "1.0.0.0",
          pluginRemark: "原始打印插件",
          dllFile: "plugins/RawPrintPlugin/RawPrintPlugin.dll",
        },
      },
      print_pic: {
        type: 0,
        application: "RawPrintPlugin",
        keyword: "printImage",
        content: {
          printerName: "Microsoft Print to PDF",
          pageSizeName: "A4",
          pageSizeWidth: 827,
          pageSizeHeight: 1169,
          marginLeft: 30,
          marginTop: 1,
          landscape: false, // true 横向 \ false 纵向
          imageBase64: "",
        },
      },
      editorMonitor: {},
      insertTableWithNewPage: false,
      isMobile1: false,
      VL: {},
      reg: [],
      // 图表
      chartKeys: [],
      ocrClient: new OCRClient(),
      fileList: [],
    };
  },
  mounted() {
    // const canvasDom = document.getElementById(this.editorId);
    // canvasDom.addEventListener("scroll", this.handleScroll, true);
    document.addEventListener(
      "selectionchange",
      this.HandleSelectionChange,
      false
    );

    document.addEventListener("mousedown", this.mouseDownEvent, true);
    const realConfig = this.handleConfig();
    const testRawData = localStorage.getItem("testRawData")
      ? localStorage.getItem("testRawData")
      : this.upConfig?.rawData;
    // 合并本地系统配置后再初始化编辑器
    this.initEditorAfterHandleConfig(testRawData, realConfig); // 该方法之后才有 editor
    this.boundTouchEvent();
    if (this.instance && this.instance.editor.isMobileTerminal()) {
      setTimeout(() => {
        this.isMobile1 = true;
      }, 500);
    }
    if (this.instance?.localTest?.useLocal) {
      document.addEventListener("visibilitychange", () => {
        if (document.visibilityState === "visible") {
          this.instance?.editor?.focus();
        }
      });
    }
  },
  created() {
    const uuid = "xxxxxxxx".replace(/[xy]/g, (c) => {
      const r = (Math.random() * 16) | 0;
      const v = c === "x" ? r : (r & 0x3) | 0x8;
      return v.toString(16);
    });
    this.editorId = "editor_" + uuid;
  },
  beforeDestroy() {
    this.$refs.editor_vue.innerHTML = "";
    this.editor.init_canvas.parentNode.innerHTML = "";
    document.removeEventListener("mousedown", this.mouseDownEvent);
    this.mouseoutFn &&
      this.editor.init_canvas.removeEventListener("mouseout", this.mouseoutFn);
    this.initChildInstance(null);
    this.editor.destroy();
    this.editor.history.clear();
    this.editor = null;
    this.instance = null;
  },
  methods: {
    openWordFile() {
      const input = document.createElement("input");
      input.hidden = "";
      input.type = "file";
      input.accept = ".docx";
      input.onchange = this.handleWordFile;
      input.click();
    },

    upload() {
      const password = localStorage.getItem("editorPassword");
      if (!password || password !== "Bianjiqiocr") {
        // 创建密码验证弹窗
        this.showPasswordDialog()
          .then((isValid) => {
            if (isValid) {
              // 密码验证通过，继续执行上传逻辑
              this.executeUpload();
            }
            // 如果密码不正确，直接返回，不执行上传
          })
          .catch(() => {
            // 用户取消或其他错误，不执行上传
          });
        return;
      }
      // 密码已验证，直接执行上传
      this.executeUpload();
    },

    // 显示密码验证对话框
    showPasswordDialog() {
      return new Promise((resolve, reject) => {
        // 创建遮罩层
        const overlay = document.createElement("div");
        overlay.style.cssText = `
          position: fixed;
          top: 0;
          left: 0;
          width: 100%;
          height: 100%;
          background-color: rgba(0, 0, 0, 0.5);
          z-index: 10000;
          display: flex;
          justify-content: center;
          align-items: center;
        `;

        // 创建对话框
        const dialog = document.createElement("div");
        dialog.style.cssText = `
          background: white;
          border-radius: 8px;
          padding: 24px;
          box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
          min-width: 320px;
          max-width: 400px;
        `;

        // 创建对话框内容
        dialog.innerHTML = `
          <div style="margin-bottom: 16px;">
            <h3 style="margin: 0 0 8px 0; font-size: 16px; color: #333;">权限验证</h3>
            <p style="margin: 0; font-size: 14px; color: #666;">请输入访问密码以继续操作</p>
          </div>
          <div style="margin-bottom: 20px;">
            <input type="password" id="passwordInput" placeholder="请输入密码"
                   style="width: 100%; padding: 8px 12px; border: 1px solid #d9d9d9; border-radius: 4px; font-size: 14px; box-sizing: border-box;" />
          </div>
          <div style="text-align: right;">
            <button id="cancelBtn" style="margin-right: 8px; padding: 6px 16px; border: 1px solid #d9d9d9; background: white; border-radius: 4px; cursor: pointer; font-size: 14px;">取消</button>
            <button id="confirmBtn" style="padding: 6px 16px; border: none; background: #1890ff; color: white; border-radius: 4px; cursor: pointer; font-size: 14px;">确认</button>
          </div>
        `;

        overlay.appendChild(dialog);
        document.body.appendChild(overlay);

        // 获取元素引用
        const passwordInput = dialog.querySelector("#passwordInput");
        const cancelBtn = dialog.querySelector("#cancelBtn");
        const confirmBtn = dialog.querySelector("#confirmBtn");

        // 自动聚焦到密码输入框
        setTimeout(() => passwordInput.focus(), 100);

        // 清理函数
        const cleanup = () => {
          document.body.removeChild(overlay);
        };

        // 验证密码函数
        const validatePassword = () => {
          const inputPassword = passwordInput.value.trim();
          if (inputPassword === "Bianjiqiocr") {
            // 密码正确，保存到localStorage
            localStorage.setItem("editorPassword", inputPassword);
            cleanup();
            resolve(true);
          } else {
            // 密码错误，显示错误提示
            passwordInput.style.borderColor = "#ff4d4f";
            passwordInput.placeholder = "密码错误，请重新输入";
            passwordInput.value = "";
            passwordInput.focus();
          }
        };

        // 绑定事件
        confirmBtn.addEventListener("click", validatePassword);

        cancelBtn.addEventListener("click", () => {
          cleanup();
          reject(new Error("用户取消"));
        });

        // 支持回车键确认
        passwordInput.addEventListener("keypress", (e) => {
          if (e.key === "Enter") {
            validatePassword();
          }
        });

        // 点击遮罩层关闭
        overlay.addEventListener("click", (e) => {
          if (e.target === overlay) {
            cleanup();
            reject(new Error("用户取消"));
          }
        });

        // 重置输入框样式
        passwordInput.addEventListener("input", () => {
          passwordInput.style.borderColor = "#d9d9d9";
          if (passwordInput.placeholder === "密码错误，请重新输入") {
            passwordInput.placeholder = "请输入密码";
          }
        });
      });
    },

    // 执行上传逻辑
    executeUpload() {
      // 创建文件输入元素
      const input = document.createElement("input");
      input.type = "file";
      input.accept = "image/*,.pdf,application/pdf";
      input.multiple = false; // 只允许选择一个文件
      input.style.display = "none";

      // 文件选择处理函数
      const handleFileSelect = async (event) => {
        const file = event.target.files[0];
        if (!file) return;
        // 读取文件内容
        const reader = new FileReader();
        reader.readAsText(file);

        this.fileList = [file];
        await this.processConversion();
        // 清理input元素
        document.body.removeChild(input);
        this.fileList = [];
      };

      // 绑定事件并触发文件选择
      input.addEventListener("change", handleFileSelect);
      document.body.appendChild(input);
      input.click();
    },

    async processConversion() {
      try {
        const options = {
          apply_document_tree: 1,
          apply_merge: 1,
          catalog_details: 1,
          dpi: 144,
          formula_level: 1,
          get_excel: 1,
          get_image: "objects",
          markdown_details: 1,
          page_count: 1000,
          page_details: 1,
          paratext_mode: "annotation",
          parse_mode: "auto",
          table_flavor: "html",
          crop_dewarp: 1, // 1 是裁剪 0 是不裁剪
        };

        const responseStr = await this.ocrClient.recognize(
          this.fileList[0],
          options
        );
        const response = JSON.parse(responseStr);
        console.log("---扫描结果--", response);

        // 展示OCR识别结果
        const markdownStr = response.result.markdown;
        const rawData = this.instance.editor.transformHTML2RawData(markdownStr);
        this.instance.editor.reInitRawByConfig(rawData);
        this.instance.editor.refreshDocument();
        console.log(markdownStr, "OCR识别结果markdown");
      } catch (error) {
        this.$editor.error("OCR处理失败: " + error.message);
      }
    },

    handleWordFile(e) {
      const file = e.target.files[0];
      // 验证文件类型
      if (!file.name.match(/\.docx$/i)) {
        console.error("请选择.docx格式的Word文档");
        return;
      }
      console.log(file.name, "文件名");
      console.log(file.size, "文件大小");

      // 使用FileReader读取文件
      const reader = new FileReader();

      reader.onload = function (e) {
        const arrayBuffer = e.target.result;

        // 使用Mammoth解析.docx
        mammoth
          .extractRawText({ arrayBuffer: arrayBuffer })
          .then((result) => {
            const text = result.value;
            console.log("解析成功:", text);
          })
          .catch((err) => {
            console.error("解析失败:", err.message);
          });
      };

      reader.onerror = function () {
        console.error("文件读取失败:", reader.error);
      };

      reader.readAsArrayBuffer(file);
    },
    // AI 聊天机器人 ↓
    onGetDataset(res) {
      // 设置数据集
      console.log("执行了 onGetDataset, 设置数据集", res);
      // this.$refs.msunReport.setDataSets([res]);
      // this.$refs.msunReport.initReportData(null);
    },
    clearData() {
      // 清空数据集
      console.log("执行了 clearData 清空数据集");
      // this.$refs.msunReport.setDataSets([]);
      // const InsStore = this.getInstance();
      // InsStore.sheet.clear();
    },
    onInitGrfData(res) {
      // 恢复table GRF数据
      console.log("执行了 oInitGrfData 恢复 table GRF 数据", res);
      // this.$refs.msunReport.initReportData(res);
    },
    getInstance() {
      return this.$refs.msunReport.getInsStore();
    },
    // AI 聊天机器人 ↑
    handleConfig() {
      // 如果配置了右键菜单就用配置的
      if (this.upConfig?.rightClickMenuConfig) {
        this.currentRightMenuConfig = this.upConfig.rightClickMenuConfig;
      }
      //设置了不显示批注替换按钮就隐藏
      if (this.upConfig?.hideReplaceBtn) {
        this.hideReplaceBtn = true;
      }
      if (this.upConfig?.medicalFont) {
        medicalConfig.formula_font =
          Number(this.upConfig?.medicalFont) *
          medicalConfig.img_devicePixelRatio;
      }
      // 如果遇到正常的打印机，设置localStorage
      if (this.upConfig?.printMarginTop) {
        this.print_pic.content.marginTop = this.upConfig?.printMarginTop;
      }
      if (this.upConfig?.printMarginLeft) {
        this.print_pic.content.marginLeft = this.upConfig?.printMarginLeft;
      }
      const systemConfig = this.upConfig?.systemConfig || {
        page_size_type: "A4",
        secret: "",
      };
      // 合并本地系统配置
      this.initReadConfigInfo(systemConfig);
      const realConfig = this.deleteEmptyConfig(systemConfig);
      realConfig.version = this.versionInfo.version; // 这是 package.json 里边的 version
      if (
        !realConfig.version.startsWith("10.") &&
        this.versionInfo.realVersion
      ) {
        realConfig.version = this.versionInfo.realVersion; // 这是执行的 node build before 的版本
      }
      return realConfig;
    },
    initEditorAfterHandleConfig(rawData, config) {
      try {
        this.instance = initEditor(`#${this.editorId}`, config);
        this.editorUseLocalTest();
        // rawData = testMobileData;
        if (rawData) {
          this.instance.editor.reInitRawByConfig(rawData);
          this.instance.editor.refreshDocument();
        }
      } catch (error) {
        this.sendEditorToParent(error);
        return;
      }
      this.editor = this.instance.editor;
      if (this.editor) {
        this.initShapeFloat.x =
          this.editor.init_canvas.getBoundingClientRect().left +
          this.editor.page_left -
          46;
        this.initWaterMarkFloat.x =
          this.editor.init_canvas.getBoundingClientRect().left +
          this.editor.page_left -
          46;
        this.VL = this.editor.internal.VL;
      }
      this.initPrinterList();

      // 移动端 执行 程序 - 开始 ↓
      // 只监控电脑端
      this.initMonitorInfo();
      // 移动端 执行 程序 - 结束 ↑
      // 自定义右键菜单示例 ↓
      // this.editor.event.on("contextmenu", (e, res) => {
      //   console.log(e, res, "e, res");
      //   this.testRight = true;
      //   this.$nextTick(() => {
      //     // 计算并修改右键菜单位置
      //   });
      //   return {
      //     list: [
      //       {
      //         key: "searchAndReplace",
      //       },
      //     ],
      //     hideSysMenu: true, // 如果为true 就用自己的div 不用默认的了
      //   };
      // });
      // 自定义右键菜单示例 ↑
      // BUS.$emit("editor_" + this.editorId, { instance: this.instance });
      this.initChildInstance(this.instance);

      this.bindInstanceEvent(this.instance);

      // 监听 ctrl+v键盘事件
      // document.addEventListener("paste", (event) => {
      //   handleClipBoardData(event, this.instance);
      // });
      // 校验secret是否合法,此处secret需要先经过base64解码
      /*const decryptKey = decrypt(systemConfig.secret);
      if (decryptKey && decryptKey.startsWith("msun-editor-")) {
        const expirationTime = decryptKey
          .split("msun-editor-")[1]
          .replace(/\//g, "-");
        // 校验secret是否过期
        if (new Date().valueOf() > new Date(expirationTime).valueOf()) {
          this.sendEditorToParent({ legalSecret: true, expired: true });
          return;
        }
      } else {
        this.sendEditorToParent({ legalSecret: false, expired: true });
        return;
      }*/

      this.sendEditorToParent(); // 往父组件传递editor
    },
    initMonitorInfo() {
      this.editorMonitor = this.editor.monitor;
      if (performance && performance.memory && this.editorMonitor) {
        this.editorMonitor.initMemory = performance.memory.usedJSHeapSize;
      }
    },
    HandleSelectionChange() {
      let sel = window.getSelection && window.getSelection();
      if (sel && sel.rangeCount) {
        this.savedRange = sel.getRangeAt(0);
      }
    },
    deleteEmptyConfig(config) {
      for (const key in config) {
        if (
          config[key] === null ||
          config[key] === undefined ||
          config[key] === ""
        ) {
          delete config[key];
        }
      }
      return config;
    },
    editorUseLocalTest() {
      if (!this.instance) {
        return;
      }
      if (!this.instance.localTest) {
        this.instance.localTest = {};
      }
      this.instance.localTest.useLocal =
        !!localStorage.getItem("EditorLocalTest");
      this.instance.localTest.useNew = !!localStorage.getItem("EditorUseNew");
      if (localStorage.getItem("EditorTransUse") === "0") {
        this.instance.localTest.transUse = false;
      }
    },
    //初始化子组件用到的实例对象
    initChildInstance(instance) {
      this.$refs.rightMenu.editor = instance;
      this.$refs.group.editor = instance ? instance.editor : instance;
      this.$refs.system_print.editor = instance;
      this.$refs.field_date_picker.editor = instance;
      this.$refs.field_select.editor = instance;
      this.$refs.fieldProperty.editor = instance;
      this.$refs.medicalCalcFormula.editor = instance;
      this.$refs.buttonPopup.editor = instance;
      this.$refs.search_and_replace.instance = instance;
      this.$refs.fieldFormulaEdit.editor = instance;
      this.$refs.image_editing.editor = instance;
      this.$refs.choiceField.editor = instance;
      this.$refs.field_number_select.editor = instance;
      this.$refs.shapeFloat.editor = instance;
      this.$refs.waterMarkFloat.editor = instance;
      this.$refs.settingTool.editor = instance;
      this.$refs.fieldSearchBox.editor = instance ? instance.editor : instance;
      this.$refs.commentEdit.editor = instance;
      this.$refs.mobileFloatBall.editor = instance;
      this.$refs.customWaterMark.editor = instance;
      this.$refs.cascade.editor = instance;
      this.$refs.fieldAuto.editor = instance;
      this.$refs.rulerEditor.editor = instance;
      this.$refs.immediatelyPrintConfig.editor = instance;
      if (instance) {
        this.replaceFormatList =
          instance.builtInVariable.fieldDefaultFormatList;
      }
    },
    // showSubMenu(subMenuContainer) {
    //   setTimeout(() => {
    //     if (document.querySelector(".editor-vue")) {
    //       document.querySelector(".editor-vue").appendChild(subMenuContainer);
    //     }
    //   }, 100);
    // },
    mouseDownEvent(e) {
      const currentDom = e.target;
      const rightClickMenuDom = this?.$refs?.rightMenu?.$el; // 右键菜单divDOM
      if (rightClickMenuDom && !rightClickMenuDom.contains(currentDom)) {
        this.cancelRightMenu();
      }
      const floatBtnDom = this?.$refs?.editorFloatButton?.$el; // 右键菜单divDOM
      if (floatBtnDom && !floatBtnDom.contains(currentDom)) {
        this.hideFloatBtn();
      }
      const datePickerEditor =
        document.getElementsByClassName("date_picker_editor");
      const fieldDatePicker = document.getElementsByClassName(
        "editorDatePickPanelClassField"
      );
      if (datePickerEditor.length && fieldDatePicker.length) {
        const result1 = this.outDom(datePickerEditor, currentDom);
        const result2 = this.outDom(fieldDatePicker, currentDom);
        if (result1 && result2) {
          this.showFieldDatePicker = false;
        }
      }

      const colorBox = document.getElementsByClassName("box")[1];
      if (colorBox) {
        const className = colorBox.className;
        className === "box open"
          ? (this.noColorFlag = true)
          : (this.noColorFlag = false);
      }
      const fieldSelectPanel =
        document.getElementsByClassName("field_select_class");
      if (fieldSelectPanel.length) {
        const result3 = this.outDom(fieldSelectPanel, currentDom);
        if (result3) {
          this.showFieldSelectPanel = false;
        }
      }
    },
    /**
     * 判断是否在dom内
     *
     * @param {*} dom
     */
    outDom(dom, currentDom) {
      return [].every.call(dom, (item) => !item.contains(currentDom));
    },
    openImagePopUpFrame(isOpen = true) {
      this.imagePopUpFrame = isOpen;
    },
    getVersionInfo() {
      return this.versionInfo.version;
    },
    //倾斜水印
    italicWaterMark() {
      this.showWaterMark = true;
    },
    handleScroll() {
      const canvasDom = document.getElementById(this.editorId);
      this.instance.editor.setScrollX(canvasDom.scrollLeft);
    },
    //传入改变instance属性的方法
    mergeAttributeToInstance(data) {
      for (const key in data) {
        this.instance[key] = data[key];
      }
    },
    //ctrl+F或ctrl+i快捷键事件
    ctrlShortcut(event) {
      const key = event.key;
      if (event.ctrlKey) {
        if (event.key.toLocaleLowerCase() !== "v") {
          event.preventDefault();
        }
        if (
          event.key.toLocaleLowerCase() === "s" &&
          this.instance.localTest.useLocal
        ) {
          event.preventDefault();
          localStorage.setItem(
            "testRawData",
            JSON.stringify(this.editor.getRawData())
          );
          this.$editor.info("已保存至浏览器LocalStorage【testRawData】!");
        }

        if (this.isElectron) {
          const electron = window.electron ?? window.top.electron;
          electron.electronTools.preventAllShortcuts();
        }

        event.stopPropagation();
        if (key.toLocaleLowerCase() === "f") {
          this.searchAndReplace();
          if (this.isElectron) {
            const electron = window.electron ?? window.top.electron;
            electron.electronTools.enableAllShortcuts();
          }
        } else if (key.toLocaleLowerCase() === "f12") {
          if (event.shiftKey) {
            this.openDebuggingTool(true);
          } else {
            // 当前页脚水平线显示状态
            const footerLine = this.editor.show_footer_line;
            // 获取到当前光标所在分组
            this.instance.editor.headerFooterHorizontal(true, footerLine);
          }
        } else if (event.altKey) {
          if (!this.instance.editor.internal.is_point_down) {
            this.instance.editor.internal.is_point_down = true;
            this.setFieldMaxOrMinWidth();
          }
        }
      }
      if (event.altKey) {
        if (
          event.key.toLocaleLowerCase() === "z" &&
          this.instance.localTest.useLocal
        ) {
          this.insertField();
        }
        if (event.key.toLocaleLowerCase() === "x") {
          this.instance.editor.internal.altX = true;
          this.showChoiceModal();
        }
        if (
          event.key.toLocaleLowerCase() === "c" &&
          this.instance.localTest.useLocal
        ) {
          this.instance.editor.mergeCell();
        }
        if (
          event.key.toLocaleLowerCase() === "v" &&
          this.instance.localTest.useLocal
        ) {
          this.showSplitCellModal();
        }
        if (
          event.key.toLocaleLowerCase() === "s" &&
          this.instance.localTest.useLocal
        ) {
          const { editor } = this.instance;

          const fields = editor.selection.selected_cells_fields;
          fields.forEach((field) => {
            field.min_width = 0;
            field.max_width = 0;
          });
          const fieldsKeys = Object.keys(
            editor.selection.selected_fields_chars.fieldIdVsChars
          );
          fieldsKeys.forEach((key) => {
            const field = editor.getFieldById(key);
            field.replaceRule = [];
          });
          editor.refreshDocument(true);
        }
      }
    },
    sendEditorToParent(errMsg) {
      if (errMsg) {
        this.$emit("init", null, errMsg);
      } else {
        const editor = {
          ...this.instance,
          id: this.editorId,
          showGroupModal: this.showGroupModal,
          insertField: this.insertField,
          getPluginVersion: this.getPluginVersion, // 托盘打印版本
          cppPrintCanBeUsed: this.cppPrintCanBeUsed, // 是否超过了指定版本
          systemPrint: this.openSystemPrint, // 系统打印
          printView: this.convertPDF, // 打印预览 传递参数  字符串  print
          immediatelyPrint: this.immediatelyPrint, // 直接打印
          printEvenPages: this.printEvenPages, //打印偶数页
          printOddPages: this.printOddPages, //打印奇数页
          printContinueImmediate: this.printContinueImmediate, // 立即续打
          batchPrint: this.batchPrint, // 批量打印
          searchAndReplace: this.searchAndReplace,
          insertFormula: this.insertFormula,
          insertTable: this.showInsertTblModal,
          showTableAttr: this.showTableAttr,
          showCellAttr: this.showCellAttr,
          showSplitCellModal: this.showSplitCellModal,
          convertPDF: this.convertPDF,
          insertLocalImage: this.insertLocalImage,
          showFontModal: this.showWordModal,
          showChoiceModal: this.showChoiceModal, // 插入自定义选择框
          showParaAttrModal: this.showParaAttrModal,
          openFile: this.openFile,
          getPrinterName: this.getPrinterName,
          cancelRightMenu: this.cancelRightMenu,
          calcRightClickMenuPosition: this.calcRightClickMenuPosition,
          mergeAttributeToInstance: this.mergeAttributeToInstance,
          getVersionInfo: this.getVersionInfo,
          saveTraceInfo: this.saveTraceInfo, // 保存修改痕迹
          userLogin: this.userLogin, // 用户登录
          compareTraces: this.compareTraces, //返回对比的rowData
          newVersionCompareTraces: this.newVersionCompareTraces, //返回对比的rowData
          getChangeData: this.getChangeData, // 给刘通用的获取两个数据对比的痕迹
          compareGroupTraces: this.compareGroupTraces, //返回对比的分组rawdata
          addComment: this.openCommentEditModal, //添加批注
          openCommentList: this.openCommentList, //打开批注查看模式
          closeCommentList: this.closeCommentList, //关闭批注哈看模式
          getLastModificationRecord: this.getLastModificationRecord, // 获取跟上一版本对比 修改的段落和内容
          openImagePopUpFrame: this.openImagePopUpFrame,
          printCurrentPage: this.printCurrentPage, //打印当前页
          openLineModal: this.openLineModal, //打开插入线窗口
          insertButtonPopup: this.insertButtonPopup, // 插入按钮弹窗
          openPageConfigModal: this.openPageConfigModal, //页面设置弹窗
          openMedicalCalcFormula: this.openMedicalCalcFormula, // 打开医学计算公式弹窗
          jsPDFLoad: this.judgeJsPDFLoad, // 字体文件主动加载
          refreshCusCommentList: this.refreshCusCommentList,
          // openCusCommentEditModal: this.openCusCommentEditModal,
          deleteCusComment: this.deleteCusComment,
          replaceCusContent: this.replaceCusContent,
          selectCusComment: this.selectCusComment,
          submitCusComment: this.submitCusComment,
          openCusCommentEditModal: this.openCusCommentEditModal,
          shapeMode: this.shapeMode,
          waterMarkModel: this.waterMarkModel,
          showCaliperModal: this.showCaliperModal,
          showFloatBtn: this.showFloatBtn,
          referenceCusComment: this.referenceCusComment,
          insertFormulaImageByData: this.insertFormulaImageByData,
          quickInputSelect: this.quickInputSelect,
          openFieldAdvanced: this.openFieldAdvanced,
          insertChioceModal: this.insertChioceModal,
          showImmediatelyPrintConfigModal: this.showImmediatelyPrintConfigModal,
          openWordFile: this.openWordFile,
          scanAndAnalyze: this.upload,
          plugin: {
            diff: this.dmp,
          },
        };
        if (this.instance.localTest.useLocal) {
          // 目前用于api打印,是否有其他副作用位置，暂时先本地测试用
          this.instance = editor;
        }
        this.$emit("init", editor);
      }
    },
    cancelRightMenu() {
      this.isShowRightClickMenu = false;
      return;
    },
    cancelShowTableLine() {
      this.show_table_line = false;
      return;
    },
    changeBigBoxWidth() {
      this.instance.editor.changeBigBoxWidth(true);
    },
    //水印模式
    waterMarkModel() {
      this.waterMark = !this.waterMark;
      if (this.waterMark) {
        this.showWaterMarkFloating = true;
        this.$refs.waterMarkFloat.waterMarkIndex = 10000;
      } else {
        this.showWaterMarkFloating = false;
        this.instance.editor.is_mark_text = false;
      }
      this.instance.editor.waterMarkModel(this.waterMark);
    },
    //插入水印图片
    insertWaterMarkImage() {
      this.insertMarkImage = true;
      if (!this.instance.editor.waterMark) {
        this.$editor.warning("请开启水印模式");
        return;
      }
      this.insertLocalImage();
    },
    //插入自定义水印图片
    insertCustomWaterMarkImage() {
      this.insertMarkImage = true;
      if (!this.instance.editor.waterMark) {
        this.$editor.warning("请开启水印模式");
        return;
      }
      this.showCustomWaterMark = true;
    },
    //插入水印文字
    insertWaterMarkText(result) {
      this.instance.editor.insertWaterMarkTextMode(result);
    },
    //改变水印模式
    changeMarkMode(mode) {
      this.instance.editor.changeMarkMode(mode);
    },
    //打开文件
    openFile(type) {
      if (type) {
        this.openFileType = type;
      }
      const input = document.createElement("input");
      input.hidden = "";
      input.type = "file";
      input.accept = ".xml,.txt,.json,.grf,.html";
      input.onchange = this.openFileChange;
      input.click();
    },
    shapeMode() {
      this.isShapeMode = !this.isShapeMode;
      if (this.isShapeMode) {
        this.showShapeFloating = true;
        const shapes = this.editor.shapes;
        const shape = shapes.find((shape) => shape.type === "fold_line");
        if (shape) {
          this.$refs.shapeFloat.$refs.shapeContent.childNodes[5].style.display =
            "block";
        } else {
          this.$refs.shapeFloat.$refs.shapeContent.childNodes[5].style.display =
            "none";
        }
        this.$refs.shapeFloat.shapeIndex = 10000;
      } else {
        this.showShapeFloating = false;
      }
      this.editor.drawShape("close");
      this.editor.shapeMode(this.isShapeMode);
    },
    drawShape(type) {
      const shapes = this.editor.shapes;
      const shape = shapes.find((shape) => shape.type === "fold_line");
      if (shape) {
        this.$refs.shapeFloat.$refs.shapeContent.childNodes[4].style.display =
          "block";
      } else {
        this.$refs.shapeFloat.$refs.shapeContent.childNodes[4].style.display =
          "none";
      }
      this.editor.drawShape(type);
    },
    closeCaliper() {
      this.isShowCaliper = false;
    },
    showCaliperModal() {
      this.isShowCaliper = true;
      if (
        this.curClickInfo &&
        this.curClickInfo.element &&
        this.curClickInfo.element.widgetType === "caliper"
      ) {
        this.caliper_height = this.curClickInfo.element.height;
        this.caliper_num = this.curClickInfo.element.params.num;
        this.caliper_sp = this.curClickInfo.element.params.spacing;
        this.editor.update();
        this.editor.render();
      }
    },

    waterMarkSubmit(params) {
      this.showWaterMark = false;
      this.instance.editor.insertItalicMark(params.text, params);
    },
    customWaterMarkSubmit(params, change) {
      this.showCustomWaterMark = false;
      if (change) {
        this.instance.editor.updateCustomMarkImage(params);
      } else {
        const src = this.instance.utils.createImagePlaceholder("水印位");
        this.instance.editor.insertWaterMarkImage(src, params);
      }
    },
    closeWaterMark() {
      this.showWaterMark = false;
    },
    closeCustomWaterMark() {
      this.showCustomWaterMark = false;
    },
    //插入卡尺修改卡尺属性
    confirmCaliper() {
      this.isShowCaliper = false;
      if (
        this.curClickInfo &&
        this.curClickInfo.element &&
        this.curClickInfo.element.widgetType === "caliper"
      ) {
        this.editor.updateCaliper(
          this.curClickInfo.element,
          this.caliper_num,
          this.caliper_height,
          this.caliper_sp
        );
      } else {
        this.editor.insertCaliper(this.caliper_height, {
          num: this.caliper_num,
          spacing: this.caliper_sp,
        });
      }
      this.caliper_height = 30;
      this.caliper_num = 10;
      this.caliper_sp = 15;
    },

    // 展示字体模态框
    showWordModal(isMark) {
      this.fontTypeList = [];
      this.fontSizeList = this.instance.builtInVariable.fontSizeList;
      this.instance.builtInVariable.fontTypeList.forEach((e) => {
        this.fontTypeList.push({
          option: e,
          value: e,
        });
      });

      this.init_font_style = Object.assign(
        {},
        this.instance.contextState.getFontState()
      );
      const selection = this.instance.editor.selection;
      const selectChars = selection.selected_fields_chars.all_chars;
      if (selectChars && selectChars.length) {
        this.init_font_style = this.checkFont(
          [
            "color",
            "family",
            "bold",
            "italic",
            "bgColor",
            "align",
            "height",
            "bgColor",
          ],
          selectChars,
          this.init_font_style
        );
      }

      if (isMark) {
        this.isMarkType = false;
        const font = this.instance.editor.markInput.style.font.split(" ");
        if (font.length === 5) {
          this.init_font_style.family = font[4];
          this.init_font_style.bold = Number(font[0]) === 700 ? true : false;
          this.init_font_style.height = Number(font[1].split("px")[0]);
        } else if (font.length === 6) {
          this.init_font_style.family = font[5];
          this.init_font_style.italic = font[0];
          this.init_font_style.bold = Number(font[1]) === 700 ? true : false;
          this.init_font_style.height = Number(font[2].split("px")[0]);
        }
        this.init_font_style.color = this.instance.editor.markInput.style.color;
      }
      this.isShowWordModal = true;
    },
    // 关闭字体模态框
    closeWordModal() {
      this.isShowWordModal = false;
      this.isMarkType = true;
      this.instance.editor.focus();
    },
    checkFont(propArr, charArr, init_font_style) {
      for (let i = 0; i < propArr.length; i++) {
        const prop = propArr[i];
        const isSame = charArr.every(
          (obj) => obj.font[prop] === charArr[0].font[prop]
        );
        if (!isSame) {
          init_font_style[prop] = undefined;
        }
      }
      return init_font_style;
    },
    insertLine() {
      this.instance.editor.insertLine(
        this.lineWidth,
        this.lineColor,
        null,
        this.lineType
      );
      this.isShowLineModal = false;
    },
    // 字体模态框确认
    confirmWordModal(style) {
      const { editor, config } = this.instance;
      config.setFontSize(style.getFontSize); // 字号
      if (!this.isMarkType) {
        style.bgColor = null;
      }
      editor.change_font_style(style);
      this.isShowWordModal = false;
      this.isMarkType = true;
      editor.focus();
    },
    debugRaw() {
      const { editor } = this.instance;

      console.log("raw", JSON.stringify(editor.getRawData()));
    },
    debugModal() {
      const { editor } = this.instance;

      console.log("header", editor.header_cell);
      console.log("content", editor.root_cell);
      console.log("footer", editor.footer_cell);
      console.log("pages", editor.pages);
      console.log("selection", editor.selection);
    },
    closeLineModal() {
      this.isShowLineModal = false;
    },
    openLineModal() {
      this.isShowLineModal = true;
      this.lineType = "solid";
    },
    openPageConfigModal() {
      const editor = this.instance.editor;
      const config = editor.config;
      this.pagePaddingLeft = config.page_padding_left;
      this.pagePaddingRight = config.page_padding_right;
      this.pagePaddingTop = config.header_margin_top;
      this.pagePaddingBottom = config.footer_margin_bottom;
      this.pageType = config.page_size_type;
      this.pageDirection = config.page_direction;
      const pageSize = config.getPageSize();
      this.pageSizeWidth = keepDecimal(pageSize.width / 37.8, 1);
      this.pageSizeHeight = keepDecimal(pageSize.height / 37.8, 1);
      this.isShowPageConfigSetting = true;
    },
    closePageConfigSetting() {
      this.isShowPageConfigSetting = false;
    },
    showImmediatelyPrintConfigModal() {
      this.showImmediatelyPrintConfig = true;
    },
    confirmPageConfigSetting() {
      const editor = this.instance.editor;
      const config = editor.config;
      config.page_padding_left = this.pagePaddingLeft;
      config.page_padding_right = this.pagePaddingRight;
      config.page_size_type = this.pageType;
      config.page_direction = this.pageDirection;
      if (config.header_margin_top !== this.pagePaddingTop) {
        config.page_padding_top = 0;
        config.header_margin_top = this.pagePaddingTop;
      }
      if (config.footer_margin_bottom !== this.pagePaddingBottom) {
        config.page_padding_bottom = 0;
        config.footer_margin_bottom = this.pagePaddingBottom;
      }
      this.isShowPageConfigSetting = false;
      if (this.pageType === "custom") {
        editor.changeCustomPageSize(
          this.pageSizeWidth,
          this.pageSizeHeight,
          this.pageDirection
        );
      }
      editor.reInitConfig(config);
    },
    handleLineColorChange(e) {
      this.lineColor = e;
    },
  },
};
</script>
<style scoped lang="less">
#editor-vue-x {
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  width: 100%;
  height: 100%;
  overflow: hidden;

  .editor-content-x {
    // margin-top: 200px;
    height: 100%;
    width: 100%;
    // height: 100px;
    // width: 100px;
    // margin-top: 200px;
    overflow-y: hidden;
    overflow-x: auto;
  }
}

.editorTablePropChange {
  margin-top: 10px;
  margin-right: 20px;
}

.editorOpenTABSkipMode {
  margin-left: 20px;
}

.lineModal {
  width: 90%;
  margin: 5px auto;
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  align-items: center;
}

.editor-cellPaddingSet {
  margin-top: 5px;
  margin-left: 40px;
}

.editor-cellHeightSet {
  display: inline-block;
  margin-top: 5px;
  margin-left: 20px;
}

.prop-select {
  width: 90px;
}

.prop-select-page {
  width: 30%;
}

.pageSetLabel {
  width: 60px;
}

.formula-symbol {
  border-radius: 4px;
  border: 1px solid rgb(217, 217, 217);
}

.formulaEdit {
  height: 140px; // 设置高度
  overflow-y: auto; // 超多的部分出现滚动条

  &:empty::before {
    // placeholder设置
    content: attr(placeholder);
    font-size: 14px;
    color: #ccc;
    line-height: 21px;
    padding-top: 20px;
  }
}
</style>
