import Editor from "./Editor";

export default class History {
  // 撤销内容存储
  undoStack: any[] = [];
  // 重做内容存储
  redoStack: any[] = [];
  // 记录上一次撤销的的内容
  lastContentState: any = {};
  // 撤销堆栈的最长存放数量

  /**
     * 是否可以执行撤销操作
     */

  canBeUndo () {
    return !!this.undoStack.length;
  }

  /**
     * 是否可以执行重做操作
     */

  canBeRedo () {
    return !!this.redoStack.length;
  }

  /**
     * 获取撤销堆栈
     */

  getUndo (): any[] {
    return this.undoStack;
  }

  /**
     * 获取重做堆栈
     */

  getRedo (): any[] {
    return this.redoStack;
  }

  /**
     * 清空重做堆栈
     */

  clearRedo () {
    this.redoStack.length = 0;
  }

  /**
     * 添加一条数据进撤销堆栈
     */

  add (contentState: any, editor: Editor): any {
    if (this.undoStack.length >= editor.config.history_limit) {
      this.undoStack.shift();
    }
    return this.undoStack.push(contentState);
  }

  /**
     * 撤销操作
     */

  undo (editor: Editor): any {
    if (!this.canBeUndo()) return;

    // 因为保存数据的时候 是在每一次操作之前 保存的数据 所以要在第一次撤销的时候 再保存一次数据 否则 ctrl + y 的时候，就少一份数据了
    if (!this.canBeRedo()) {
      this.redoStack.push(editor.getContentState(true));
    } else {
      this.redoStack.push(this.lastContentState);
    }

    const contentState = this.undoStack.pop(); // 紧挨着 使用该变量的地方 不要放到函数开头
    this.lastContentState = contentState;

    return contentState;
  }

  /**
     * 重做操作
     */

  redo (): any {
    if (!this.canBeRedo()) return;
    const contentState = this.redoStack.pop();
    if (this.lastContentState) {
      this.undoStack.push(this.lastContentState);
    }
    this.lastContentState = contentState;
    return contentState;
  }

  /**
     * 清空历史记录
     */

  clear () {
    this.lastContentState = {};
    this.undoStack = [];
    this.redoStack = [];
  }
}
