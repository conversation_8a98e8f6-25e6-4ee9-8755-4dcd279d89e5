// import Editor, { Direction } from "./Editor";
import Editor from "./Editor";

import { redo, undo } from "./Command";
import { getCellIndex, getNextField, getPrevField } from "./Helper";
import Paragraph from "./Paragraph";
import Cell from "./Cell";
import { Direction } from "./Definition";
import InputAdapter from "./InputAdapter";
import { contentChanged } from "./Decorator";
import EditorHelper from "./EditorHelper";
import { ShapeMode } from "./Constant";
import { isTable } from "./Utils";
import Table from "./Table";
import XField from "./XField";

let ime_start: number[] | null = null;
let bold: boolean = false;
let italic: boolean = false;
let underline: boolean = false;
type pointerEventName = "onPointerDown" | "onPointerMove" | "onPointerUp";
type keyboardEventName = "onKeyDown" | "onKeyUp";
type compositionEventName =
  | "onCompositionStart"
  | "onCompositionUpdate"
  | "onCompositionEnd";
type mouseEventName = "dblClick";
const pointerHandlers = {
  onPointerDown,
  onPointerMove,
  onPointerUp
};

const KeyboardHandlers = {
  onKeyDown,
  onKeyUp
};

const compositionHandlers = {
  onCompositionStart,
  onCompositionUpdate,
  onCompositionEnd
};

let requestAnimationFrameId: any = -1;

type EventType = PointerEvent | KeyboardEvent | InputEvent | CompositionEvent | MouseEvent;
type EventName = pointerEventName | keyboardEventName | compositionEventName | "onInput" | mouseEventName;

export default function (eventName: EventName, event: EventType, editor: Editor) {
  // editor.focus();

  if (event instanceof PointerEvent) {
    const method = pointerHandlers[eventName as pointerEventName];
    method(event, editor);
  }

  // if (event instanceof MouseEvent && event.type === "dblclick") {
  //   dblClick(event, editor);
  // }
  // TODO 打字一定会触发键盘事件(里边有判断，打字的时候键盘事件中什么逻辑都不会走) 中文的时候会触发键盘事件和 CompositionEvent 英文的时候会触发键盘事件和 InputEvent 是因为在 input 上绑定了这几个事件
  if (event instanceof KeyboardEvent) {
    const method = KeyboardHandlers[eventName as keyboardEventName];

    method(event, editor);
  }

  // 英文输入法的时候会走这个
  if (event instanceof InputEvent) {
    const result = EditorHelper.checkFormula(editor);
    if (!result) {
      return;
    }
    onInput(event, editor);
  }

  // 中文输入法的时候会走这个
  if (event instanceof CompositionEvent) {
    const result = EditorHelper.checkFormula(editor);
    if (!result) {
      return;
    }
    const method = compositionHandlers[eventName as compositionEventName];
    method(event, editor);
  }
}
let click_num: number = 1;
let time0: number, time1: number, time2: number;
time0 = time1 = time2 = 0;
let timeout: any; // 定时函数变量
let pointer_down_posi: any;
// let time, time1, time2 = 0;
function onPointerDown(event: PointerEvent, editor: Editor) {
  event.preventDefault();
  const { x, y } = editor.getNeedXYbyXY(event.offsetX, event.offsetY);
  if (event.button === 2) {
    if (editor.internal.draw_shape === ShapeMode.FoldLine) {
      return;
    }

    if (editor.isInSelectedArea(x, y) || editor.print_continue || editor.area_print) {
      return;
    }
  }
  // 处理连续选中复选框情况下会触发选区问题
  if (pointer_down_posi && (Math.abs(event.offsetX - pointer_down_posi.x) > 5 ||
    Math.abs(event.offsetY - pointer_down_posi.y) > 5)) {
    time0 = time1 = time2 = 0;
  }
  // 记录当前点击的坐标
  pointer_down_posi = { x: event.offsetX, y: event.offsetY };
  const result = editor.clickScrollBar(x, y); // TODO 是不是应该放到该方法的最开头
  if (result) {
    editor.hold_mouse = true;
    editor.internal.currentClickCommentPage = undefined;
    editor.internal.commentCurrentPage = undefined;
    return;
  }
  // 单击
  if (time0 && time1 && !time2) {
    // 三击
    time2 = new Date().getTime();
    click_num = 3;
    clearTimeout(timeout);
    timeout = setTimeout(() => {
      time0 = time1 = time2 = 0;
    }, 300);
    tripleClick(event, editor);
    editor.event.emit("tripleClick", event);
    // return;
  } else if (time0 && !time1 && !time2) {
    // 双击
    if (event.button !== 0) return;
    time1 = new Date().getTime();
    click_num = 2;
    clearTimeout(timeout);
    timeout = setTimeout(() => {
      time0 = time1 = 0;
    }, 300);
    dblClick(event, editor);
    // return;
  } else if (!time0 && !time1 && !time2) {
    time0 = new Date().getTime();
    click_num = 1;
    timeout = setTimeout(() => {
      time0 = 0;
    }, 300);

    editor.pointer_down(x, y, event.shiftKey);
    if (editor.isMobileTerminal()) {
      editor.internal.pointer_down_ev = event
    }
    if (editor.print_continue || editor.area_print) {
      return;
    }
    editor.event.emit("click", event, { x, y });
  }
}
function onPointerMove(event: PointerEvent, editor: Editor) {
  // if (click_num > 1) { // 解开会导致 双击 三击事件之后 光标样式不变
  //   return;
  // }
  // 解决光标移动时三连击从页眉到正文会报错及复选框连续选中问题。
  cancelAnimationFrame(editor.internal.holdMouseAnimationId);
  editor.internal.holdMouseAnimationId = requestAnimationFrame(() => {
    const { x, y } = editor.getNeedXYbyXY(event.offsetX, event.offsetY);
    const info = editor.pointer_move(x, y);
    editor.event.emit("pointerMove", event, { x, y, info });
  });
}
function onPointerUp(event: PointerEvent, editor: Editor) {
  if (click_num > 1) {
    return;
  }
  const { x, y } = editor.getNeedXYbyXY(event.offsetX, event.offsetY);
  editor.pointer_up(x, y, event);
  editor.event.emit("pointerUp", event);
}
function dblClick(event: MouseEvent, editor: Editor) {
  const { x, y } = editor.getNeedXYbyXY(event.offsetX, event.offsetY);
  editor.internal.dblclick(x, y);
  if (editor.print_continue || editor.area_print) {
    return;
  }
  editor.event.emit("dblClick", event, { x, y });
}
function tripleClick(event: MouseEvent, editor: Editor) {
  editor.internal.tripleClick(event);
}

function onKeyDown(event: KeyboardEvent, editor: Editor) {
  if (editor.internal.is_in_field_drag) {
    return
  }
  editor.isPastedFromKeyboard = false; // 开始就要置为 false 只有确定按下了 ctrl + v 的时候才改为 true 因为手写输入也走键盘事件也走该方法了
  editor.caret.show = true;
  const key = event.key;

  // 触发事件前判断是否需要执行底层逻辑
  const isForbidExe = editor.event.emit("beforeKeydown", event);
  if (!isForbidExe) return;

  // const print_num: number[] = [];
  if (event.ctrlKey || event.metaKey) {
    if (key.toLocaleLowerCase() !== "v") { // 不知道 阻止默认行为有没有额外的损失就先 在不是 ctrl + v 的时候阻止吧 否则就把 paste 事件也给阻止掉了
      event.preventDefault();
    }
    event.stopPropagation();

    switch (key.toLocaleLowerCase()) {
      case "a":
        editor.selectAll();
        break;
      case "b":
        bold = !bold
        editor.change_font_style({ bold: bold });
        break;
      case "c":
        editor.selection.copy();
        // 去掉ctrl+shift+c的快捷键
        // if (event.shiftKey) {
        //   editor.selection.copy();
        // } else {
        //   editor.selection.copy();
        // }
        break;
      case "i":
        italic = !italic
        editor.change_font_style({ italic: italic });
        break;
      case "p":
        editor.print("browserPrint");
        break;
      case "u":
        underline = !underline
        editor.change_font_style({ underline: underline });
        break;
      case "v":
        editor.isPastedFromKeyboard = true;
        // 分组锁定不可编辑
        if (event.shiftKey) {
          editor.paste(false, true);
        } else if (event.altKey) {
          editor.paste(false, false, true);
        } else {
          editor.paste(false, false);
        }
        break;
      case "x":
        // 分组锁定不可编辑
        editor.cut();
        break;
      case "y":
        redo(editor);
        break;
      case "z":
        undo(editor);
        break;
      case "home":
        if (event.shiftKey) {
          editor.selection.setCursorByContainerType("doc", "start", false, true);
        } else {
          editor.selection.setCursorByContainerType("doc", "start");
        }
        return;
      case "end":
        if (event.shiftKey) {
          editor.selection.setCursorByContainerType("doc", "end", false, true);
        } else {
          editor.selection.setCursorByContainerType("doc", "end");
        }
        return;
      case "tab": // 只支持众阳浏览器
        editor.tabDown();
        return;
      case "delete":
        editor.deleteRowFromTbl();
        return;
    }
  }
  if (event.shiftKey) {
    // 按下去的时候记录一下当前的光标位置
    if (!editor.selection.isCollapsed) {
      if (!editor.internal.originStartPath.length) {
        if (key === "ArrowLeft" || key === "ArrowUp") {
          editor.internal.originStartPath = editor.selection.para_end;
        } else if (key === "ArrowRight" || key === "ArrowDown") {
          editor.internal.originStartPath = editor.selection.para_start;
        }
      }
    } else {
      if (!editor.internal.originStartPath.length) {
        editor.internal.originStartPath = editor.selection.focus;
      }
    }
    switch (key.toLocaleLowerCase()) {
      case "home":
        editor.selection.setCursorByContainerType("row", "start", false, true);
        return;
      case "end":
        editor.selection.setCursorByContainerType("row", "end", false, true);
        return;
      case "delete":
        editor.deleteColFromTbl();
        return;
    }
  }
  preventDefaultByKeys(["Tab"], event);
  stopPropagationByKeys(["Tab", "Backspace", "Delete", "ArrowLeft", "ArrowRight", "ArrowUp", "ArrowDown", "Enter"], event);

  const mainExe = ()=>{
    const focus = editor.selection.focus;
    if (key === "Backspace") {
      // 分组锁定不可编辑
      if (!editor.operableOrNot(["cell", "group"])) return false;
      editor.delete_backward();
      editor.delete_watermark();
      editor.delete_shape();
      editor.event.emit("exeCommand", { command: "deleteBackward" }, event);
      return;
    }

    if (key === "Delete") {
      // 分组锁定不可编辑
      if (!editor.operableOrNot(["cell", "group"])) return false;
      const current_row = editor.selection.getFocusRow();
      if (editor.current_cell.children.length === 1 && !current_row.children.length) return;
      if (!current_row.parent.parent && current_row.cell_index === 0 && current_row.children.length === 0 && !current_row.parent.hf_part && isTable(current_row.next_container)) {
        // delete 删除表格上方空行
        editor.selection.setCursorPosition([1, 0, 0, 0]);
        editor.deleteEmtptyParagraphOnTblSide(Direction.up);
      } else {
        // 不是删除表格上方空行的时候 再走正常的删除 逻辑
        editor.delete_forward();
        editor.delete_watermark();
        editor.delete_shape();
      }

      editor.event.emit("exeCommand", { command: "deleteForward" }, event);
      return;
    }

    if (key === "ArrowLeft") {
      if (event.shiftKey) {
        if (!editor.selection.isCollapsed) {
          editor.selection.setCursorPosition(editor.selection.focus);
        }
        editor.caret_move("left");
        editor.selection.setSelectionByPath(editor.internal.originStartPath, editor.selection.focus, "model_path");
        editor.render();
      } else {
        editor.caret_move("left");
      }
      editor.event.emit("exeCommand", { command: "caretMove" }, event);

      return;
    }

    if (key === "ArrowRight") {
      if (event.shiftKey) {
        if (!editor.selection.isCollapsed) {
          editor.selection.setCursorPosition(editor.selection.focus);
        }
        editor.caret_move("right");
        editor.selection.setSelectionByPath(editor.internal.originStartPath, editor.selection.focus, "model_path");
        editor.render();
      } else {
        editor.caret_move("right");
      }
      editor.event.emit("exeCommand", { command: "caretMove" }, event);

      return;
    }

    if (key === "ArrowUp") {
      if (event.shiftKey) {
        if (!editor.selection.isCollapsed) {
          editor.selection.setCursorPosition(editor.selection.focus);
        }
        editor.caret_move("up");
        editor.selection.setSelectionByPath(editor.internal.originStartPath, editor.selection.focus, "model_path");
        editor.render();
      } else {
        editor.caret_move("up");
      }
      editor.event.emit("exeCommand", { command: "caretMove" }, event);

      return;
    }

    if (key === "ArrowDown") {
      if (event.shiftKey) {
        if (!editor.selection.isCollapsed) {
          editor.selection.setCursorPosition(editor.selection.focus);
        }
        editor.caret_move("down");
        editor.selection.setSelectionByPath(editor.internal.originStartPath, editor.selection.focus, "model_path");
        editor.render();
      } else {
        editor.caret_move("down");
      }
      editor.event.emit("exeCommand", { command: "caretMove" }, event);
      return;
    }
    if (key === "Enter") {
      // 分组锁定不可编辑
      if (!editor.operableOrNot(["cell", "group"])) return false;
      const currentCell = editor.focusElement.cell;
      const currentTable = editor.focusElement.table;
      if (currentCell && currentTable && event.shiftKey) {
        if (focus[focus.length - 2] !== 0 || focus[focus.length - 1] !== 0) {
          currentTable.addRowOrCol(focus, Direction.down, editor); // 这里需要重置光标了
          for (let i = focus[focus.length - 3]; i < currentTable.children.length; i++) {
            const cell = currentTable.children[i];
            if (cell.position[0] === currentCell.position[0] + (currentCell.rowspan - 1) + 1 && cell.position[1] === currentCell.position[1]) {
              focus.splice(1, 3, i, 0, 0);
              editor.selection.setCursorPosition(focus);
            }
          }
        } else {
          currentTable.addRowOrCol(focus, Direction.up, editor); // 这里不需要重置光标
        }
        editor.update(...editor.getUpdateParamsByContainer(currentTable));
        editor.render();
      } else {
        editor.enterDown();
      }
    }
    //TODO tang 抽出来然后增加表单模式的列跳转
    if (key === "Tab") {
      // 分组锁定不可编辑
      if (editor.selection.getFocusGroup()?.lock) return;
      // const cellWithCaret = editor.selection.getFocusCell()?.getOrigin();
      if (editor.config.fastEntryMode && editor.selection.focus.length !== 4) {
        handleFastEntryMode(editor, event)
        return
      } else {
        const cellWithCaret = editor.selection.getFocusCell()?.getOrigin();
        if (cellWithCaret) {
          const index = getCellIndex(cellWithCaret);
          const lastCellIndex = (cellWithCaret.parent!.children as any).length - 1;
          const originTable = cellWithCaret.parent!.getOrigin();
          if (event.shiftKey) {
            handleTabWithShiftKey(editor, cellWithCaret, originTable, index)
            return
          }
          // 当前表格里边如果有文本域 就先进文本域
          // 应该是在该单元格里边 光标位置后边还有文本域的话 才设置光标到文本域里边去
          const caret_next_field = getNextField(cellWithCaret, editor.selection.para_focus);
          if (caret_next_field) {
            // 如果光标没在文本域里边 但是 该单元格里边又有文本域 就应该放到文本域里边去
            editor.locatePathInField(caret_next_field, "start");
          } else { // 不进文本域的话 才走到下一个单元格里边去 下一个单元格不能是锁定的 所以要找下一个能跳的单元格跳过去
            if (index === lastCellIndex && editor.view_mode !== "form") {
              const cursor_position = editor.selection.focus;
              cursor_position.splice(1, 3, index, 0, 0); // 重置下光标位置 因为插入行和列是根据 selection.start 来的 选区情况下 光标就不会在最后一个单元格了 所以也就不会在末尾追加一行了
              editor.selection.setCursorPosition(cursor_position);
              // 光标在最后一个单元格了 就插入一行
              // editor.addRowOrColInTbl(Direction.down);
              originTable.addRowOrCol(cursor_position, Direction.down, editor);
              editor.update(...editor.getUpdateParamsByContainer(originTable));
              // 不能 return 因为光标依然会留在 上边设置的位置处 要经过下边的光标位置更新
            }
            if (originTable.skipMode === "row" || !originTable.skipMode) {
              handleRowTab(editor, originTable, index)
            } else if (originTable.skipMode === "col") {
              handleColTab(editor, originTable, cellWithCaret)
            }

          }
          editor.updateCaret();
          editor.render();
          return;
        }
        // 屏蔽按键原有功能，比如tab键会将焦点聚焦到浏览器最上面的元素上
        // 然后更舒适的执行自己定义功能
        event.returnValue = false;
        // 判断
        editor.tabDown();
      }
    }

    if (key === "Home") {
      // 光标定位到行首
      editor.selection.setCursorByContainerType("row", "start");
    }

    if (key === "End") {
      // 光标定位到行尾
      editor.selection.setCursorByContainerType("row", "end");
    }
  }
  // 解决模拟事件触发太快导致输入数字丢失问题
  if(event.isTrusted){
    // 优化连续键盘事件卡顿问题
    cancelAnimationFrame(requestAnimationFrameId);
    requestAnimationFrameId = requestAnimationFrame(() => {
      mainExe()
    });
  }else{
    mainExe()
  }

}

function handleFastEntryMode(editor: Editor, event: KeyboardEvent) {
  const fields = editor.sortAllFields();
  const curParaPath = editor.modelPath2ParaPath(editor.selection.focus);
  const curField = editor.selection.getFieldByPath(curParaPath);
  const insertCursorPosition = event.shiftKey ? "end" : "start";
  if (event.shiftKey) {
    fields.reverse();
    if (!curField) {
      for (let i = curParaPath[0]; i >= 0; i--) {
        const preField = getPrevField(editor.current_cell, curParaPath);
        if (preField) {
          const index = fields.indexOf(preField);
          for (let i = index; i < fields.length; i++) {
            const field = fields[i];
            if (!field.isReadonly && !field.cell.lock) {
              editor.locatePathInField(field, insertCursorPosition);
              editor.updateCaret();
              editor.scroll_by_focus();
              editor.render();
              return;
            }
          }
        }
      }
    }
  }
  if (curField) {
    const index = fields.indexOf(curField);
    for (let i = index + 1; i < fields.length; i++) {
      const nextField = fields[i];
      if (nextField && !nextField.isReadonly && !nextField.cell.lock) {
        editor.locatePathInField(fields[i], insertCursorPosition);
        break;
      }
    }
  } else {
    for (let i = curParaPath[0]; i < editor.current_cell.paragraph.length; i++) {
      const nextField = getNextField(editor.current_cell, curParaPath);
      if (nextField) {
        const index = fields.indexOf(nextField);
        for (let i = index; i < fields.length; i++) {
          const field = fields[i];
          if (!field.isReadonly && !field.cell.lock) {
            editor.locatePathInField(field, insertCursorPosition);
            editor.updateCaret();
            editor.scroll_by_focus();
            editor.render();
            return;
          }
        }
      }
    }
  }
  editor.updateCaret();
  editor.scroll_by_focus();
  editor.render();
}

function handleTabWithShiftKey(editor: Editor, cellWithCaret: Cell, originTable: Table, index: number) {
  const caret_prev_field = getPrevField(cellWithCaret, editor.selection.para_focus);
  if (caret_prev_field) {
    editor.locatePathInField(caret_prev_field, "start");
  } else {
    if (originTable.skipMode === "col") {
      handleColShiftTab(editor, originTable, cellWithCaret);
    } else {
      handleRowShiftTab(editor, originTable, index)
    }
  }
  editor.updateCaret();
  editor.render();
}

function handleColTab(editor: Editor, originTable: Table, cellWithCaret: Cell) {
  let arr = [...editor.selection.focus];
  const curPosition = cellWithCaret.position;
  const curRowPosition = curPosition[0];
  const curColPosition = curPosition[1]
  let foundEditableCell = false;

  for (let i = curRowPosition + 1; i <= originTable.row_size.length; i++) {
    const nextCell = originTable.getCellByPosition(i, curColPosition);
    if (nextCell && !nextCell.lock) {
      arr = [...nextCell?.start_path];
      foundEditableCell = true;
      break;
    } else if (i === originTable.row_size.length) {
      for (let col = curColPosition + 1; col < originTable.col_size.length; col++) {
        for (let row = 0; row < originTable.row_size.length; row++) {
          const nextCell = originTable.getCellByPosition(row, col);
          if (nextCell && !nextCell.lock) {
            if (editor.view_mode === "form") {
              if (nextCell.fields.length) {
                arr = [...nextCell?.start_path];
                foundEditableCell = true;
                break;
              }
            } else {
              arr = [...nextCell?.start_path];
              foundEditableCell = true;
              break;
            }
          }
        }

        if (foundEditableCell) {
          break;
        }
      }
    }
  }
  if (editor.view_mode === "form") {
    handleFormModeTab(editor, originTable, arr)
  } else {
    editor.selection.setCursorPosition(arr);
  }
}
function handleRowTab(editor: Editor, originTable: Table, index: number) {
  let arr = [...editor.selection.focus];
  for (let i = index + 1; i < originTable.children.length; i++) {
    const cell = originTable.children[i];
    if (!cell.lock) {
      if (editor.view_mode === "form") {
        if (cell.fields.length) {
          arr = cell.start_path
          break
        } else {
          continue
        }
      } else {
        arr.splice(1, 3, i, 0, 0);
        break;
      }
    }

    if (i === originTable.children.length - 1) return;
  }

  if (editor.view_mode === "form") {
    handleFormModeTab(editor, originTable, arr)
  } else {
    editor.selection.setCursorPosition(arr);
  }
}
function handleFormModeTab(editor: Editor, originTable: Table, arr: any) {
  let curCell
  curCell = originTable.children[arr[1]]
  if (curCell) {
    const fields = curCell.fields
    let firstField
    for (let i = 0; i < fields.length; i++) {
      const field = fields[i]
      if (field) {
        firstField = field
        break
      }
    }
    if (firstField) editor.locatePathInField(firstField, "start");
  }
}
function handleFormModeShiftTab(editor: Editor, originTable: Table, arr: any) {
  let curCell
  curCell = originTable.children[arr[1]]
  if (curCell) {
    const fields = curCell.fields
    let firstField
    for (let i = fields.length - 1; i > 0; i--) {
      const field = fields[i]
      if (field) {
        firstField = field
        break
      }
    }
    if (firstField) editor.locatePathInField(firstField, "start");
  }
}

function handleColShiftTab(editor: Editor, originTable: Table, cellWithCaret: Cell) {
  let arr = [...editor.selection.focus];
  const curPosition = cellWithCaret.position;
  const curRowPosition = curPosition[0];
  const curColPosition = curPosition[1];

  if (curColPosition === 0 && curRowPosition === 0) return;
  let firstTime = true

  for (let i = curColPosition; i >= 0; i--) {

    let startIndex = firstTime ? curRowPosition : originTable.row_size.length
    for (let j = startIndex; j >= 0; j--) {
      firstTime = false
      const nextCell = originTable.getCellByPosition(j - 1, i);
      if (nextCell && !nextCell.lock) {
        if (editor.view_mode === "form" && !nextCell.fields.length) {
          continue;
        }
        arr = [...nextCell?.start_path];
        const endParaPath = editor.modelPath2ParaPath([
          arr[0],
          nextCell.index,
          nextCell.paragraph.length - 1,
          (nextCell.paragraph[nextCell.paragraph.length - 1] as Paragraph).characters.length - 1,
        ]);
        const prevField = getPrevField(nextCell, endParaPath);
        if (prevField) {
          editor.locatePathInField(prevField, "start");
        } else {
          if (editor.view_mode === "form") {
            handleFormModeShiftTab(editor, originTable, arr);
          } else {
            editor.selection.setCursorPosition(arr);
          }
        }
        return;
      }
    }
  }
}

function handleRowShiftTab(editor: Editor, originTable: Table, index: number) {
  let arr = [...editor.selection.focus];
  for (let i = index - 1; i >= 0; i--) {
    const cell = originTable.children[i];
    if (!cell.lock) {
      if (editor.view_mode === "form") {
        if (cell.fields.length) {
          arr = cell.end_path
        } else {
          continue
        }
      }
      const end_para_path = editor.modelPath2ParaPath([arr[0], cell.index, cell.paragraph.length - 1, (cell.paragraph[cell.paragraph.length - 1] as Paragraph).characters.length - 1]);
      const prev_field = getPrevField(cell, end_para_path);
      if (prev_field) {
        editor.locatePathInField(prev_field, "start");
        break;
      }
      arr.splice(1, 3, i, 0, 0);
      if (editor.view_mode === "form") {
        handleFormModeShiftTab(editor, originTable, arr)
      } else {
        editor.selection.setCursorPosition(arr);
      }
      break;
    }
    if (i === originTable.children.length - 1) return;
  }
}

function onKeyUp(event: KeyboardEvent, editor: Editor) {
  if (event.key === "Shift") {
    editor.internal.originStartPath = [];
  }
  if (event.ctrlKey) {
    if (event.key === "Alt") {
      editor.internal.is_in_field_drag = false
      const field = editor.internal.focus_drag_field
      if (field) {
        field.showPoint = false
        editor.internal.focus_drag_field = null
        editor.reviseFieldAttr({ field: field })
      }
      editor.internal.is_point_down = false
    }
  } else if (event.altKey) {
    if (event.key === "Control") {
      editor.internal.is_in_field_drag = false
      const field = editor.internal.focus_drag_field
      if (field) {
        field.showPoint = false
        editor.internal.focus_drag_field = null
        editor.reviseFieldAttr({ field: field })
      }
      editor.internal.is_point_down = false
    }
  }
  // const key = event.key;
  // (key === "Meta" || key === "Control") && (editor.is_pressing_ctrl = false);
  // (key === "Shift") && (shift = false);
}

function onInput(event: InputEvent, editor: Editor) {
  // 防止偶发情况下按下Delete键先执行input会输入个null
  if (event.data === null || event.data === undefined) {
    return;
  }
  const mainExe = ()=>{
    // 分组锁定不可编辑
    if (!editor.operableOrNot(["cell", "group"])) return false;
    editor.internal.is_mobile_edit = false
    editor.internal.VL.is_mobile_button = false
    editor.insertText(event.data as string, "input");
    editor.event.emit("input", event);
    XField.handleDateFieldInput(editor,event.data)
  }
  if(event.isTrusted){
    // 优化连续键盘事件卡顿问题
    // 此处不能使用cancelAnima.. 扫码枪快速录入会丢失内容
    // cancelAnimationFrame(requestAnimationFrameIdInput);
    requestAnimationFrame(() => {
      mainExe()
    });
  }else{
    mainExe()
  }
}
function onCompositionStart(event: CompositionEvent, editor: Editor) {
  editor.internal.is_mobile_edit = false
  editor.internal.VL.is_mobile_button = false
  editor.event.emit("compositionStart", event);
  if (editor.config.useLetterPlaceholder) {
    editor.delSectionRecordStack(true); // 不做判断 是为了往历史堆栈里边记录一遍内容 因为输入中文的时候，会不断的插入拼音，那时候不需要往历史堆栈中记录，确认的时候记录内容就少了刚开始的数据，所以这里要记录一下
    ime_start = editor.modelPath2ParaPath(editor.selection.start);
  }
}

function onCompositionUpdate(event: CompositionEvent, editor: Editor) {
  if (event.data && editor.config.useLetterPlaceholder) {
    // 优化连续键盘事件卡顿问题
    cancelAnimationFrame(requestAnimationFrameId);
    requestAnimationFrameId = requestAnimationFrame(() => {
      InputAdapter.onCompositionInputHelper(editor, ime_start ? editor.paraPath2ModelPath([...ime_start]) : ime_start, event.data as string, false);
    });
  }
}

function onCompositionEnd(event: CompositionEvent, editor: Editor) {
  event.preventDefault();
  // 防止偶发情况下按下Delete键先执行input会输入个null
  if (event.data === null || event.data === undefined) {
    return;
  }
  cancelAnimationFrame(requestAnimationFrameId);
  requestAnimationFrameId = requestAnimationFrame(() => {
    if (editor.config.useLetterPlaceholder) { // 不能挪到 onCompositionStart 上边去，否则撤销重做会有问题
      ime_start = ime_start ? editor.paraPath2ModelPath([...ime_start]) : null;
    } else {
      ime_start = null;
    }
    if (editor.config.useLetterPlaceholder) {
      InputAdapter.onCompositionInputHelper(editor, ime_start, event.data as string, true);
    } else {
      editor.onCompositionInputHelper(ime_start, event.data as string, true);
    }
    // 因为开启拼音之后输入中文确认的时候就不走装饰器了
    if (editor.config.useLetterPlaceholder) {
      contentChanged(editor, "onCompositionEnd");
    }
    editor.event.emit("compositionEnd", event);
    ime_start = null;
  });
}

/**
   * 阻止传入的 keys 按键列表的默认行为
   * @param keys 按键名称数组
   * @param event 按键事件
   */
function preventDefaultByKeys(keys: string[], event: KeyboardEvent) {
  const current = event.key;
  if ((keys as any).includes(current)) {
    event.preventDefault();
  }
}

/**
   * 阻止传入的 keys 按键列表的冒泡行为
   * @param keys 按键名称数组
   * @param event 按键事件
   */
function stopPropagationByKeys(keys: string[], event: KeyboardEvent) {
  const current = event.key;
  if ((keys as any).includes(current)) {
    event.stopPropagation();
  }
}
