import dataDefi from "../../dataDefin.json";
import tableFillExample from "../../tableFillExample.json";
const dataSetMixIn = {
  data() {
    return {
      showDataSetModal: false,
      isShowRealValue: false, // 显示真实值
      isInitExtendBtn: false,
      isPrintDesign: false, // 是打印平台文档设计器
      dataSetInfo: {
        title: "数据集",
        optionTitle: "字段",
        optionTip: `字段前的图标代表该字段值对应的类型,可通过拖拽或者点击插入文本域,字段会自动与文本域绑定并自动设置文本域类型`,
        dataSets: [],
      },
      dictSetInfo: {
        title: "字典集",
        optionTitle: "选项",
        optionTip:
          "选项前图标绿色代表与字段存在关联关系，可通过拖拽直接插入一组选项，如果排版不符合要求则展开后一项一项插入;黑色图标则需要将字典选项拖拽到字段文本域中才能自动绑定字段并转换为相对应的选择项。",
        dataSets: [],
      },
      descDataInfo: {},
      oriInfo: {},
      oriDataSet: {},
      allDataSetTree: {},
      allDictSetTree: {},
      sameCodeChildrenMap: {},
      allDataItemCodeMap: {},
      initRightTreeData: [],
      sysInfo: {
        userSysId: "",
        userId: "001",
        userName: "众阳健康管理员",
        deptId: "",
        deptName: "",
        deptCode: "",
        hospitalName: "众阳健康测试医院",
        hospitalId: "10000",
        systemName: "打印平台",
        host: "",
      },
      sysInfoDesc: {
        userSysId: "用户系统ID",
        userId: "用户ID",
        userName: "用户名称",
        deptId: "科室ID",
        deptName: "科室名称",
        deptCode: "科室编码",
        hospitalId: "医院ID",
        hospitalName: "医院名称",
        systemName: "系统名称",
        host: "客户端IP",
      },
      descSuffix: "-$desc$",
    };
  },
  created() {
    if (this.rightMenuConfig.treeData) {
      this.dictSetInfo.optionTip = "";
      this.dictSetInfo.optionTitle = "文本域";
      this.initRightTreeData = this.rightMenuConfig.treeData;
    }
  },
  mounted() {
    if (this.instance && this.instance.localTest.useLocal) {
      this.initDataSet(dataDefi);
      this.updateCustomFieldDataSet();
    }
    // this.rightMenuConfig["draggable"] = true;
    // this.rightMenuConfig["treeData"] = initTreeData;
  },
  methods: {
    openManageDataSetModal() {
      this.showDataSetModal = true;
    },
    updateCustomFieldDataSet() {
      const { editor } = this.instance;
      const customFields = editor.document_meta.customField;
      if (!Array.isArray(customFields)) {
        return;
      }
      const customField = {};
      const customFieldDesc = {};
      for (let i = 0, len = customFields.length; i < len; i++) {
        const name = customFields[i].fieldName;
        const desc = customFields[i].description;
        customField[name] = "";
        customFieldDesc[name] = desc;
      }
      if (this.oriInfo && this.oriInfo.data) {
        this.oriInfo.data["自定义字段"] = customField;
        this.oriInfo.data["自定义字段-desc"] = customFieldDesc;
        this.initDataSet(this.oriInfo);
      }
    },
    updateDataSet(newDataSet) {
      if (this.oriInfo) {
        this.oriInfo.data = newDataSet;
      }
      this.initDataSet(this.oriInfo);
      setTimeout(() => {
        this.instance.editor.updateCanvasSize();
      }, 100);
      this.closeDataSetModal();
    },
    closeDataSetModal() {
      this.showDataSetModal = false;
    },
    extendBaseBtnUsedByDataSet() {
      if (this.isInitExtendBtn) {
        return;
      }
      this.isInitExtendBtn = true;
      // 存在诸多问题，先注释
      // this.baseStartBtn.push(
      //   { type: "line" },
      //   {
      //     type: "icon",
      //     icon: "icon-huyanmoshi",
      //     title: "显示文本域真实值",
      //     key: "showRealVal",
      //     func: this.showRealValue,
      //   }
      // );
    },
    showRealValue() {
      this.isShowRealValue = !this.isShowRealValue;
      this.customSelected["showRealVal"] = this.isShowRealValue;
      this.$forceUpdate();
      const { editor } = this.instance;
      if (this.isShowRealValue) {
        this.fillContentByReceiveJsonData(editor);
      } else {
        this.clearFilledContent(editor);
      }
    },
    determineType(value) {
      // 如果值是对象且有 `value` 属性，取其值
      if (value && typeof value === "object") {
        value = value.value;
      }

      // 如果值为空或无效，默认返回 "string"
      if (value === null || value === undefined) {
        return "string";
      }

      // 尝试将字符串解析为数字
      const valueStr = String(value);
      if (/^\d+(\.\d+)?$/.test(valueStr)) {
        return valueStr.includes(".") ? "number" : "string";
      }

      // 尝试将字符串解析为日期
      const parsedDate = new Date(value);
      if (
        !isNaN(parsedDate) &&
        parsedDate.toString() !== "Invalid Date" &&
        valueStr !== "-1"
      ) {
        return "date";
      }

      // 默认返回 "string"
      return "string";
    },
    initDataSet(dataSets) {
      this.oriInfo = dataSets;
      if (!dataSets) {
        return;
      }
      const editorLocalDataSet = localStorage.getItem("EditorLocalDataSet");
      if (editorLocalDataSet) {
        dataSets.data = Object.assign(
          {},
          dataSets.data,
          JSON.parse(editorLocalDataSet)
        );
      }
      this.isPrintDesign = true;
      this.dataSetInfo.dataSets = [];
      this.dictSetInfo.dataSets = [];
      dataSets.data["系统变量"] = this.sysInfo;
      dataSets.data["系统变量-desc"] = this.sysInfoDesc;
      this.handleInnerVar(dataSets);
      this.oriDataSet = dataSets.data;
      this.dataColumnType = dataSets.dataColumnType;
      if (dataSets.data && Object.keys(dataSets.data).length) {
        this.extendBaseBtnUsedByDataSet();
      }
      const data = Object.assign({}, this.oriDataSet, this.dataColumnType);
      let initDataSetId = "";
      let initDictSetId = "";
      for (const key in data) {
        // 处理字段说明数据集
        if (key.endsWith("-desc")) {
          this.handleDescSets(key);
        }
      }
      for (const key in data) {
        if (!key.endsWith("-dict") && !key.endsWith("-desc")) {
          this.dataSetInfo.dataSets.push({
            name: key,
            value: key,
          });
          if (!initDataSetId) {
            initDataSetId = key;
          }
          this.handleDesignFields(key);
        }
      }
      for (const key in data) {
        if (key.endsWith("-dict")) {
          if (!initDictSetId) {
            initDictSetId = key;
          }
          this.dictSetInfo.dataSets.push({
            name: key,
            value: key,
          });
          this.handleDictSets(key);
        }
        // 处理字段说明数据集
        if (key.endsWith("-desc")) {
          this.handleDescSets(key);
        }
      }
      this.handleDataSetChildren();
      this.leftPanelConfig.treeData = this.allDataSetTree[initDataSetId];
      if (initDictSetId) {
        this.rightMenuConfig.treeData = this.allDictSetTree[initDictSetId];
      } else {
        this.rightMenuConfig.treeData = this.initRightTreeData;
      }
    },
    handleInnerVar(dataSets) {
      const sysVar = this.instance.sysVariables;
      const editorInnerVar = {};
      const editorInnerVarDesc = {};
      for (const key in sysVar) {
        if (key === "page_number") {
          editorInnerVar[sysVar[key]] = 0;
          editorInnerVarDesc[sysVar[key]] = "页";
        }
        if (key === "page_count") {
          editorInnerVar[sysVar[key]] = 0;
          editorInnerVarDesc[sysVar[key]] = "总页数";
        }
        if (key === "serial_number") {
          editorInnerVar[sysVar[key]] = 1;
          editorInnerVarDesc[sysVar[key]] = "序号";
        }
        if (key === "system_time") {
          editorInnerVar[sysVar[key]] = "2024-03-15";
          editorInnerVarDesc[sysVar[key]] = "系统时间";
        }
      }
      // 处理内置变量
      dataSets.data["内置变量"] = editorInnerVar;
      dataSets.data["内置变量-desc"] = editorInnerVarDesc;
    },
    handleDataSetChildren() {
      const dataSet = this.allDataSetTree;
      for (const key in dataSet) {
        const list = dataSet[key];
        for (let i = 0, len = list.length; i < len; i++) {
          const item = list[i];
          const children = this.sameCodeChildrenMap[item.code];
          if (children) {
            item.children = JSON.parse(JSON.stringify(children));
          }
        }
      }
    },
    handleDictSets(dataSetId) {
      const data = this.oriDataSet[dataSetId];
      const treeData = [];
      if (data) {
        this.handleDictTreeData(dataSetId, treeData, data);
        this.allDictSetTree[dataSetId] = treeData;
        this.$forceUpdate();
      }
    },
    // 处理描述字段
    handleDescSets(dataSetId) {
      let data = this.oriDataSet[dataSetId];
      if (data) {
        dataSetId = dataSetId.replace("-desc", "");
        if (Array.isArray(data)) {
          data = data[0];
        }
        for (const key in data) {
          this.descDataInfo[dataSetId + ">" + key] = data[key];
        }
      }
    },
    groupBy(array, key) {
      return array.reduce((result, item) => {
        (result[item[key]] = result[item[key]] || []).push(item);
        return result;
      }, {});
    },
    handleDictTreeData(dataSetId, treeData, data) {
      const dictObj = this.groupBy(data, "name");
      dataSetId = dataSetId.replace("-dict", "");
      for (const key in dictObj) {
        const code = dataSetId + ">" + key;
        const children = dictObj[key].map((ele) => {
          return {
            title: ele.label + "-" + ele.value,
            code: code + ">" + ele.value,
            optionType: ele.optionType,
            value: ele.value,
            pCode: code,
          };
        });
        let vsField = false;
        if (this.allDataItemCodeMap[code]) {
          this.sameCodeChildrenMap[code] = children;
          vsField = true;
        }
        const node = {
          title: key,
          code,
          vsField,
          pCode: dataSetId,
          slots: { icon: vsField ? "dict1" : "dict0" },
          children,
        };
        treeData.push(node);
      }
      this.allDictSetTree[dataSetId] = treeData;
    },
    judgeDataSetIsEmpty(dataSet) {
      if (!dataSet) return false;
      if (Array.isArray(dataSet)) {
        return !dataSet.length;
      } else if (typeof dataSet === "object") {
        return !Object.keys(dataSet).length;
      }
      return false;
    },
    handleDesignFields(dataSetId) {
      let data;
      if (
        this.oriDataSet &&
        !this.judgeDataSetIsEmpty(this.oriDataSet[dataSetId])
      ) {
        data = this.oriDataSet[dataSetId];
      } else if (this.dataColumnType && this.dataColumnType[dataSetId]) {
        data = this.dataColumnType[dataSetId];
      }
      const treeData = [];
      if (data) {
        this.handleTreeData(dataSetId, treeData, data);
        this.allDataSetTree[dataSetId] = treeData;
        this.$forceUpdate();
      }
    },
    handleChildTreeData(parentCode, pNode, item) {
      for (let i = 0; i < item.length; i++) {
        const ele = item[i];
        const node = {
          title: ele.name,
          code: parentCode + ">" + i,
          value: ele.value,
          pCode: parentCode,
        };
        pNode.push(node);
      }
    },
    handleTreeData(parentCode, pNode, item) {
      if (Array.isArray(item)) {
        item = item[0];
      }
      for (const key in item) {
        if (key.endsWith(this.descSuffix)) {
          continue;
        }
        const fieldType = this.determineType(item[key]);
        const code = parentCode + ">" + key;
        let desc = this.descDataInfo[code] ? this.descDataInfo[code] : "";
        desc = item[key + this.descSuffix] ? item[key + this.descSuffix] : desc;
        const node = {
          title: desc ? desc + " (" + key + ")" : key,
          code,
          desc,
          pCode: parentCode,
          fieldType,
          slots: { icon: fieldType },
          children: [],
        };
        this.allDataItemCodeMap[node.code] = true;
        pNode.push(node);
        if (!item[key]) {
          continue;
        }
        if (typeof item[key] === "object") {
          if (Array.isArray(item[key].children)) {
            this.handleChildTreeData(
              node.code,
              node.children,
              item[key].children
            );
          } else {
            this.handleTreeData(node.code, node.children, item[key]);
          }
        } else {
          node["value"] = item[key];
        }
      }
    },
    dragDrop() {
      if (!this.dragInfo) {
        return;
      }
      const { data, key, arr } = this.dragInfo;
      this.clickTreeMenu(data, key, arr);
      this.dragInfo = null;
    },
    treeMenuDragstart(data, key, arr) {
      this.dragInfo = {
        data,
        key,
        arr,
      };
    },
    clickTreeMenu(data, key, arr) {
      const code = data.code ? data.code : data.title ? data.title : "";
      const { editor } = this.instance;
      let insertedField = editor.insertField({
        type: "normal",
        placeholder: data.title,
        name: code,
        style: {
          bold: false,
        },
      });
      const focusField = editor.selection.getFocusField();
      if (
        focusField &&
        focusField.type === "box" &&
        data.vsField === undefined
      ) {
        // 如果字段拖拽位置是box，则将替换box关联字段
        focusField.parent_box.name = code;
        this.$editor.info(`已替换关联字段为【${code}】`);
      }
      if (typeof insertedField === "object") {
        let pData = null;
        if (arr) {
          pData = arr[arr.length - 2];
          editor.focus();
        }
        this.instance.showFloatBtn(insertedField, data, pData);
      }
    },
    test() {
      const editor = this.instance.editor;

      this.instance.scanAndAnalyze();

      // const fields = editor.getFieldsByName("finding");
      // debugger;
      // fields[0].setNewText(
      //   "彩色噶乐凯大街撒孤苦伶仃是\n\n\ndsklag 广东撒、、\n\n\ngdksa gewagd格但斯克啦发多少\n\n\n\n\n\n"
      // );
      // editor.updateFieldText({
      //   fields,
      //   append: true,
      // });

      // this.instance.openWordFile();
      // editor.setFieldsAsterisk(["a"], {
      //   type: "cut",
      //   truncPos: 2,
      //   truncPercent: 10,
      // });
      // const copyEditor = editor.copyEditor();
      // const data = localStorage.getItem("testRawData");
      // copyEditor.reInitRaw(data);
      // const base64 = copyEditor.print({ printRatio: 2 });
      // console.log(base64);
      // const fields = editor.getFieldsByName("test");
      // const res = editor.validFields(fields);
      // console.log(res, "打印文本域");
      // editor.locatePathInField(res[2]);
      // editor.refreshDocument();
      // editor.updateCaret();
      // editor.render();
      // editor.refreshDocument();
      // const data = localStorage.getItem("tt");
      // editor.reInitRaw(data);
      // editor.refreshDocument();
      // editor.insertChartPlaceholder();

      // // 插入排版 - 新版 ↓
      // const ratio = [3, 3, 4];

      // const srcs = [
      //   "https://img1.baidu.com/it/u=1394459067,3018342203&fm=253&fmt=auto&app=120&f=JPEG?w=353&h=500",
      //   "https://view-cache.book118.com/view3/M01/29/07/wKh2BF2nVC6AFjuyAAAYi5A8DEM243.png",
      //   "https://img0.baidu.com/it/u=1944966311,750301424&fm=253&fmt=auto&app=138&f=JPEG?w=500&h=500",
      //   "https://img0.baidu.com/it/u=405086877,2666710572&fm=253&fmt=auto&app=138&f=JPEG?w=500&h=500",
      //   "https://view-cache.book118.com/view3/M04/0A/20/wKh2BV2nVC6AW4GkAAAYeJykwPU260.png",
      // ];
      // const total = ratio.reduce((t, c) => t + c, 0);
      // const data = [];
      // for (let i = 0; i < total; i++) {
      //   const selectField = editor.createElement("field");
      //   selectField.type = "select";
      //   selectField.source_list = [
      //     {
      //       code: "检索1",
      //       text: "选项1",
      //     },
      //     {
      //       code: "检索2",
      //       text: "选项2",
      //     },
      //     {
      //       code: "检索3",
      //       text: "选项3",
      //     },
      //     {
      //       code: "检索4",
      //       text: "选项4",
      //     },
      //     {
      //       code: "检索5",
      //       text: "选项5",
      //     },
      //   ];
      //   data.push({
      //     src: srcs[Math.floor(Math.random() * srcs.length)],
      //     serialNum: i + "",
      //     selectField,
      //     meta: { id: Math.random(), t: "ce" },
      //     defaultCode: "检索1",
      //   });
      // }

      // const table = editor.insertLayout(ratio, data, 92, 74, 24, 50);
      // table.meta = { a: "ceshi", B: "测试用的b", c: ["c里边的数组元素1"] };
      // // 插入排版 ↑

      // const fields = editor.getFieldById("field-b2b5205e");
      // editor.reviseFieldAttr({
      //   fields: [fields],
      //   type: "select",
      //   inputMode: 0,
      //   source_list: [
      //     { text: "男", value: "男", code: "男" },
      //     { text: "女", value: "女", code: "女" },
      //   ],
      // });
      // this.instance.showSplitCellModal();
      // const fields = editor.getFieldsByName("A");
      // editor.highLightByFields({ isHighLight: true, name: "A" });

      // editor.splitCell();
      // debugger;
      // const fields = editor.getFieldsByName("info>operation_code1");
      // fields.forEach((field) => {
      //   field.max_width = 111;
      //   field.min_width = 111;
      //   editor.reviseFieldAttr(field);
      // });
      // editor.insertField({
      //   name: `date_${Date.now()}`, // 字段名称，使用时间戳确保唯一
      //   type: "date", // 类型：日期选择器
      //   label_text: "日期选择", // 标签文本
      //   placeholder: "请选择日期", // 占位提示文本

      //   // 权限属性
      //   canBeCopied: 0, // 设置为不可复制

      //   // 日期属性
      //   format: "YYYY-MM-DD HH:mm:ss", // 日期格式，精确到秒
      //   min_value: "2024-02-03", // 最小日期
      //   max_value: "2024-05-06", // 最大日期

      //   // 验证规则
      //   valid: 1, // 启用验证
      //   valid_content: {
      //     require: 1, // 必填
      //     type: "date",
      //     rule: {
      //       max_length: 19, // "YYYY-MM-DD HH:mm:ss" 的长度
      //       min_length: 19,
      //     },
      //   },

      //   // 显示属性
      //   show_format: 1, // 显示格式化
      // });

      // 校验↓
      // editor.validFields();
      // 校验↑

      // // 插入倾斜水印 ↓
      // editor.insertItalicMark("修改倾斜水印bug", {
      //   direction: "right",
      //   // module: ["pageWrite", "printView", "printPaper"],
      //   module: ["printPaper"],
      //   font: {
      //     //水印字体
      //     fontSize: 18,
      //     fontFamily: "华文彩云",
      //     opacity: 0.5,
      //     color: "red",
      //   },
      // });
      // // 插入倾斜水印 ↑
      // const data = JSON.parse(localStorage.getItem("t1"));
      // editor.insertTemplateData(data, false, false, [], {
      //   // waterMarkUseNew: true,
      //   waterMarkMerge: true,
      // });
      // const field = editor.getFieldById("t1");
      // field.setNewText("测@{1#24#123}试");
      // editor.updateFieldText({ fields: [field], append: true });
      // editor.splitTableByFullRowsWithAnchors();
      // editor.changeCellStyle({ style: { bgColor: "red" } });
      // editor.validFields();
      // editor.insertImage(
      //   "https://gips0.baidu.com/it/u=3808212212,1411671053&fm=3042&app=3042&f=JPEG&wm=1,huayi,0,0,13,9&wmo=0,0&w=480&h=640",
      //   { meta: { imageType: 1 } }
      // );

      // editor.insertWarterMarkBy({
      //   fieldId: "warterMark",
      //   width: 100,
      //   height: 200,
      //   imageSrc:
      //     "https://gips0.baidu.com/it/u=1939859157,1111239881&fm=3028&app=3028&f=JPEG&fmt=auto&q=100&size=f600_800",
      // });
      // const data = editor.m2w();
      // console.log(data);
      // const text = editor.getBodyText();
      // console.log(text, "获取到的body text");
      // 替换rawRawData的两行代码 ↓
      // const rawData1 = JSON.parse(localStorage.getItem("t1"));
      // const rawData2 = JSON.parse(localStorage.getItem("t2"));
      // const resRawData = this.instance.compareTraces(rawData1, rawData2, true);
      // this.instance.editor.setReadonly(false);
      // this.instance.editor.reInitRaw(resRawData);
      // this.instance.editor.refreshDocument();
      // this.instance.editor.setReadonly(true); // 替换rawRawData的两行代码 ↑ // this.instance.openTraceContrast(rawData1, rawData2)
      // const field = editor.getFieldsByName("B0113")[0];
      // const res = field.getRawData();
      // console.log(res);
      // const testField = editor.getFieldsByName("test")[0];
      // testField.replaceWith(res);
      // 先删除原插入的文本域，然后重新插入一个
      // let field = editor.insertField();
      // // field.maxHeight = 50;
      // if (field) {
      //   field.setNewText("1.111111111111111111111111");
      //   editor.updateFieldText({ fields: [field], append: true });
      //   editor.insertText("\n");
      //   editor.refreshDocument();
      //   field.setNewText("2.222222222222222222222222");
      //   editor.updateFieldText({ fields: [field], append: true });
      //   this.$editor.info(field.text);
      // }
      // editor.updateCellsAttr({
      //   cells: tables[0].children,
      //   attr: { lock: true },
      // });
      // this.instance.editor.fillContentByJson(null, tableFillExample);
    },
    fillContentByReceiveJsonData(editor) {
      // editor.fillContentByJson(null, tableFillExample);
      editor.fillContentByJson(null, this.oriDataSet, "dataSet");
    },
    clearFilledContent(editor) {
      editor.fillContentByJson(null, this.oriDataSet, "dataSet", true);
    },
    handleDataSetSelect(value) {
      this.leftPanelConfig.treeData = this.allDataSetTree[value];
      this.$forceUpdate();
    },
    handleDictSetSelect(value) {
      this.rightMenuConfig.treeData = this.allDictSetTree[value];
      this.$forceUpdate();
    },
    getRealValueByFileName(name) {
      const nameArr = name.split(">");
      let data = this.oriDataSet;
      for (let i = 0; i < nameArr.length; i++) {
        const key = nameArr[i];
        data = data[key];
        if (Array.isArray(data)) {
          data = data[0];
        }
        if (!data) return;
      }
      if (Array.isArray(data.children)) {
        let vals = data.value;
        if (typeof data.value === "string") {
          vals = data.value.split(",");
        }
        if (Array.isArray(vals)) {
          vals = vals.map((item) => String(item));
          const selItem = data.children.find((item) =>
            vals.includes(String(item.value))
          );
          if (selItem) {
            data.value = selItem.name;
          }
        }
      }
      return data;
    },
    searchFieldByKeyText(text, resolve) {
      const resList = [];
      const allData = this.allDataSetTree;
      for (const dataSetId in allData) {
        const array = allData[dataSetId];
        for (let i = 0, len = array.length; i < len; i++) {
          const item = array[i];
          if (item.title.toLowerCase().indexOf(text.toLowerCase()) > -1) {
            resList.push({
              name: item.pCode + ">" + item.title,
              node: item,
              type: "field",
            });
          }
        }
      }
      resolve(resList);
    },
    insertFieldByQuickInputSelect(item) {
      this.clickTreeMenu(item.node);
    },
    saveLocalData() {
      if (!this.instance) return;
      const editor = this.instance.editor;
      let counter =
        parseInt(localStorage.getItem("designEditorSaveCounter")) || 1;
      // 创建一个键名
      let key = `designEditorSaveData${counter}`;
      editor.config.getDataType = 1;
      // 写入数据到localStorage
      localStorage.setItem(key, editor.getRawData());
      editor.config.getDataType = 0;
      this.$editor.success(`已暂存至本地${counter}`);
      // 更新计数器，确保循环在1到10之间
      counter = (counter % 10) + 1;
      // 将计数器存储到localStorage
      localStorage.setItem("designEditorSaveCounter", String(counter));
    },
    insertHelperField() {
      const editor = this.instance.editor;
      // const allFields = editor.getAllFields();
      const fieldName = "helper_1";
      let insertedField = editor.insertField({
        type: "normal",
        placeholder: "辅助",
        name: fieldName,
        style: {
          bold: false,
        },
      });
      this.instance.showFloatBtn(insertedField, {
        code: fieldName,
        desc: "辅助",
      });
      this.$editor.info(`文本域名称:${fieldName}`);
    },
    // 重新插入文本域
    reInsertField() {
      const editor = this.instance.editor;
      let field = editor.selection.getFocusField();
      if (!field) {
        return this.$editor.warn(`请先将光标定位到需要重新插入的文本域中`);
      }

      if (field.type === "box") {
        field = field.parent_box;
      }
      // 根据文本域名称去数据集中检索，检索到后删除原文本域
      const fieldName = field.name;
      const tree = this.allDataSetTree;
      for (const key in tree) {
        const dataset = tree[key];
        for (let i = 0, len = dataset.length; i < len; i++) {
          const item = dataset[i];
          const code = item.code;
          if (fieldName.indexOf(">") > -1) {
            if (fieldName === code) {
              return this.instance.showFloatBtn(field, item);
            }
          } else {
            if (code.split(">")[1] === fieldName) {
              return this.instance.showFloatBtn(field, item);
            }
          }
        }
      }
      this.$editor.warn(`未从数据集中匹配到当前字段`);
      this.instance.showFloatBtn(field, {
        code: fieldName,
        desc: field.placeholder,
      });
    },
  },
};
export default dataSetMixIn;
