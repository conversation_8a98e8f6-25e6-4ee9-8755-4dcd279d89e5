/**
 // beforeContentChange: 编辑器内容修改之前调用事件
 // contentChanged: 编辑器内容修改之后事件
 // scroll: 编辑器滚动事件
 // pointerUp:编辑器点击后抬起事件
 // pointerMove:编辑器光标移动事件
 // click:编辑器点击事件
 // dblClick:编辑器双击事件
 // fieldSelectItemsRequest://文本域下拉数据源请求
 // menuClick:右键菜单点击事件
 */
// import BUS from "@/assets/js/eventBus";
import { Modal } from "ant-design-vue";
function handleFieldCharacterSize(editor, focus_field) {
  const res = editor.event.emit("beforeSetFieldCharacterSize");
  if (res === "prevent") {
    // 如果没有绑定事件的话，返回值为 origin
    return;
  }
  if (!focus_field) return;
  // 处理字体缩放 ↓
  const maxHeight = focus_field.maxHeight;

  if (!maxHeight) return;

  let count1 = 0;
  let count2 = 0;
  // uniformFontHeight 统一字体高度
  res === "uniformFontHeight" && editor.setCharacterSize(focus_field, "base");

  // focus_field.height 不能缓存 需要每次都重新计算
  // 字体放大
  while (
    focus_field.height < maxHeight &&
    focus_field.children[0]?.height < focus_field.style.height &&
    count2 < 10
  ) {
    editor.setCharacterSize(focus_field, "bigger");
    count2++;
  }

  // 字体缩小
  while (focus_field.height > maxHeight && count1 < 12) {
    // 加个次数限制 避免字体已经缩小到最小了还是超高进入死循环
    editor.setCharacterSize(focus_field, "smaller");
    count1++;
  }
  // 处理字体缩放 ↑

  editor.event.emit(
    "afterSetFieldCharacterSize",
    focus_field.children[0]?.height
  );
}

const editorEventMixIn = {
  methods: {
    /**
     * 编辑器抛出事件处理
     * @param Instance
     */
    bindInstanceEvent(Instance) {
      const editor = Instance.editor;
      this.curClickInfo = {};
      editor.init_canvas.addEventListener("mouseout", this.mouseoutFn);
      Instance.loadComSentencesData = (data) => {
        this.commonSentences = data ? data : [];
      };
      editor.event.on("reInitRaw", () => {
        //判断，如果批注列表展示的情况下，则刷新批注列表
        if (this.isShowCommentList) {
          this.refreshCommentList();
        }
      });
      editor.event.on("clickComment", (commentInfo) => {
        if (commentInfo.comment) {
          const { hideDate, hideDeleteBtn, hideReplaceBtn } =
            editor.config.comment;
          if (!hideDate && !hideDeleteBtn && !hideReplaceBtn)
            this.openCommentEditModal(commentInfo.comment.comment);
        }
      });

      editor.event.on("fieldFormula", (field) => {
        if (this.showFieldFormula) {
          //可编辑div聚焦
          var divElement = this.$refs.formulaEdit;
          if (divElement) {
            divElement.focus();
          }
          this.$refs.fieldFormulaEdit.insertFieldFormulaName(field.name);
          this.$refs.fieldFormulaEdit.verification();
        } else if (this.showCascadeOption) {
          this.$refs.cascade.insertFieldName(field.name);
        } else if (this.isShowChoiceModal) {
          if (this.showFieldAutomation) {
            this.$refs.fieldAuto.insertFieldName(field);
          } else {
            this.$refs.choiceField.insertFieldName(field.name);
          }
        } else if (this.showFieldAutomation) {
          this.$refs.fieldAuto.insertFieldName(field);
        }
      });
      editor.event.on("boxChecked", ({ field }) => {
        if (!field || field.type !== "box") return;
        const parent_box = field.parent_box;
        if (parent_box.cascade_list && parent_box.cascade_list.length) {
          const checkedItems = field.box_checked_items;
          if (checkedItems.length) {
            const txtArr = checkedItems.map((item) => item.text);
            editor.showOrHideField(parent_box, txtArr);
          } else {
            editor.showOrHideField(parent_box, []);
          }
        }
        // const isAutomation = parent_box.group_boxes.some(
        //   (e) => e.automation_list && e.automation_list.length
        // );
        // if (isAutomation) {
        //   editor.updateFieldAutomation(parent_box);
        // }

        if (field.name) {
          editor.updateFieldAdvancedFunctions(field);
        } else if (parent_box) {
          editor.updateFieldAdvancedFunctions(parent_box);
        }
      });
      // 滚轮滚动
      editor.event.on("scroll", () => {
        this.showInputSelect = false;
        this.hideFloatBtn();
        //鼠标滚动隐藏文本域展示的日期选择框、下拉面板
        this.hideFieldNeedPanel();
      });
      editor.event.on("input", () => {
        this.hideFieldNeedPanel();
      });
      // 表格插入不了 提示
      editor.event.on("message", (arg) => {
        let type, msg;
        if (!arg) return;
        if (typeof arg === "string" || typeof arg === "number") {
          msg = arg;
        } else {
          type = arg.type;
          msg = arg.msg;
        }
        if (!this.$editor[type]) {
          type = "warning";
        }
        this.$editor[type](msg);
      });
      editor.event.on("alert", (arg) => {
        Modal.info({
          title: `${arg}`,
          onOk() {},
        });
      });
      // 编辑器内容改变
      editor.event.on("contentChanged", () => {
        const focus_field = editor.selection.getFocusField();
        if (focus_field) {
          if (editor.field_valid) {
            this.editorContentChangedValid(focus_field);
          }

          // 因为人家调用接口也会频繁的弹出提示 不合理 所以先暂时去掉 后边再想怎么办
          // if (!editor.getInputDOM() && editor.isMobileTerminal()) {
          //   this.$editor.info("请注意：数据有改动", 0.5);
          // }
          handleFieldCharacterSize(editor, focus_field);
        }
        if (
          editor.config.source === "design" ||
          (this.instance && this.instance.localTest.useNew)
        ) {
          this.quickInputSelect();
        }
      });
      editor.event.on("changeImageSize", (image) => {
        this.handlePlaceImageField(image);
      });
      // 编辑器点击事件
      editor.event.on("click", (event, { x, y }) => {
        this.showInputSelect = false;
        //取消右键菜单显示
        this.cancelRightMenu();

        this.hideFloatBtn();
        // 取消展示表格线
        this.cancelShowTableLine();
        if (!editor.formula_mode) {
          this.curClickInfo = editor.getElementByPoint(x, y);
          this.buttonInfo = this.curClickInfo.element;
          this.fieldInfo = this.curClickInfo.field;
          this.activeSelectComment(this.curClickInfo);
          //解决直接右键打开文本域属性窗口不能正常赋值问题

          if (this.isDebugMode) {
            this.logDebugInfo(this.curClickInfo);
          }
        }
      });
      // 编辑器双击事件
      editor.event.on("dblClick", (event, { x, y }) => {
        //元素双击事件
        const res = editor.event.emit("eleDblClick", this.curClickInfo, event, {
          x,
          y,
        });
        if (res === false) {
          return;
        }
        editor._curClickInfo = this.curClickInfo;
        // BUS.$emit("eleDblClick" + this.editorId, this.curClickInfo);
        // this.$refs.image_editing.curClickInfo = this.curClickInfo;
        const currentField = this.curClickInfo.field;
        // 按住alt时直接打开文本域属性
        if (currentField && event.altKey && !event.ctrlKey) {
          if (currentField.type === "box") {
            this.showChoiceModal();
          } else {
            this.showFieldPropWindow();
          }
          return;
        }

        if (currentField) {
          this.openMedicalCalcFormula(currentField.placeholder, true);
        }

        if (!editor.readonly) {
          //打开医学表达式编辑窗口
          this.showEditMedicalFormulaWin();
          if (
            this.curClickInfo.image &&
            this.curClickInfo.image.meta.qrCodeType
          ) {
            this.showQrCodeModal(this.curClickInfo.image, true);
          } else if (this.curClickInfo.image && this.imagePopUpFrame) {
            if (this.curClickInfo.image.meta.imageType !== 1) {
              this.showImageEditingWindow();
            }
          }
          //处理文本域双击事件
          this.handleFieldClick(true);
        }
      });
      // 编辑器获得焦点事件
      editor.event.on("editorFocus", () => {
        this.browserAfterPrint && this.browserAfterPrint();
        this.browserAfterPrint = null;
      });
      editor.event.on("pointerUp", (event) => {
        const res = editor.event.emit(
          "beforePointerUp",
          event,
          this.curClickInfo
        );
        if (res === false) {
          return;
        }
        //处理文本域点击事件  只有左键点击生效
        // (当点击任意功能按钮调用了编辑器接口时会使编辑器获得焦点，此时curClickInfo还未赋值就触发了pointerUp事件，所以需增加curClickInfo判断)
        if (
          event.button === 0 &&
          this.curClickInfo &&
          !editor.isMobileTerminal()
        ) {
          this.handleFieldClick();
        }
      });
      //编辑器三击事件
      editor.event.on("tripleClick", (event) => {
        if (event.altKey && event.ctrlKey && !this.curClickInfo.page) {
          this.openDebuggingTool(true);
        }
      });
      editor.event.on("beforeKeydown", (event) => {
        this.ctrlShortcut(event);
        if (this.showFieldSearchBox) {
          this.altQuickInput(event);
        }
        return this.inputSelectKeyDown(event);
      });
      editor.event.on("compositionStart", () => {
        this.showInputSelect = false;
      });
      editor.event.on("exeCommand", (event) => {
        if (event.command === "paste") {
          handleFieldCharacterSize(editor, editor.selection.getFocusField());
        }
      });
      editor.event.on("pointerMove", (event, { x, y }) => {
        this.showFieldTip(x, y);
        if (editor.view_mode === "person") this.showContrastTip(x, y);
      });
      editor.event.on("insertComSentence", (item) => {
        if (item && item.type === "insertMenu") {
          item.node.func();
        }
      });
      editor.event.on("assembleText", (text, rawField, extraInfo) => {
        if (!this.instance || !this.instance.localTest.useNew) {
          return;
        }
        if (extraInfo.isGetRawData) {
          this.handleAssembleText(text, rawField);
        }
      });
    },
  },
};
export default editorEventMixIn;
