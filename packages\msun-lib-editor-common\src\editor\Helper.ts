import Group from "./Groups"; // 因为 isGroup 在 DataTrans 里边用了 所以得在 DataTrans 的引用上方引用
import Cell from "./Cell";
import Table from "./Table";
import Row from "./Row";
import Character from "./Character";
import Font from "./Font";
import createImagePlaceholder, {
  removeRepeat,
  uuid,
  caesarCipher,
  isTable,
  isParagraph,
  isRow,
  isCharacter,
  decodeBase64,
  isNotNullAndUndefined,
  isTibetan
} from "./Utils";
import Widget from "./Widget";
import Paragraph from "./Paragraph";
import Editor from "./Editor";
import { Config, system_variables } from "./Config";
import PathUtils, { Path } from "./Path";
import XField from "./XField";
import Image from "./Image";
import XSelection from "./Selection";
import Line from "./line";
import Box from "./Box";
import EditorHelper from "./EditorHelper";
import Button from "./Button";
import Renderer from "./Renderer";
import { SkipMode, ViewMode } from "./Constant";
import BoxField from "./BoxField";
import ImageTable from "./ImageTable";
import EditorLocalTest from "../../localtest";
import Page from "./Page";
import Fraction from "./Fraction";

export type ReplaceContainerType = Paragraph | Table | XField

// 循环当前 cell 内的所有段落将数据放到 saveData 中
function handleSaveData(editor: Editor, selection: XSelection, saveData: (Paragraph | Table)[], paraTableArr: (Paragraph | Table)[], startParaIndex: number, endParaIndex: number, startCharacterIndex: number, endCharacterIndex: number, cell: Cell) {
  // TODO 跟 XField.ts 文件中的 getRawData 方法有重复代码，逻辑相似 可以考虑优化(不要加各种判断，不好理解，也不好维护) 能不能拆分成更小的函数等其他优化手段
  for (let i = startParaIndex, len = endParaIndex; i <= len; i++) {
    const current_para_tbl = paraTableArr[i].copy(cell); // 循环 当前段落或者表格
    if (i === startParaIndex) {
      // 处理开头 - 段落和表格两种情况
      if (isTable(current_para_tbl)) {
        saveData.push(current_para_tbl);
      } else if (isParagraph(current_para_tbl)) {
        const now_paragraph_characters = current_para_tbl.characters;
        if (
          startCharacterIndex === 0 &&
          (endParaIndex > startParaIndex || endCharacterIndex >= now_paragraph_characters.length - 1) // 因为有个"\n" 所以减一
        ) {
          if (endCharacterIndex === now_paragraph_characters.length - 1) {
            // 此时虽然是完整段落 但是要处理下换行符的 field_id
            const lastCharacter = now_paragraph_characters[now_paragraph_characters.length - 1];
            if (lastCharacter.field_id) {
              let flag = true;
              for (let j = 0; j < endCharacterIndex; j++) {
                // 换行符之前的所有 character 的 field_id 如果都没有跟换行符的 field_id 一样的 那就说明换行符不在文本域里边 直接设置 field_id 为 null 就可以
                // 解决的是只单独成一段的嵌套文本域 换行符的 field_id 为外层文本域 id 的情况下，粘贴出来文本域多一个开始边框的问题
                if (now_paragraph_characters[j].field_id === lastCharacter.field_id) {
                  flag = false;
                  break;
                }
              }
              flag && (lastCharacter.field_id = null);
            }
          }
          // 开头是完整的段落
          saveData.push(current_para_tbl);
        } else {
          // 否则开头不是完整的段落 要区分 开始和结尾路径是否都在该段落 因为截取参数不一样
          const para = new Paragraph(uuid("para"), cell, null);
          if (endParaIndex === startParaIndex) {
            // 在一个段落如果末尾有换行符
            if (selection.hasLastLinebreak) {
              para.characters = now_paragraph_characters.slice(
                startCharacterIndex,
                endCharacterIndex + 1
              );
            } else {
              para.characters = now_paragraph_characters.slice(
                startCharacterIndex,
                endCharacterIndex
              );
            }
          } else {
            para.characters = now_paragraph_characters.slice(startCharacterIndex);
          }
          saveData.push(para);
        }
      }
    } else if (i === endParaIndex) {
      // 处理结尾 - 表格和段落两种情况
      if (isTable(current_para_tbl)) {
        saveData.push(current_para_tbl);
      } else if (isParagraph(current_para_tbl)) {
        const now_paragraph_characters = current_para_tbl.characters;
        if (endCharacterIndex !== 0) {
          // 如果endCharacterIndex === 0说明结尾段落啥也没选中，就什么也不用操作，所以只操作不等于0的情况
          if (endCharacterIndex === now_paragraph_characters.length - 1) {
            saveData.push(current_para_tbl);
          } else {
            const para = new Paragraph(uuid("para"), cell, null);
            para.characters = now_paragraph_characters.slice(0, endCharacterIndex);
            saveData.push(para);
          }
        } else {
          // 说明末尾段落只有一个换行符
          const para = new Paragraph(uuid("para"), cell, null);
          const character = new Character(editor.fontMap.add(editor.config.default_font_style), "\n");
          para.characters = [character];
          saveData.push(para);
        }
      }
    } else {
      saveData.push(current_para_tbl);
    }
  }
}

function saveSelectionData1(editor: Editor, start?: Path, end?: Path, currentSelecion?: {
  selectedAreas: {
    start_para_path: Path;
    end_para_path: Path;
    last_linebreak?: boolean; // 区域的最后一行是否包含换行符
  }[],
  selectedCellPath: Path[],
  start?: Path,
  end?: Path,
}) {
  const selection = editor.selection;
  const saveData: (Paragraph | Table)[] = []; // 保存选中的数据

  start = start ? start : editor.selection.para_start;
  end = end ? end : editor.selection.para_end;

  const outerCell = editor.current_cell;

  let multipleSelectionIsOneTable = false;
  let multipleSelectionIsOnlyCell = false;
  let selectionTable: Table | undefined;
  if (currentSelecion && !currentSelecion.selectedAreas.length && currentSelecion.selectedCellPath.length) {
    const tableIndex = currentSelecion.selectedCellPath[0]?.[0];
    selectionTable = outerCell.paragraph[tableIndex] as Table;
    if (isTable(selectionTable)) {
      if (selectionTable.children.length === currentSelecion.selectedCellPath.length) {
        multipleSelectionIsOneTable = true;
      } else {
        multipleSelectionIsOnlyCell = true;
      }
    }
  }
  if (selection.isOneTable || multipleSelectionIsOneTable) { // 选中的只是一个完整的表格

    const table = editor.current_cell.paragraph[start[0]] as Table;
    const copiedTable = table.copy(outerCell);


    if (multipleSelectionIsOneTable) {
      for (const cell of copiedTable.children) {
        for (let j = 0; j < cell.paragraph.length; j++) {
          const p = cell.paragraph[j];
          const copiedP = p.copy(cell)
          saveData.push(copiedP);
        }
      }
      return saveData;
    }
    saveData.push(copiedTable);
    return saveData;
  }

  if (selection.isOnlyCell || multipleSelectionIsOnlyCell) { // 选中的只是某一表格内的若干个单元格
    const selected_cells = selection.selected_cells;
    const assignCells = selected_cells.map(c => c.cell);

    const new_tbl = someCells2Table(assignCells);

    if (multipleSelectionIsOnlyCell) {
      for (const cell of new_tbl.children) {
        for (let j = 0; j < cell.paragraph.length; j++) {
          const p = cell.paragraph[j];
          const copiedP = p.copy(cell)
          saveData.push(copiedP);
        }
      }
      return saveData;
    }

    saveData.push(new_tbl);
    return saveData;
  }

  const start_len = start.length;
  const end_len = end.length;
  const paragraph_tbl_arr = outerCell.paragraph;

  // 选区首尾都是表格
  if (PathUtils.isTwoDiffTable(start, end)) {
    for (let i = start[0], len = end[0]; i <= len; i++) {
      const current = paragraph_tbl_arr[i];
      saveData.push(current.copy(outerCell));
    }
    return saveData;
  }

  // 选区首尾都在一个单元格内 因为排除了首尾都是表格的情况 所以这个判断没问题
  if (start_len === end_len && end_len > 3) {
    const current_cell = paragraph_tbl_arr[start[0]].children[start[1]] as Cell;
    // 这里边必然只是选中了表格中的段落,因为排除掉了首尾都是表格和若干个单元格和单个表格的情况
    const paragraphs = current_cell.paragraph as Paragraph[]; // 该单元格内的所有paragraph 因为不考虑嵌套表格 所以都必然只是段落
    handleSaveData(editor, selection, saveData, paragraphs, start[2], end[2], start[3], end[3], current_cell);
    amendmentSaveSelectionData(selection, saveData);
    return saveData;
  }

  // 因为上边return掉了 所以这里必然是有文字和表格(如果有表格的话)一块选的，哪怕文字是空或者换行符
  handleSaveData(editor, selection, saveData, paragraph_tbl_arr, start[0], end[0], start[1], end[1], outerCell);
  amendmentSaveSelectionData(selection, saveData);
  return saveData;
}

/**
 * 保存选区数据 不是完整段落也会保存为Paragraph
 * @param editor
 * @returns 返回选区数据 [...,Paragraph,...,Table,...] 形式
 */
export function saveSelectionData(
  editor: Editor,
  currentSelecion?: any
): (Paragraph | Table)[] | undefined {
  const selection = editor.selection;
  if (selection.isCollapsed) { // 选区没内容 直接return
    return;
  }
  const saveData: (Paragraph | Table)[] = []; // 保存选中的数据
  const res = saveSelectionData1(editor, currentSelecion?.start, currentSelecion?.end, currentSelecion);
  res?.length && (saveData.push(...res));
  return saveData;
}

/**
 * 若干单元格转表格
 * @param assignCells 需要处理的单元格数组
 * @param source 调用源 copy 代表复制时使用 row-代表指定行转换时使用
 */
export function someCells2Table(assignCells: Cell[], source: string = "copy"): Table {
  const editor = assignCells[0].editor;
  const outerCell = editor.root_cell;
  const table = assignCells[0].parent!.copy(outerCell);

  let row_index_arr: number[] = [];
  const col_index_arr: number[] = [];
  const selected_cells: { cell: Cell }[] = [];
  // selected_cells 要修改这里边单元格的属性 所以必须copy
  const copy_selected_cells = assignCells.map((cell) => {
    row_index_arr.push(cell.position[0], cell.end_row_index);
    col_index_arr.push(cell.position[1], cell.end_col_index);
    selected_cells.push({ cell });
    return cell.copy(table);
  });
  row_index_arr = removeRepeat(row_index_arr).sort((a, b) => a - b);

  const min_row_index = Math.min(...row_index_arr);
  const min_col_index = Math.min(...col_index_arr);
  const max_row_index = Math.max(...row_index_arr);
  const max_col_index = Math.max(...col_index_arr);

  let newRowSize = table.row_size.slice(min_row_index, max_row_index + 1);
  const newColSize = table.col_size.slice(min_col_index, max_col_index + 1); // 不用做精确计算 在new Table里边会重新计算的
  let newMinRowSize = table.min_row_size.slice(min_row_index, max_row_index + 1);
  if (source === "row") {
    newRowSize = row_index_arr.map(i => table.row_size[i]);
    newMinRowSize = row_index_arr.map(i => table.min_row_size[i]);
  }
  const new_tbl = new Table(
    editor,
    uuid("table"),
    null,
    newColSize,
    newRowSize,
    newMinRowSize,
    outerCell,
    table.left,
    table.right,
    0,
    false,
    SkipMode.ROW
  );
  // 记录每个cell的position重组后应该对应的行下标
  const realRowIndexMap: any = {};
  for (let i = 0, len = row_index_arr.length; i < len; i++) {
    realRowIndexMap[row_index_arr[i]] = i;
  }
  // 因为要处理 不规则的表格 避免出现负数 用最小行和最小列 组成起点位置
  const col_sub = min_col_index;
  copy_selected_cells.forEach((cell) => { // 将所有单元格的位置 向左上角平移 row_sub和col_sub个距离
    cell.parent = new_tbl; // 修改其对应的表格
    cell.position[0] = realRowIndexMap[cell.position[0]];
    cell.position[1] -= col_sub;
    // TODO 临时处理
    if (cell.rowspan > row_index_arr.length) {
      cell.rowspan = row_index_arr.length;
    }
  });

  new_tbl.children = copy_selected_cells;
  if (!Table.canMergeCells(editor, selected_cells)) {
    // 不规则的 要补全单元格
    new_tbl.completeTheCell();
  }
  new_tbl.handleLine(selected_cells, table, min_row_index, min_col_index); // 处理表格线的显示隐藏和画不画
  return new_tbl;
}

// 获取每行每列上的单元格
export function getCellsOnRowAndCol(cells: Cell[]): { rows_cells_obj: { [propName: string]: Cell[] }, cols_cells_obj: { [propName: string]: Cell[] } } {
  // 3. 将每一行的数据 和 每一列的数据 都分别存储到一个单独的对象中
  const rows_cells_obj: { [propName: string]: Cell[] } = {}; // 放所有行的单元格
  const cols_cells_obj: { [propName: string]: Cell[] } = {}; // 放所有列的单元格

  for (const cell of cells) { // 此时的cell已经是最小化后的单元格了
    const start_row_index = cell.start_row_index;
    const start_col_index = cell.start_col_index;
    if (rows_cells_obj[start_row_index]) {
      rows_cells_obj[start_row_index].push(cell);
    } else {
      rows_cells_obj[start_row_index] = [cell];
    }
    if (cols_cells_obj[start_col_index]) {
      cols_cells_obj[start_col_index].push(cell);
    } else {
      cols_cells_obj[start_col_index] = [cell];
    }
  }
  return {
    rows_cells_obj,
    cols_cells_obj
  };
}

/**
 * 修正保存的选区数据（主要用于修正文本域结构）
 */
function amendmentSaveSelectionData(selection: XSelection, data: (Paragraph | Table)[]) {
  const { fieldIdVsChars, field_chars } = selection.selected_fields_chars;
  const fieldIdsToClear: string[] = [];
  const fieldIdsToDelete: string[] = [];

  for (const fieldId in fieldIdVsChars) {
    const field = selection.editor.getFieldById(fieldId);
    if (!field) continue;

    if (!field.canBeCopied) {
      fieldIdsToDelete.push(field.id);
      continue;
    }

    if (!field.isCompleteField(field_chars)) {
      fieldIdsToClear.push(field.id);
    }
  }

  data.forEach((item) => {
    if (isParagraph(item)) {
      item.characters = item.characters.filter((element) => {
        // 检查是否需要删除
        if ((fieldIdsToDelete as any).includes(element.field_id)) {
          return false;
        }
        // 检查是否需要清除
        const needsClear = (fieldIdsToClear as any).includes(element.field_id);
        if (needsClear) {
          element.field_id = "";
          if (element.field_position !== "normal") {
            return false;
          }
        }
        return true;
      });
    }
  });
}



export function isLastCell(cell: Cell): boolean {
  // return (
  //   cell.position[0] + cell.rowspan === cell.row_size.length &&
  //   cell.position[1] + cell.colspan === cell.col_size.length
  // );
  const table = cell.parent as Table;
  return table.children.length === table.children.indexOf(cell) + 1;
}

export function isRootTableRow(cell: Cell): boolean {
  return !cell.position;
}

export function getNextCellIndex(cell: Cell): number {
  const parent = cell.parent;

  if (!parent || isLastCell(cell)) return -1;

  return parent.children.indexOf(cell) + 1;
}

export function getNextCell(cell: Cell): Cell | null {
  const next_index = getNextCellIndex(cell);

  const parent = cell.parent!;

  return next_index < 0 ? null : parent.children[next_index];
}

export function isFirstCell(cell: Cell): boolean {
  return cell.position[0] === 0 && cell.position[1] === 0;
}

export function getPreviousCellIndex(cell: Cell): number {
  const parent = cell.parent;

  if (!parent || isFirstCell(cell)) return -1;

  return parent.children.indexOf(cell) - 1;
}

export function getPreviousCell(cell: Cell): Cell | null {
  const previous_index = getPreviousCellIndex(cell);

  const parent = cell.parent!;

  return previous_index < 0 ? null : parent.children[previous_index];
}

export function getCellIndex(cell: Cell): number {
  if (!cell.parent) return -1;

  return cell.parent.children.indexOf(cell);
}

// 获取当前位置的后一个文本域
export function getNextField(cell: Cell, current_para_path: Path): XField | undefined {
  const editor = cell.editor;
  const entryMode = editor.config.fastEntryMode;
  let nextField;
  if (entryMode && editor.selection.focus.length !== 4) {
    const allFields = editor.sortAllFields();
    for (let i = current_para_path[0]; i < editor.current_cell.paragraph.length; i++) {
      nextField = allFields.find(field => {
        if (current_para_path.length === 2) {
          return (field.start_para_path[0] === i && field.start_para_path[0] !== current_para_path[0]) ||
            (field.start_para_path[0] === current_para_path[0] && field.start_para_path[1] > current_para_path[1] - 1);
        } else {
          return (field.start_para_path[0] === current_para_path[0] &&
            field.start_para_path[1] === current_para_path[1] &&
            field.start_para_path[2] === current_para_path[2] &&
            field.start_para_path[3] > current_para_path[3] - 1) ||
            (field.start_para_path[0] === current_para_path[0] &&
              field.start_para_path[1] === current_para_path[1] &&
              field.start_para_path[2] > current_para_path[2]) ||
            (field.start_para_path[0] === current_para_path[0] &&
              field.start_para_path[1] > current_para_path[1]
            ) || (field.start_para_path[0] > current_para_path[0]);
        }
      });
    }
    return nextField;
  } else {
    const fields = cell.fields;
    return fields.find(field => {
      const field_path = field.start_para_path_inner;
      const field_para_index = field_path[field_path.length - 2]; // 该文本域所在段落的下标
      const current_para_index = current_para_path[current_para_path.length - 2]; // 当前光标所在段落的下标

      const field_char_index = field_path[field_path.length - 1]; // 该文本域开头字符的位置
      const current_char_index = current_para_path[current_para_path.length - 1];
      if (field_para_index === current_para_index) {
        // 如果在同一段的话 字符位置 必须要在当前光标的后边 才是后边的文本域
        return field_char_index > current_char_index && !field.isReadonly;
      } else if (field_para_index > current_para_index) {
        // 不在同一段的话 下一个文本域 必须在后边的段落 后边段落有 必然就有文本域了
        return !field.isReadonly;
      } else {
        return false;
      }
    });
  }
}

// 获取当前位置的前一个文本域
export function getPrevField(cell: Cell, current_para_path: Path): XField | undefined {
  const editor = cell.editor;
  const entryMode = editor.config.fastEntryMode;
  const allFields = editor.sortAllFields();
  if (entryMode && editor.selection.focus.length !== 4) {
    let preField;
    for (let i = current_para_path[0]; i >= 0; i--) {
      preField = allFields.find(field => {
        if (current_para_path.length === 2) {
          return ((field.end_para_path[0] === i && field.end_para_path[0] !== current_para_path[0]) ||
            (field.end_para_path[0] === current_para_path[0] && current_para_path[1] > field.end_para_path[1]));
        } else {
          return (field.end_para_path[0] === current_para_path[0] &&
            field.end_para_path[1] === current_para_path[1] &&
            field.end_para_path[2] === current_para_path[2] &&
            field.end_para_path[3] < current_para_path[3]) ||
            (field.end_para_path[0] === current_para_path[0] &&
              field.end_para_path[1] === current_para_path[1] &&
              field.end_para_path[2] < current_para_path[2]) ||
            (field.end_para_path[0] === current_para_path[0] &&
              field.end_para_path[1] < current_para_path[1]
            ) || (field.end_para_path[0] < current_para_path[0]);
        }
      });
    }
    return preField;
  } else {
    const current_field = cell.editor.selection.getFieldByPath(current_para_path);
    const fields = cell.fields;
    // 找前一个 就倒着循环
    for (let i = fields.length - 1; i >= 0; i--) {
      const field = fields[i];
      const field_path = field.start_para_path_inner;
      const field_para_index = field_path[field_path.length - 2]; // 该文本域所在段落的下标
      const current_para_index = current_para_path[current_para_path.length - 2]; // 当前光标所在段落的下标

      const field_char_index = field_path[field_path.length - 1]; // 该文本域开头字符的位置
      const current_char_index = current_para_path[current_para_path.length - 1];
      if (field_para_index === current_para_index) {
        // 如果在同一段的话 字符位置 必须要在当前光标的前边 才是前边的文本域
        if (field_char_index < current_char_index && !field.isReadonly && field !== current_field) {
          return field;
        }
      } else if (field_para_index < current_para_index) {
        // 不在同一段的话 下一个文本域 必须在后边的段落 后边段落有 必然就有文本域了
        if (!field.isReadonly && field !== current_field) {
          return field;
        }
      }
    }
  }
}

export function findSplitCellIndexByCell(
  splitTable: Table,
  cell: Cell
): number {
  let index!: number;

  for (let i = 0; i < splitTable.children.length; i++) {
    const split_table_cell = splitTable.children[i];

    if (split_table_cell.origin === cell) {
      index = getCellIndex(split_table_cell);

      break;
    }
  }

  return index;
}

/**
 * 根据当前容器获得最外层父容器 即root_cell
 * @param container
 */
export function getRootContainer(
  container: Row | Cell | Table
): Row | Cell | Table {
  if (container.parent) {
    return getRootContainer(container.parent);
  }
  return container;
}

/**
 * 获得距离x坐标最近的cell
 */
export function nearOffsetCell(
  cells: Cell[] = [],
  x: number,
  moveType: "down_in" | "up" | "down" | "up_in",
  current_cell?: Cell
): Cell | null {
  if (moveType === "down_in") {
    return cells.filter((cell) => x >= cell.left && x < cell.right)[0];
  }
  if (moveType === "up") {
    const cell = getPreviousCell(current_cell as Cell);
    if (!cell) return null;
    if (x >= cell.left && x < cell.right) {
      return cell;
    } else {
      return nearOffsetCell(cells, x, moveType, cell);
    }
  }
  if (moveType === "down") {
    const cell = getNextCell(current_cell as Cell);
    if (!cell) return null;
    if (x >= cell.left && x < cell.right) {
      return cell;
    } else {
      return nearOffsetCell(cells, x, moveType, cell);
    }
  }
  if (moveType === "up_in") {
    const result = cells.filter((cell) => x >= cell.left && x < cell.right);
    return result[result.length - 1];
  }
  return null;
}

/**
 * 获取 base64 方法
 * @param editor
 * @param editor2
 */
export function getImgBase64List(editor: Editor, editor2: Editor, canvas: any, printParameter?: any, mode?: string): string[] {
  const show_h_f = editor.show_header_footer;
  let dataList = [];
  let printPageNums = [];
  let pages = editor2.pages;
  // 续打开始页
  let start_print_page = 0;
  let end_print_page = editor2.pages.length;

  const page_size = editor.config.getPageSize();
  if (editor.print_continue) {
    start_print_page = Math.floor(
      (editor.internal.print_absolute_y - editor.config.editor_padding_top) /
      (page_size.height + editor.config.page_margin_bottom)
    );
    // 初始续打位置 如果没有点击，会计算出来一个-1，所以要置为0
    start_print_page = start_print_page < 0 ? 0 : start_print_page;
  }
  if (editor.area_print) {
    start_print_page = Math.floor(
      (editor.internal.area_location.start_absolute_y - editor.config.editor_padding_top) /
      (page_size.height + editor.config.page_margin_bottom)
    );
    // 位置 如果没有点击，会计算出来一个-1，所以要置为0
    start_print_page = start_print_page < 0 ? 0 : start_print_page;
    end_print_page = Math.floor(
      (editor.internal.area_location.end_absolute_y - editor.config.editor_padding_top) /
      (page_size.height + editor.config.page_margin_bottom)
    ) + 1;
    end_print_page = end_print_page <= 0 ? editor2.pages.length : end_print_page;
  }
  //分组打印的时候只打印能打印的分组页
  if (editor.group_print) {
    start_print_page = editor.pages.findIndex((item: Page) => item.groups.length && item.groups.filter((itm: Group) => itm.meta.print).length)
    end_print_page = editor.pages.length  - editor.pages.slice().reverse().findIndex((item: Page) => item.groups.length && item.groups.filter((itm: Group) => itm.meta.print).length)
  }
  if (printParameter && printParameter.pages) {
    // 传来的参数中提供了打印的页数
    printPageNums = printParameter.pages;

    const realPages = [];
    if (editor.print_continue) {
      for (let i = start_print_page; i < pages.length; i++) {
        realPages.push(pages[i]);
      }
      start_print_page = 0;
      end_print_page = printPageNums.length;
    } else if (editor.area_print) {
      for (let i = start_print_page; i < end_print_page; i++) {
        realPages.push(pages[i]);
      }
      start_print_page = 0;
      end_print_page = printPageNums.length;
    } else {
      // 因为进到外一层的 if 里边就说明已经指定页码了 既然指定页码了就不能全放进去 editor2.pages 了,所以这样写应该没啥问题
      for (let i = 0; i < printPageNums.length; i++) {
        realPages.push(editor2.pages[i]);
      }
      start_print_page = 0;
      end_print_page = realPages.length;
    }
    pages = realPages;
  }

  const ctx = canvas.getContext("2d", { alpha: false }) as CanvasRenderingContext2D;
  const print_odd = [];
  const print_even = [];
  for (let i = start_print_page; i < end_print_page; i++) {
    const page_bounding = printPageNums.length ? pages[printPageNums[i]] : editor2.pages[i];
    editor2.scroll_top = page_bounding.top;
    editor2.show_header_footer = show_h_f;
    editor2.render();
    if (editor.print_continue && i === start_print_page) {
      if (editor.internal.print_absolute_y === page_bounding.top || editor.internal.print_absolute_y === 0 || (printParameter && printParameter.pages && printParameter.pages[0] !== 0)) {
        editor2.show_header_footer = show_h_f;
      } else {
        editor2.show_header_footer = false;
        editor2.render();
        ctx.fillStyle = "#fff";
        ctx.fillRect(0, 0, editor.page_size.width * editor.config.printRatio, (editor.internal.print_absolute_y - page_bounding.top) * editor.config.printRatio);
      }
    } else if (editor.area_print) {
      const start_y = (editor.internal.area_location.start_absolute_y - page_bounding.top) * editor.config.printRatio;
      const end_y = (editor.internal.area_location.end_absolute_y - page_bounding.top) * editor.config.printRatio;
      const start_x = (editor.internal.area_location.start_absolute_x - editor.page_left) * editor.config.printRatio;
      const end_x = (editor.internal.area_location.end_absolute_x - editor.page_left) * editor.config.printRatio;
      const page_height = editor.page_size.height * editor.config.printRatio;
      const page_width = editor.page_size.width * editor.config.printRatio;
      // 区域打印模式
      if (editor.internal.area_location.end_absolute_y !== 0) {
        // 无论区域打印横跨几页，i = start_print_page时裁减高度等于鼠标开始位置的高度
        if (i === start_print_page) {
          ctx.fillStyle = "#fff";
          ctx.fillRect(0, 0, page_width, start_y);
        }

        // 当开始位置和结束位置同一页时，截取高度为结束高度减去开始高度
        if (start_print_page === end_print_page - 1) {
          ctx.fillStyle = "#fff";
          ctx.fillRect(0, end_y, page_width, page_height - end_y);
          ctx.fillRect(0, start_y, start_x, end_y - start_y);
          ctx.fillRect(end_x, start_y, page_width - end_x, end_y - start_y);
        } else if (start_print_page === end_print_page - 2) {
          // 开始位置和结束位置相差一页时，第一页的结束高度为选中部分，第二页的结束高度为鼠标所在处结束高度减去页面的top
          if (i === start_print_page) {
            ctx.fillStyle = "#fff";
            ctx.fillRect(0, start_y, start_x, page_height - start_y);
            ctx.fillRect(end_x, start_y, page_width - end_x, page_height - start_y);
          } else {
            ctx.fillStyle = "#fff";
            ctx.fillRect(0, end_y, page_width, page_height - end_y);
            ctx.fillRect(0, 0, start_x, end_y);
            ctx.fillRect(end_x, 0, page_width - end_x, end_y);
          }
        } else {
          // 开始位置和结束位置相差一页以上时，除最后一页的结束高度为鼠标所在处结束高度减去页面的top，第一页高度为选中部分，其余页面的结束高度都是页面的高度
          if (i === end_print_page - 1) {
            ctx.fillStyle = "#fff";
            ctx.fillRect(0, end_y, page_width, page_height - end_y);
            ctx.fillRect(0, 0, start_x, end_y);
            ctx.fillRect(end_x, 0, page_width - end_x, end_y);
          } else if (i === start_print_page) {
            ctx.fillStyle = "#fff";
            ctx.fillRect(0, start_y, start_x, page_height - start_y);
            ctx.fillRect(end_x, start_y, page_width - end_x, page_height - start_y);
          } else {
            ctx.fillStyle = "#fff";
            ctx.fillRect(0, 0, start_x, page_height);
            ctx.fillRect(end_x, 0, page_width - end_x, page_height);
          }
        }
      }
    }

    const element = canvas.toDataURL();

    printParameter && printParameter.sendPrintRequest && printParameter.sendPrintRequest(element);
    // 打印的最后一页
    const last_page = editor2.pages[editor2.pages.length - 1];
    // 打印最后一行的高度
    const last_page_children = last_page.children;
    // 判断原始数据meta中是否有最后一页最后一行高度的属性
    editor.document_meta.printInfo = { height: last_page.top + last_page_children[last_page_children.length - 1].top + last_page_children[last_page_children.length - 1].height };
    // 保证预览的时候不刷新续打位置
    if (!printParameter || !printParameter.printView) {
      editor.internal.print_absolute_y = 0; // 打印完成后回复续打点击位置为0
    }
    if (mode && mode === "odd" && i % 2 === 0) {
      print_odd.push(element);
    } else if (mode && mode === "even" && i % 2 !== 0) {
      print_even.push(element);
    } else {
      dataList.push(element);
    }
  }

  if (mode && mode === "odd") {
    dataList = print_odd;
  } else if (mode && mode === "even") {
    dataList = print_even;
  }
  return dataList;
}

export function getPrintContinueBase64(start_print_page: number, continue_page_top: number, editor: Editor) {
  const page_size = editor.config.getPageSize();
  start_print_page = Math.floor(
    (editor.internal.print_absolute_y - editor.config.editor_padding_top) /
    (page_size.height + editor.config.page_margin_bottom)
  );
  // 初始续打位置 如果没有点击，会计算出来一个-1，所以要置为0
  start_print_page = start_print_page < 0 ? 0 : start_print_page;
  const page_height_margin = page_size.height + editor.config.page_margin_bottom;
  continue_page_top = (editor.internal.print_absolute_y - page_height_margin * start_print_page - editor.config.editor_padding_top) *
    editor.config.printRatio;
}

export function initEditor(editor: Editor, type?: string) {
  const temporaryCanvas = document.createElement("canvas");
  const input = document.createElement("textarea");
  const div = document.createElement("div");
  temporaryCanvas.height = editor.page_size.height * editor.config.printRatio;
  temporaryCanvas.width = editor.page_size.width * editor.config.printRatio;
  temporaryCanvas.style.width = editor.page_size.width + "px";
  temporaryCanvas.style.height = editor.page_size.height + "px";
  const newConfig = editor.config.copy();
  const editor2 = new Editor(newConfig);
  const italicWatermark = editor.internal.italicWatermark;
  editor2.internal.italicWatermark = JSON.parse(JSON.stringify(italicWatermark));
  if (italicWatermark.module.length === 1 && italicWatermark.module[0] === "pageWrite") {
    editor2.internal.italicWatermark.module = [];
  }
  editor.event.emit("initNewEditor", editor2);
  editor2.config.page_color = "#ffffff";
  editor2.show_header_footer = editor.show_header_footer;
  editor2.viewScale = 1;
  editor2.config.history_limit = 0;
  if (type !== "print") {
    editor2.render = () => { };
  }
  editor2.internal.attach(temporaryCanvas, input, div);
  return { editor2, temporaryCanvas };
}

export function initPrintEditor(editor: Editor, rawData?: any) {
  if (!rawData) {
    rawData = editor.getRawData();
  }
  const { editor2, temporaryCanvas } = initEditor(editor, "print"); // 这里出来的 temporaryCanvas 也是绘制好的 .toDataURL() 一样可以看到绘制的效果
  editor2.reInitRaw(rawData);
  editor2.refreshDocument();
  editor2.imageMap = editor.imageMap; // 此处一定要赋值，否则打印预览时没有图片
  editor2.print_mode = true;

  const index = editor.internal.italicWatermark.module.indexOf("printView");
  if (index !== -1) {
    editor2.internal.italicWatermark = JSON.parse(JSON.stringify(editor.internal.italicWatermark))
    editor2.internal.italicWatermark.module.splice(index, 1, "pageWrite");
  }
  editor2.config.history_limit = 0;
  const keepSymbolWidthWhenPrint = !!editor.document_meta.keepSymbolWidthWhenPrint;
  if (!keepSymbolWidthWhenPrint) {
    editor2.setViewMode(ViewMode.VIEW);
  }
  editor2.readonly = true;
  editor2.destroy();
  return { editor2, temporaryCanvas };
}

/**
 * 获取页眉信息中符合分组页眉关联信息的文本域最大长度与内容
 * @param root_cell
 */
// export function getHeaderFieldMaxLength(root_cell: Cell) {
//   const lengthInfo: any = {};
//   // 处理分组中包含的页眉数据信息
//   const groups = root_cell.groups;
//   const editor = root_cell.editor;
//   for (let i = 0; i < groups.length; i++) {
//     const header_info = groups[i].header_info;
//     if (!header_info) {
//       continue;
//     }
//     for (const key in header_info) {
//       if (!lengthInfo[key]) {
//         lengthInfo[key] = {};
//         lengthInfo[key].width = -1;
//         lengthInfo[key].value = "";
//       }
//       let field = editor.getFieldById(key, editor.header_cell);
//       if (!field) {
//         field = editor.getFieldsByName(key, editor.header_cell)[0];
//       }
//       if (!field) {
//         continue;
//       }
//       const { width } = Renderer.measure(new Font(field.style), header_info[key], editor);
//       if (lengthInfo[key].width < width) {
//         lengthInfo[key].width = width;
//         lengthInfo[key].value = header_info[key];
//       }
//     }
//   }
//   return lengthInfo;
// }

/**
 * 根据原文本域内容与最长文本域内容，返回指定长度的字符，用于补全短文本域长度
 * @param field
 * @param ori_str
 * @param max_str
 */
export function createCustomWidthCharToField(field: XField, ori_str: string, max_str: string) {
  const cur_width = field.textToFieldCharacter(ori_str).reduce(function (total, currentValue) {
    return total + currentValue.width;
  }, 0);
  const max_width = field.textToFieldCharacter(max_str).reduce(function (total, currentValue) {
    return total + currentValue.width;
  }, 0);
  const width = max_width - cur_width;
  const char = new Character(new Font(field.style), " ", width > 0 ? width : 0);
  char.field_id = field.id;
  return char;
}

/**
 * 处理复制加密字符串为cell
 * @param editor 编辑器对象
 * @param str 为复制的加密字符串
 * @param isHandleFont  默认不处理字体样式加入至FontMap中
 */
export function handleCopyDataToParas(editor: Editor, str: string, isHandleFont: boolean = false)
  : { cell: Cell, hasLastLinebreak: boolean, rawDataContent: any, fontStr: string } {
  const param = str.slice(Config.program_flag_start.length);
  const base64Data = caesarCipher(param, Config.caesar_shift, true);
  str = decodeURIComponent(decodeBase64(base64Data)); // 从base64转回原字符串
  const hasLastLinebreak = str.includes(Config.program_flag_end); // 自己定义的标识符 用来判断末尾是否有换行符
  hasLastLinebreak && (str = str.replace(Config.program_flag_end, ""));
  const arr = str.split(Config.font_map_flag);
  const fontStr = arr.splice(1, 1).join("");
  if (isHandleFont) {
    const fontObj = JSON.parse(fontStr);
    EditorHelper.mergeTemplateFontMap(fontObj, editor);
  }
  const rawDataContent = JSON.parse(arr.join(""));
  const insertTemplateUseDefaultSetting = editor.config.insertTemplateUseDefaultSetting;
  if (insertTemplateUseDefaultSetting && insertTemplateUseDefaultSetting.rowRatio) {
    rawDataContent.forEach((p: any) => {
      p.row_ratio = editor.config.row_ratio;
      if (p.type === "table") {
        const cells = p.cells;
        cells.forEach((cell: any) => {
          const children = cell.children;
          children.forEach((para: any) => {
            para.row_ratio = editor.config.row_ratio;
          });
        });
      }
    });
  }
  const cell = editor.getCellByRawNodeList(rawDataContent)!;
  return { cell, hasLastLinebreak, rawDataContent, fontStr };
}

/**
 * 获取段落中所有的字符串
 */
export function getParagraphsText(paragraphs: (Paragraph | Table)[]) {
  const textArr: any[] = [];
  paragraphs.forEach((container: Paragraph | Table) => {
    textArr.push(container.getStr(true));
  });
  return textArr.join("");
}

/**
 * 处理复制出来的数据
 */
export function handleCopyData(cell: Cell, focus_field: XField | null, para_path: Path, editor: Editor, isInsertTemplate: boolean = false): (Table | Paragraph)[] {
  // 获取编辑器中所有文本域对象
  const allFields = editor.getAllFields(); // 包括了页眉页脚
  // 获取新cell中所有文本域对象
  const allCopyFields = editor.getAllFields(cell); // 获取到的文本域是复制出来的文本域 因为传递进去的 cell 是新 new 出来的 不是 root_cell 或者单元格 和页眉页脚的 cell
  const allFieldIds = allFields.map((field: XField) => field.id);
  // 获取当前光标所在段落，获取cell
  const cur_paragraph = editor.selection.getParagraphByPath(para_path);
  const current_cell = cur_paragraph.cell; // 如果是 root_cell 的话 里边的 fields 是不包括 单元格里边的文本域的
  // 此时需要将外层文本域的
  for (let i = 0; i < allCopyFields.length; i++) {
    const copy_field = allCopyFields[i];
    // 如果当前文档中有与需要复制到编辑器中相同id的文本域，则重置其id
    if ((allFieldIds as any).includes(copy_field.id)) {
      copy_field.id = uuid("field-paste");
      copy_field.updateCharactersFieldId();
    }
    // 如果只是文本域内文本，将最外层文本域id置空（如果复制到文本域中则更改为焦点文本域的id）
    // 说明是在一个表格内，不需处理，如果在表格外则将其放入当前cell
    if (!copy_field.cell.parent) { // 复制了若干单元格或者整个表格 单元格里边的文本域 .cell.parent 才有值 并不是 从单元格里边复制的文本域或者往单元格里边粘贴的文本域 .cell.parent 有值
      // const insert_index = current_cell.fields.length; // getInsertIndexInCurrentCellFields(para_path, current_cell.fields); 不用该方法 是因为复制多个文本域 current_cell.fields 里边新复制的文本域还没有在编辑器里显示出来的 是没有 start_para_path 的
      current_cell.fields.push(copy_field);
      // 更新field的cell属性
      copy_field.cell = current_cell;
    }
    if (!copy_field.parent && focus_field) {
      copy_field.parent = focus_field;
    }
    EditorHelper.mergeTemplateFontMap(copy_field, editor);
  }

  cell.paragraph.forEach((paraOrTbl) => {
    if (isTable(paraOrTbl)) {
      // 表格、段落复制后粘贴时id重新赋值，否则会造成分组bug
      paraOrTbl.id = uuid("table-paste");
      // 因为数据转换 表格的parent属性不正确了 所以这儿要修改为正确的
      paraOrTbl.parent = editor.current_cell;
    }
    if (isParagraph(paraOrTbl)) {
      paraOrTbl.id = uuid("para-paste");
      // 非插入模板时使用
      if (!isInsertTemplate && paraOrTbl.title_length === 0) {
        paraOrTbl.content_padding_left = 0;
      }
      if (focus_field) {
        // 普通文本字符重置id
        paraOrTbl.characters.forEach((ele) => {
          if (!ele.field_id) {
            ele.field_id = focus_field.id;
          }
        });
      }
    }
  });
  return cell.paragraph;
}
/**
 * 用于处理粘贴时的段落字符,用于拼接到对应的focus_field中
 * @param current
 * @param isInsertTemplate 是否为插入模板调用
 */
export function handlePasteParaChars(current: Paragraph, isInsertTemplate: boolean = false, isReplace: boolean = false, keepOrigin: boolean = false) {
  const all_elements: (XField | Character | Image | Widget | Button)[] = [];
  const exist_fields: XField[] = [];
  for (let i = 0, len = current.characters.length; i < len; i++) {
    const char = current.characters[i];
    if (char.field_id) {
      const field = current.cell.getFieldById(char.field_id);
      if (field) {
        if (!(exist_fields as any).includes(field)) {
          exist_fields.push(...field.getFieldInChildren());
          if (!isInsertTemplate && !isReplace && !keepOrigin && field.type !== "box") {
            // 复制粘贴的文本域设置为可编辑、可删除
            field.deletable = 1;
            field.readonly = 0;
          }
          all_elements.push(field);
        }
      } else {
        all_elements.push(char);
      }
    } else {
      all_elements.push(char);
    }
  }
  return all_elements;
}

/**
 * 处理分组中的页眉信息，补全文本域宽度，保证每页页眉内容高度一致
 */
// export function handleGroupsHeaderInfo(editor: Editor, header_cell: Cell) {
//   const lengthInfo = getHeaderFieldMaxLength(editor.root_cell);
//   // 处理原始数据中的内容
//   const fields = editor.getAllFields(header_cell);
//   for (let i = 0; i < fields.length; i++) {
//     const field = fields[i];
//     // 此处只刷新最内层文本域，否则会出问题
//     if (field.getFieldInChildren().length > 1) {
//       continue;
//     }
//     // 如果内容中包含换行也不进行替换操作
//     if (field.children.findIndex((char) =>
//       ((isCharacter(char)) && char.value === "\n")) > -1) {
//       continue;
//     }
//     let field_key = field.id;
//     let info = lengthInfo[field_key];
//     if (!info) {
//       field_key = field.name;
//       info = lengthInfo[field_key];
//     }
//     if (!info) {
//       continue;
//     }
//     const text = field.text.trim();
//     const { width } = Renderer.measure(new Font(field.style), text, editor);
//     if (width < info.width) {
//       const chars = field.textToFieldCharacter(text);
//       field.clear();
//       chars.push(createCustomWidthCharToField(field, text, info.value));
//       field.children = chars;
//       field.updateChildren();
//     }
//   }
// }

/**
 * 上下文字体样式随当前光标设置
 * @param path
 * @param offset
 * @param containers
 * @param current_container
 */
export function contextStateSetByCaret(editor: Editor, path: Path, offset: number, containers: Row[], current_container: Row) {
  let character: Character | Image | Widget | Line | Box | Button;
  let container: Row = current_container;
  // 当光标在行首
  if (path[0] === 0) {
    // 上一行无换行符，context 取上一行末尾字符的 font
    if (
      offset !== 0 &&
      isRow(containers[offset - 1]) &&
      !(containers[offset - 1] as Row).linebreak
    ) {
      container = containers[offset - 1] as Row;
      character = container.children[container.children.length - 1];
    } else {
      // 光标在行首且无上一行或者上一行以换行符结尾，取当前行第一个字符或者当前行换行符font为上下文font
      if (current_container.children.length) {
        character = current_container.children[0];
      } else {
        // 当前行只存在一个换行符
        character = current_container.linebreak!;
      }
    }
  } else {
    character = current_container.children[path[0] - 1];
  }
  // 如果当前字符不是普通字符，是小组件或者图片时，继续取当前段落当前字符的上一个字符，一直取到行首如果仍不是正常字符，则取换行符
  if (!(isCharacter(character))) {
    // 获取符合条件的字符
    const getParagraphFitChar = function (paragraph: Paragraph, character: Image | Widget | Line | Box | Button): Character {
      const p_index = paragraph.characters.indexOf(character) - 1;
      if (p_index < 0) {
        return paragraph.lastCharacter;
      }
      const element = paragraph.characters[p_index];
      if (isCharacter(element)) {
        return element;
      } else {
        return getParagraphFitChar(paragraph, element);
      }
    };
    character = getParagraphFitChar(container.paragraph, character);
  }

  // 非格式刷与调用更改样式接口时
  if (!editor.format_brush) {
    if (character.field_position === "normal") {
      editor.contextState.setFontState((character as Character).font);
    } else if (character.field_id) {
      const field = container.paragraph.cell.getFieldById(character.field_id)!;
      editor.contextState.setFontState(new Font(field.style));
    }
  }
}

/**
 *  插入段落数据（由复制的数据或者是模板数据转换成的段落）
 * @param sel
 * @param para_path
 * @param field 根据光标位置 获取 文本域
 * @param template_data 复制出来的数据,段落list
 * @param hasLastLinebreak
 * @param isInsertTemplate 是否是插入模板调用
 */
export function insertParagraphsHelper(
  sel: XSelection,
  para_path: Path,
  field: XField | null,
  template_data: any[],
  hasLastLinebreak: boolean,
  isInsertTemplate: boolean = false,
  inParaEndNotFirst: boolean = false,
  isReplace: boolean = false,
  keepOrigin: boolean = false
) {
  const para_and_tbl_arr = sel.editor.current_cell.paragraph;// 第一段
  const current_para_tbl = para_and_tbl_arr[para_path[0]]; // 光标所在的位置 可能是paragraph 或者是 table
  const currentGroupId = current_para_tbl.group_id; // 因为一次粘贴或者插入模板的内容 不可能在两个分组里边或者部分在分组部分在外边 所以粘贴的内容要统一赋值当前的分组 id
  // const nextGroupId = para_and_tbl_arr[para_path[0] + 1]?.group_id;
  // console.log(nextGroupId, "下一个分组Id");
  if (template_data.length === 0) return;
  if (!sel.isCollapsed) {
    // 选区情况下 要删除
    sel.editor.delete_backward();
  }
  // let index: number = current_para_tbl.para_index; // 当前段落或者表格的下标 每插入一个段落或者表格或者换行符就要+1
  const out_obj = { index: current_para_tbl.para_index };
  const default_font = sel.editor.fontMap.add(sel.editor.config.default_font_style);
  // 模板数据中开头是否为表格
  let templateDataStartIsTable = false;
  // 抽离的往段落内粘贴的共有逻辑 - start
  /**
   *
   * @param i
   * @param len
   * @param current
   * @param para_and_tbl_arr
   * @param isInTbl
   */
  const pasteData2Para = (
    i: number,
    len: number,
    current: Paragraph,
    para_and_tbl_arr: (Paragraph | Table)[],
    obj: { index: number },
    isInTbl: boolean,
    keepOrigin: boolean
  ) => {
    if (i === len - 1 && (!hasLastLinebreak || isInsertTemplate)) {
      // 说明是末尾了 要根据 hasLastLinebreak属性判断有没有复制上换行符
      current.characters.pop(); // 删除掉自身的末尾\n
      if (isInsertTemplate) { // 插入模板 最后一行 要按照模板样式来设置
        (para_and_tbl_arr[obj.index] as Paragraph).setParagraphAttr(current);
        // 最后一段虽然逻辑上保留，但是实际上是直接使用模板中的，所以段落id应该重置。
        if (templateDataStartIsTable) {
          (para_and_tbl_arr[obj.index] as Paragraph).id = uuid("para-");
          (para_and_tbl_arr[obj.index] as Paragraph).paraGroup?.refreshContentParaId();
        }
        (para_and_tbl_arr[obj.index] as Paragraph).title_length = current.title_length;
      }
      // 处理当前段落中的所有字符
      (para_and_tbl_arr[obj.index] as Paragraph).insertElements(
        current.characters,
        para_path[para_path.length - 1],
        field,
        handlePasteParaChars(current, isInsertTemplate, isReplace, keepOrigin)
      );
      const character_pos = current.characters.length + para_path[para_path.length - 1];
      if (isInTbl) {
        const cursor_path = sel.editor.paraPath2ModelPath([
          para_path[0], // table
          para_path[1], // cell
          obj.index, // paragraph
          character_pos // character
        ]);
        sel.setCursorPosition(cursor_path);
      } else {
        para_path = [obj.index, character_pos];
        const cursor_path = sel.editor.paraPath2ModelPath(para_path);
        sel.setCursorPosition(cursor_path);
      }
    } else {
      const enterCharIndex = current.characters.findIndex(char => char.value === "\n");
      const focus_paragraph = sel.getParagraphByPath(sel.para_focus);
      if (enterCharIndex > -1) {
        const enterChar = current.characters[enterCharIndex];
        current.characters.pop();
        // 新增 解决跨段文本域复制到文本域内 错乱的问题 ↓
        let pointField = field;
        if (field) {
          const resField = sel.editor.getFieldById(enterChar.field_id);
          if (resField && (resField.getAllParents() as any).includes(field)) { pointField = resField; }
          const field_enter_index = pointField?.children.indexOf(enterChar);
          // 如果文本域中存在该字符，则不需要再往文本域中插入该字符，通过给pointField赋值成为null实现
          if (field_enter_index !== undefined && field_enter_index > -1) {
            pointField = null;
          }
        }
        focus_paragraph.insertEnterChar(para_path[para_path.length - 1], enterChar, enterChar.field_id ? pointField : null);
        // 新增 解决跨段文本域复制到文本域内 错乱的问题 ↑
        // focus_paragraph.insertEnterChar(para_path[para_path.length - 1], enterChar, enterChar.field_id ? field : null);
      } else {
        focus_paragraph.insertText("\n", para_path[para_path.length - 1], default_font, field);
      }
      (para_and_tbl_arr[obj.index] as Paragraph).group_id = currentGroupId;
      // 肯定有换行符
      if (para_path[para_path.length - 1] === 0) {
        // 如果在开头插入的话，要设置段落属性为复制的段落属性 有居中属性的 可以居中
        (para_and_tbl_arr[obj.index] as Paragraph).setParagraphAttr(current);
        if (isInsertTemplate && templateDataStartIsTable) {
          // 最后一段虽然逻辑上保留，但是实际上是直接使用模板中的，所以段落id应该重置。
          (para_and_tbl_arr[obj.index] as Paragraph).id = uuid("para-");
          (para_and_tbl_arr[obj.index] as Paragraph).paraGroup?.refreshContentParaId();
        }
        (para_and_tbl_arr[obj.index] as Paragraph).title_length = current.title_length;
      }
      // 处理当前段落中的所有字符
      (para_and_tbl_arr[obj.index] as Paragraph).insertElements(
        current.characters,
        para_path[para_path.length - 1],
        field,
        handlePasteParaChars(current, isInsertTemplate, false, keepOrigin)
      );
      if (isInTbl) {
        para_path = [para_path[0], para_path[1], obj.index + 1, 0];
        const cursor_path = sel.editor.paraPath2ModelPath(para_path);
        sel.setCursorPosition(cursor_path);
        obj.index++;
      } else {
        para_path = [obj.index + 1, 0];
        const cursor_path = sel.editor.paraPath2ModelPath(para_path);
        sel.setCursorPosition(cursor_path);
        obj.index++;
      }
    }
  };

  if (isTable(current_para_tbl)) {
    // 如果复制的内容中有表格 就不能复制
    if (!validateInsertContent(sel, template_data, field, current_para_tbl)) {
      return;
    }
    const current_cell = current_para_tbl.children[para_path[1]];
    const current_paragraphs = current_cell.paragraph as Paragraph[];
    const obj = { index: current_paragraphs[para_path[2]].para_index };
    for (let i = 0, len = template_data.length; i < len; i++) {
      const current = template_data[i] as Paragraph;
      // 如果当前段落有换行符 就先插入换行符 然后把上一段落的\n替换成当前段落的characters 如果没有换行符就不用插换行符，直接把characters直接塞进当前光标位置即可
      pasteData2Para(i, len, current, current_paragraphs, obj, true, keepOrigin);
    }
  } else {
    if (!validateInsertContent(sel, template_data, field, current_para_tbl)) {
      return;
    }
    for (let i = 0, len = template_data.length; i < len; i++) {
      const current = template_data[i];
      if (isTable(current)) {
        if (i === 0) {
          templateDataStartIsTable = true;
        }
        current.group_id = sel.editor.current_cell.paragraph[out_obj.index].group_id;
        // 只有复制的表格在开头时(只有一个表格时也会走i===0) 并且不是在段落开头或者光标所在段的前一段是表格的时候 才需要插入换行符，否则连续选择表格段落就会出现空行
        if (i === 0 && (!PathUtils.isStartPathInPara(para_path) || (isTable(current_para_tbl.previousParagraph)))) {
          if (insertTblInsLinebreak(para_path, sel) ||
            PathUtils.isEndPathInPara(para_path, sel.getParagraphByPath(sel.para_focus))) {
            if (inParaEndNotFirst) {
              // 插入换行符 ↓
              if (i === 0 && current_para_tbl.characters.length - 1 === para_path[para_path.length - 1]) { // 如果第一个是表格 并且在插入位置处末尾的话 并且还不是单独的表格的时候 就要插入换行符
                const enterChar = current_para_tbl.characters[current_para_tbl.characters.length - 1];
                current_para_tbl.insertEnterChar(para_path[para_path.length - 1], enterChar, null);
              }
              // 插入换行符 ↑
            }
            out_obj.index++;
          }
        }

        sel.editor.current_cell.insertTableByParagraphIndex(current, out_obj.index);
        para_path = [++out_obj.index, 0]; // 因为又插入了一个表格 所以index 还要++
        const cursor_path = sel.editor.paraPath2ModelPath(para_path);
        sel.setCursorPosition(cursor_path);
      } else if (isParagraph(current)) {
        // 如果当前段落有换行符 就先插入换行符 然后把上一段落的\n替换成当前段落的characters 如果没有换行符就不用插换行符，直接把characters直接塞进当前光标位置即可
        pasteData2Para(
          i,
          len,
          current,
          para_and_tbl_arr,
          out_obj,
          false,
          keepOrigin
        );
      }
    }
  }
}

/**
 * 验证粘贴内容
 */
function validateInsertContent(sel: XSelection,
  copy_data: any[],
  field: XField | null,
  current_para_tbl: Paragraph | Table)
  : boolean {
  // 分组如果锁定 则不能编辑
  if (!sel.editor.operableOrNot(["cell", "group"])) return false;
  // 如果复制的内容中有表格 就不能复制 ，文本域中也不能粘贴表格
  for (let i = 0, len = copy_data.length; i < len; i++) {
    const current = copy_data[i];
    if (isTable(current)) {
      if (isTable(current_para_tbl) || field) {
        return false;
      }
    }
  }
  return true;
}

/**
 * 删除二维数组中的一些数组
 * @param source_arr 原数组
 * @param del_arr 要删除的数组
 * @returns 操作完成的数组
 */
export function deleteTwoDimArrElement(source_arr: number[][], del_arr: number[][]) {
  const changed_result: number[][] = [];
  /* eslint-disable */
  out: for (let i = 0; i < source_arr.length; i++) {
    const element = source_arr[i];
    for (let j = 0; j < del_arr.length; j++) {
      const del_ele = del_arr[j];
      if (del_ele[0] === element[0] && del_ele[1] === element[1]) {
        continue out;
      }
    }
    changed_result.push(element);
  }
  /* eslint-enable */
  return changed_result;
}
/**
 * 插入表格时判断是否需要插入换行符
 * @para_path 光标所在位置
 * @selection 选区
 */
export function insertTblInsLinebreak(para_path: Path, selection: XSelection): boolean {
  const paragraph = selection.getParagraphByPath(para_path);
  if (PathUtils.isStartInDocument(para_path)) {
    return false;
  }

  // 如果上一行为表格，并且当前光标在段首，则需要插入空行 ；或者 当前在段末并且下一行不是段落 ；或者当前光标在段落中间
  if (PathUtils.isMiddlePathInPara(para_path, paragraph) ||
    (PathUtils.isStartPathInPara(para_path) && isTable(paragraph.previousParagraph)) ||
    (PathUtils.isEndPathInPara(para_path, paragraph) && !PathUtils.isStartPathInPara(para_path) && !isParagraph(paragraph.nextParagraph))) {
    paragraph.insertText("\n", para_path[1], selection.editor.fontMap.add(selection.editor.config.default_font_style));
    return true;
  }
  return false;
}

/**
 * 插入文本后的光标设置
 * @param splitText
 * @param current_paragraph
 * @param para_path
 */
export function insertTextAfterCursorSet(editor: Editor, splitText: string, current_paragraph: Paragraph, para_path: Path) {
  // 获取上一行最后字符 将上一行的最后一个字符拼接上，用于设置光标所在位置
  if (splitText === "\n") {
    // 因为段落末尾字符排版，所以此处不能单纯的往后移动一步
    // this.selection.stepForward(1, focus_row);
    // 回车操作下一段新创建的必然是段落
    const next_row_cell_index = (current_paragraph.nextParagraph as Paragraph).children[0].cell_index;
    let cur_focus_path = [...editor.selection.focus];
    if (PathUtils.isTablePath(cur_focus_path)) {
      cur_focus_path.splice(2, 2, next_row_cell_index, 0);
    } else {
      cur_focus_path = [next_row_cell_index, 0];
    }
    editor.selection.setCursorPosition(cur_focus_path);
  } else {
    PathUtils.movePathCharNum(para_path, splitText.length);
    const model_path = editor.paraPath2ModelPath(para_path);
    editor.selection.setCursorPosition(model_path);
  }
}

/**
 * 更新文本域内容辅助函数
 * @param editor
 * @param fields
 * @param params
 */
export function updateFieldTextHelper_RootCell(editor: Editor, fields: XField[], params: any, isDocumentAlign?: boolean) {
  for (let i = 0; i < fields.length; i++) {
    const field = fields[i];
    if (field.isRemove || (field.readonly && !editor.adminMode)) {
      continue;
    }
    const groupId = editor.root_cell.paragraph[field.start_para_path[0]].group_id!;
    const group = editor.selection.getGroupByGroupId(groupId);
    if (group && group.lock && !editor.adminMode) {
      continue;
    }

    const paramsValueIsValid = params.value !== null && params.value !== undefined;
    // 当前需要处理的文本域新值是有效的
    const textInFieldIsValid = field.new_text !== null && field.new_text !== undefined;

    const simpleReplace = () => {
      if (textInFieldIsValid) {
        params.append ? field.appendText(field.new_text!) : field.replaceText(field.new_text!);
        field.new_text = null;
      } else {
        params.append ? field.appendText(params.value) : field.replaceText(params.value);
      }
    }

    if(String(field.cell.id).startsWith("ext_cell")){
      simpleReplace();
      continue;
    }

    // 如果是字符对齐，则内容不允许为空字符串或者换行符
    if (isDocumentAlign) {
      if (String(field.new_text).replace(/\\n/g, "") === "") {
        continue;
      }
    }
    const startParaPathInField = field.start_para_path_inner;
    // 转换为modelpath
    const startModelPathInField = editor.paraPath2ModelPath(startParaPathInField, "content");
    if (params.append) {
      editor.locatePathInField(field, "end");
      editor.updateCaret(); // 因为 locatePathInField 只改了 anchor 和 focus 并没有改变修改上下文样式为光标处的上下文样式 所以调用一下 updateCaret
    } else {
      editor.selection.setCursorPosition(startModelPathInField);
      editor.contextState.setFontState(editor.fontMap.add(field.style));
    }

    if (field.type === "date") {
      field.new_text = judgeStringIsDateFormat(field.new_text!, field.replace_format);
      simpleReplace();
    } else if (field.type === "number") {
      field.new_text = field.new_text ?? field.text;
      field.new_text = formatTheNumberInNumberField(field.new_text, field.number_format);
      params.append ? field.appendText(field.new_text) : field.replaceText(field.new_text);
      field.new_text = null;
    } else if (field.type === "label") {
      simpleReplace();
      editor.locatePathInField(field);
    } else if (field.cell.meta.isExtCell || field.cell.parent?.parent.meta.isExtCell) {
      // 锚点文本域隐藏cell中的文本域替换使用单独逻辑处理
      simpleReplace();
      editor.locatePathInField(field);
    } else {
      // 如果不是追加，则是替换（替换也只是给替换的值了 才替换 要考虑 值可能为 ""）
      if (!params.append && (paramsValueIsValid || textInFieldIsValid)) {
        field.clear();
        field.reShowPlaceholder();// 重新展示文本域背景文本，必须存在
        // 清空内容后需要重新定位坐标，否则原设置的光标可能不正确
        editor.locatePathInField(field);
      }
      if (textInFieldIsValid) {
        EditorHelper.insertText(editor, field.new_text!, "transit");
        field.new_text = null;
      } else {
        paramsValueIsValid && editor.insertText(params.value, "transit");
      }
    }
    // 对齐设置
    if (isDocumentAlign) {
      const endModelPathInField = editor.paraPath2ModelPath(field.end_para_path);
      // 设置文档对齐
      EditorHelper.setDocumentAlign(editor, startModelPathInField, endModelPathInField);
    }
  }
}

// 是否使用当前逻辑 判断 useBeforeVersion 和 本地的 transUse 这俩有一个不符合要求就不走当前逻辑
export function isUseCurrentLogic(editor: Editor) {
  return !editor.config.useBeforeVersion && EditorLocalTest.transUse;
}
/*  文字形式的时间转换为时间毫秒数
    0: "YYYY-MM-DD",
    1: "YYYY-MM-DD HH:mm",
    2: "YYYY-MM-DD HH:mm:ss",
    3: "YYYY年MM月DD日",
    4: "YYYY年MM月DD日 HH时",
    5: "YYYY年MM月DD日 HH时mm分",
    6: "MM-DD",
    7: "MM月DD日",
    8: "HH:mm",
    9: "HH时mm分",
    10: "YYYY年MM月DD日 HH时mm分ss秒",
    11："MM-DD HH:mm" */
export function stringDateTransformNormDate(text: string) {
  let date = "";
  let type = "日";
  if (!isNaN(Date.parse(text))) {
    date = text;
    if (text.length === 18) {
      type = "秒";
    } else if (text.length === 19) {
      type = "秒";
    } else if (text.length === 16) {
      type = "分";
    } else if (text.length === 14) {
      type = "时";
    } else if (text.length === 5) {
      type = "日";
      const newDate = new Date();
      const year = newDate.getFullYear();
      date = year + "-" + text;
    } else if (text.length === 11) {
      type = "分";
      const newDate = new Date();
      const year = newDate.getFullYear();
      date = year + "-" + text;
    }
  } else {
    if (text.indexOf("年") !== -1) {
      const yearText = text.split("年");
      const monthText = yearText[1].split("月");
      const dayText = monthText[1].split("日");
      date = yearText[0] + "-" + monthText[0] + "-" + dayText[0] + " ";

      if (dayText[1].indexOf("时") !== -1) {
        const hourText = dayText[1].split("时");
        date += hourText[0] + ":";
        type = "时";
        if (hourText[1].indexOf("分") !== -1) {
          const minText = hourText[1].split("分");
          date += minText[0] + ":";
          type = "分";
          if (minText[1].indexOf("秒") !== -1) {
            const secondText = minText[1].split("秒");
            date += secondText[0];
            type = "秒";
          }
        }
      }
    } else {
      const newDate = new Date();
      const year = newDate.getFullYear();
      const month = newDate.getMonth() + 1;
      const day = newDate.getDate();

      if (text.indexOf("月") !== -1) {
        date = year + "-";
        const monthText = text.split("月");
        date += monthText[0];
        if (monthText[1].indexOf("日") !== -1) {
          const dayText = monthText[1].split("日");
          date += "-" + dayText[0];
        }
      } else {
        date = year + "-" + month + "-" + day + " ";
        if (text.indexOf("时") !== -1) {
          const hourText = text.split("时");
          date += hourText[0];
          type = "时";
          if (hourText[1].indexOf("分") !== -1) {
            const minText = hourText[1].split("分");
            date += ":" + minText[0];
            type = "分";
          }
        } else {
          if (text.indexOf(":") !== -1) {
            const hourText = text.split(":");
            date += hourText[0] + ":" + hourText[1];
            type = "分";
          }
        }
      }
    }
  }

  date = date.replace(/-/g, "/");
  return { date: Date.parse(date), type: type };
}
/* 0: "YYYY-MM-DD",
    1: "YYYY-MM-DD HH:mm",
    2: "YYYY-MM-DD HH:mm:ss",
    3: "YYYY年MM月DD日",
    4: "YYYY年MM月DD日 HH时",
    5: "YYYY年MM月DD日 HH时mm分",
    6: "MM-DD",
    7: "MM月DD日",
    8: "HH:mm",
    9: "HH时mm分",
    10: "YYYYMM月DD日 HH时mm分ss秒",
    11："MM-DD HH:mm" */
export function judgeStringIsDateFormat(text: string, format: number) {
  text = String(text);
  // const dateFormat = ["YYYY-MM-DD", "YYYY-MM-DD HH:mm", "YYYY-MM-DD HH:mm:ss", "YYYY年MM月DD日", "YYYY年MM月DD日 HH时", "YYYY年MM月DD日 HH时mm分", "MM-DD", "MM月DD日"];
  const dateRule = /^([1-2][0-9][0-9][0-9]-[0-1]{0,1}[0-9]-[0-3]{0,1}[0-9])\s(20|21|22|23|[0-1]\d):[0-5]\d:[0-5]\d$/;
  // const nyr = /^[1-2][0-9][0-9][0-9]年[0-1]{0,1}[0-9]月[0-3]{0,1}[0-9]日$/;
  // const nyrs = /^[1-2][0-9][0-9][0-9]年[0-1]{0,1}[0-9]月[0-3]{0,1}[0-9]日\s{0,1}([01]?[0-9]|2[0-3])时$/;
  // const nyrsf = /^[1-2][0-9][0-9][0-9]年[0-1]{0,1}[0-9]月[0-3]{0,1}[0-9]日\s{0,1}([01]?[0-9]|2[0-3])时[0-6]{0,1}[0-9]分$/;
  // const yr = /^[0-1]{0,1}[0-9]月[0-3]{0,1}[0-9]日$/;
  text = text.split(".")[0];
  if (isNaN(Number(text)) && !isNaN(Date.parse(text)) && text.match(dateRule)) {
    const year = text.split("-")[0];
    const month = text.split("-")[1];
    const day = text.split("-")[2].split(" ")[0];
    const hour = text.split(" ")[1].split(":")[0];
    const minute = text.split(" ")[1].split(":")[1];
    const second = text.split(" ")[1].split(":")[2];
    if (format === 0) {
      text = text.split(" ")[0];
    } else if (format === 1) {
      text = text.slice(0, 16);
    } else if (format === 3) {
      text = year + "年" + month + "月" + day + "日";
    } else if (format === 4) {
      text = year + "年" + month + "月" + day + "日 " + hour + "时";
    } else if (format === 5) {
      text = year + "年" + month + "月" + day + "日 " + hour + "时" + minute + "分";
    } else if (format === 6) {
      text = text.slice(5, 10);
    } else if (format === 7) {
      text = month + "月" + day + "日";
    } else if (format === 8) {
      text = hour + ":" + minute;
    } else if (format === 9) {
      text = hour + "时" + minute + "分";
    } else if (format === 10) {
      text = year + "年" + month + "月" + day + "日 " + hour + "时" + minute + "分" + second + "秒";
    } else if (format === 11) {
      text = month + "-" + day + " " + hour + ":" + minute;
    }
  }
  return text;
}

export function formatTheNumberInNumberField(text: string, numberFormat: number) {
  const parsedNumber = parseFloat(text);
  if (isNaN(parsedNumber)) {
    return text;
  }

  let formattedNumber: string;
  if (numberFormat === 0) {
    formattedNumber = parsedNumber.toString(); // 无位数限制，保持原样
  } else if (numberFormat === 1) {
    formattedNumber = Math.round(parsedNumber).toString(); // 保留整数部分
  } else {
    if(!numberFormat){
      formattedNumber = parsedNumber.toString(); // 无位数限制，保持原样
    }else{
      const decimals = Math.min(numberFormat - 1, 20); // 最多保留20位小数
      const factor = Math.pow(10, decimals);
      const roundedNumber = Math.round(parsedNumber * factor) / factor; // 保留指定小数位数并进行四舍五入
      formattedNumber = roundedNumber.toFixed(decimals);
    }
  }

  return formattedNumber;
}

/**
 *判断原始属性值是否为undefined，如果是则不进行赋值
 * @param model 模型对象
 * @param raw 原始数据对象
 * @param props 属性数组
 */
export function judgeUndefinedAssign(model: any, raw: any, props: string[] = []) {
  for (let i = 0, len = props.length; i < len; i++) {
    const prop = props[i];
    if (raw[prop] === undefined) {
      continue;
    }
    model[prop] = raw[prop];
  }
}

/**
 * 更新页眉页脚文本域的辅助函数
 */
export function updateFieldTextHelper_HeaderFooter(fields: XField[], params: any) {
  for (let i = 0; i < fields.length; i++) {
    const field = fields[i];
    if (field.isRemove) {
      continue;
    }

    if (isNotNullAndUndefined(field.new_text)) {
      if (field.type === "date" && !params.append) {
        field.new_text = judgeStringIsDateFormat(field.new_text, field.replace_format);
      }
      params.append ? field.appendText(field.new_text) : field.replaceText(field.new_text);
      field.new_text = null;
    } else if (isNotNullAndUndefined(params.value)) {
      if (field.type === "date" && !params.append) {
        params.value = judgeStringIsDateFormat(params.value, field.replace_format);
      }
      params.append ? field.appendText(params.value) : field.replaceText(params.value);
    }
  }
}

export function isGroup(item: any): item is Group {
  return item instanceof Group;
}

export function isImage(item: any): item is Image {
  return item instanceof Image;
}

export function isCell(item: any): item is Cell {
  return item instanceof Cell;
}

export function isWidget(item: any): item is Widget {
  return item instanceof Widget;
}

export function isButton(item: any): item is Button {
  return item instanceof Button;
}

// 是否是分数
export function isFraction(item: any): item is Fraction {
  return item instanceof Fraction;
}

export function isBoxField(item: any): item is BoxField {
  return item instanceof BoxField;
}

export function isBox(item: any): item is Box {
  return item instanceof Box;
}

export function isLine(item: any): item is Line {
  return item instanceof Line;
}
/**
 * 判断元素是否是 ImageTable
 * @param item 可以是实例也可以是 node 在 insert_raw 里边也用到该方法了
 * @returns item is ImageTable
 */
export function isImageTable(item: any): item is ImageTable {
  return item.isImageTable;
}

/**
 * 判断元素是否在视口内
 * @param editor Editor
 * @param top 元素的 top 值 是相对于画布原点的值
 * @param bottom 元素的 bottom 值 是相对于画布原点的值
 * @returns 返回是否在视口内
 */
export function isInViewport(editor: Editor, top: number, bottom: number): boolean {
  if (editor.print_mode) return true; // 如果是打印模式就应该都在视口内

  if (editor.isMobileTerminal()) {
    top = top * editor.viewScale * window.devicePixelRatio - Math.abs(editor.offsetY) * window.devicePixelRatio;
    bottom = bottom * editor.viewScale * window.devicePixelRatio - Math.abs(editor.offsetY) * window.devicePixelRatio;
    if (((top < 0 && bottom > 0) || (top > 0 && bottom < editor.init_canvas.height)) || (top < editor.init_canvas.height && bottom > editor.init_canvas.height)) {
      return true;
    } else {
      return false;
    }
    // return bottom * editor.viewScale > editor.offsetY * -1 && top * editor.viewScale < editor.offsetY * -1 + editor.init_canvas.height
  }


  // 浏览器进行缩放的时候会导致 window.devicePixelRatio 的变化,缩小时值变小,放大时值变大
  // 当本来的 top 值距离原点有 100px 但是经过缩小之后,因为整个浏览器界面都缩小了,所以距离原点肯定就不是 100px 了,所以要乘以 window.devicePixelRatio
  // 但是浏览器的高度依然是占据整个电脑屏幕的,意思就是说 canvasDOM 这个容器的高度是不变的(editor.init_canvas.height的高度不变,正如浏览器的高度不变一样)
  // editor.scroll_top 误差不大 不是很好测 但是如果 editor.scroll_top 不乘的话 在页面放大的时候就会展示第一页,下边的页展示不全了
  // 第一个 && 之前 bottom > editor.scroll_top 不用都乘了 因为可以互相抵消了
  const ratio = editor.viewScale * window.devicePixelRatio;
  return bottom > editor.scroll_top && top * ratio < editor.scroll_top * ratio + editor.init_canvas.height;
}

export function forEachFlatParagraph(cell: Cell, callBack: (paragraph: Paragraph, cell?: Cell, table?: Table) => void) {
  const containers = cell.paragraph;
  for (let i = 0; i < containers.length; i++) {
    const container = containers[i];
    if (isTable(container)) {
      for (const cell of container.children) {
        for (const paragraph of cell.paragraph) {
          callBack((paragraph as Paragraph), cell, container);
        }
      }
    } else {
      callBack(container);
    }
  }
}

// 手麻1
export function filterData (editorData: any[]) {
  // editorData 是获取到的全部的文本域集合
  const editorArray: any[] = [];
  editorData.forEach((item) => {// 循环拿到的所有的文本域
    if (item.parent === null) {
      const edit: any = {
        // 字典
        termDict: [],
        // 类型
        type: "",
        // 更改前的页面中类型
        oldType: item.type,
        value: "",
        name: item.id.indexOf("#") > -1 ? item.id : item.name,
        termColumnName: item.meta.remark || item.placeholder || "",
      };
      // 签名id数据
      const signEdit: any = {
        termDict: [], // 字典
        type: "", // 类型
        value: "",
        name: item.name,
        termColumnName: item.meta.remark || item.placeholder || "",
      };
      // 处理单选框/复选框/复选框（有字典)
      if (item.type === "box" || item.type === "select") {
        if (item.type === "select") {
          edit.termDict = item.source_list;
          edit.type = "Select";
          signEdit.termDict = item.source_list;
          signEdit.type = "Select";
          if (
            (item.source_id &&
            item.source_id.indexOf("autograph") !== -1) || (item.source_id && item.source_id.indexOf('签名组件') && item.source_id.indexOf('签名组件') !== -1)
          ) {
            signEdit.params = JSON.parse(item.source_id);
            signEdit.type = "autograph";
            if (item.children.length !== 0) {
              // 不为签名图片时，默认赋值name
              signEdit.value = item.name;
              item.children.forEach((item1: any) => {
                if (item1.meta) {
                  // edit.name = item1.meta.id
                  edit.value = edit.value + item1.src;
                  // 为签名图片时更改为对应数据
                  signEdit.value = item1.meta.id;
                } else {
                  edit.value = edit.value + item1.value;
                  signEdit.value = JSON.parse(item.source_id).userId;
                }
              });
            } else {
              edit.value = "";
              signEdit.value = "";
            }
            editorArray.push(signEdit);
          } else {
            if (item.children.length !== 0) {
              item.children.forEach((item1: any) => {
                edit.value = edit.value + item1.value;
              });
            } else {
              edit.value = "";
            }
            editorArray.push(edit);
          }
        } else {
          item.children.forEach((item1: any) => {
            if (item1.type === "box") {
              const editObjedt = {
                name: "",
                value: item1.name,
              };
              item1.children.forEach((item2: any, index2: any) => {
                if (index2 !== 0) {
                  editObjedt.name = editObjedt.name + item2.value;
                }
              });
              if (item1.box_checked === 1 && item.box_multi === 0) {
                edit.value = item1.name;
              } else if (item1.box_checked === 1 && item.box_multi === 1) {
                if (edit.value === "") {
                  edit.value = item1.name;
                } else {
                  edit.value = edit.value + "," + item1.name;
                }
              }
              edit.termDict.push(editObjedt);
            }
          });
          if (item.box_multi === 0) {
            edit.type = "Radio";
          } else {
            edit.type = "Checkbox";
          }
          editorArray.push(edit);
        }
      } else if (
        item.type === "normal" ||
        item.type === "label" ||
        item.type === "date"
      ) {
        // 处理标签/输入框/时间框（无字典）
        if (item.type === "normal" || item.type === "label") {
          if (
            item.children[0] &&
            item.children[0].meta &&
            item.children[0].meta.id === "initializeImage"
          ) {
            edit.type = "image";
            edit.value += item.children[0] ? item.children[0].src : "";
          } else {
            edit.type = "Textarea";
          }
        } else {
          edit.type = "DatePicker";
        }
        if (item.children.length === 0) {
          edit.value = "";
        } else {
          item.children.forEach((item1: any) => {
            edit.value += item1.value;
          });
        }
        editorArray.push(edit);
      } else if (item.type === "image") {
        edit.type = "image";
        edit.value += item.children[0] ? item.children[0].src : "";
        editorArray.push(edit);
      }
    }
  });
  const scheduleTableDTOList: any[] = [];
  const upEditorArray: any[] = [];
  // 选择器，筛选出字典拆分组，并合并
  editorArray.forEach((item) => {
    if (item.type === "Radio" || item.type === "Checkbox") {
      let flag = true;
      upEditorArray.forEach((ite) => {
        if (ite.name === item.name) {
          // 单选
          if (item.type === "Radio" && ite.type === "Radio") {
            if (ite.value === "") {
              ite.value = item.value;
            }
          } else {
            if (item.value !== "") {
              ite.value = ite.value + "," + item.value;
            }
          }
          ite.termDict = ite.termDict.concat(item.termDict);
          flag = false;
        }
      });
      if (flag) {
        upEditorArray.push(item);
      }
    } else {
      upEditorArray.push(item);
    }
  });
  upEditorArray.forEach((item) => {
    const scheduleTableDTOindex: any = {
      oldType: item.oldType,
      name: item.name,
      termColumnName: item.termColumnName,
      termDict:
        item.termDict.length === 0 ? "" : JSON.stringify(item.termDict),
      termType: item.type,
      value: item.value,
    };
    if (item.type === "autograph") {
      scheduleTableDTOindex.params = item.params;
    }
    scheduleTableDTOList.push(scheduleTableDTOindex);
  });
  return scheduleTableDTOList;
}
// 手麻2
export function filterTypr (item: any)  {
  const replacePatInfo: any = [
    "hospitalName",
    "bedNo",
    "patAge",
    "patBlood",
    "patCardNo",
    "patDeptName",
    "patHeight",
    "patName",
    "patWardName",
    "sexName",
    "patWeight",
    "patInHosCode",
    "clinicalRecordCode",
    "patPreAnesList",
    "patInAnesList",
    "patPreDiagnList",
    "patInDiagnList",
    "patPreOperList",
    "patInOperList",
    "patInPositList",
    "trulyOperTime",
    "operRoom",
    "operTimes",
    "patPreAreaNameStr",
    "elseside",
    "operType",
    "inSurgeryVsAreaList",
    "doctorName",
    "doctorOneName",
    "doctorTwoName",
    "anesDocName",
    "anesDocOneName",
    "scrubNurseOneName",
    "circuNurseOneName",
    "idNumber",
    "patientAdmissionDate",
    "patBMI",
    "telephone",
    "addressName",
  ];
  const type = item.termType;
  const termObject: any = {
    field: item.name,
    title: item.termColumnName,
    native: false,
    value: item.value,
    hidden: false,
    display: true,
  };
  if (type === "Textarea") {
    const editorId = item.name.split("#");
    if (!replacePatInfo.includes(editorId[1])) {
      termObject.type = "input";
      termObject._fc_drag_tag = "diisinput";
      termObject.props = {
        type: "text",
      };
    } else {
      termObject.type = "UniqueSpan";
      termObject._fc_drag_tag = "UniqueSpan";
    }
  } else if (type === "DatePicker") {
    termObject.type = "datePicker";
    termObject._fc_drag_tag = "diisDatePicker";
    termObject.$required = false;
    termObject.props = {
      formula: "",
      format: "yyyy-MM-dd HH:mm:ss",
      type: "datetime",
    };
  } else if (type === "Select") {
    const options: any = [];
    if (item.termDict) {
      JSON.parse(item.termDict).forEach((it: any) => {
        options.push({
          value: it.code,
          label: it.text,
        });
      });
    }
    termObject.type = "select";
    termObject._fc_drag_tag = "diisSelect";
    termObject.options = options;
  } else if (type === "Radio") {
    const options: any = [];
    if (item.termDict) {
      JSON.parse(item.termDict).forEach((it: any) => {
        options.push({
          value: it.value,
          label: it.name,
        });
      });
    }
    termObject.type = "radio";
    termObject._fc_drag_tag = "diisradio";
    termObject.options = options;
  } else if (type === "Checkbox") {
    const options: any = [];
    if (item.termDict) {
      JSON.parse(item.termDict).forEach((it: any) => {
        options.push({
          value: it.value,
          label: it.name,
        });
      });
    }
    termObject.type = "checkbox";
    termObject._fc_drag_tag = "diischeckbox";

    termObject.options = options;
  } else if (type === "autograph") {
    termObject.type = "Uniqueautograph";
    termObject._fc_drag_tag = "autograph";
    termObject.props = {};
    Object.keys(item.params).forEach((i) => {
      termObject.props[i] = item.params[i];
    });
  } else if (type === "image") {
    termObject.type = "Uniqueimage";
    termObject._fc_drag_tag = "image";
  }
  return termObject;
}
// 手麻3
export function getRowObj(children: any[]) {
  return {
    type: "fcRow",
    _fc_drag_tag: "row",
    hidden: false,
    display: true,
    children: children,
  }
}

/**
 * 处理特殊字符使用，例如【】单个字符长度为2，在现有逻辑中不能识别会乱码
 */
export const specialCharHandle = {
  specialSymbol: ["☑"],
  specialCharObj: {
    "𧿹": "々",
    "𣇈": "〻",
    "𬌗": "乛"
  },
  // 可将长度为2的生僻字正确分割
  splitString(string: string) {
    const pattern = /([\u4e00-\u9fff])|([\s\S])/gu;
    let result = string.match(pattern);
    if (result) {
      const mergedCharacters: any = [];
      let i = 0;
      const font = new Font({ family: "宋体", height: 16 });
      while (i < result.length) {
        let currentChar = result[i];
        // 如果不是藏文字符，直接添加到 mergedCharacters
        if (!isTibetan(currentChar)) {
          mergedCharacters.push(currentChar);
          i++;
          continue;
        }
        let combinedChar = currentChar;
        let combinedWidth = Renderer.measure(font, combinedChar).width;
        let nextCharIndex = i + 1;
        while (nextCharIndex < result.length) {
          let nextChar = result[nextCharIndex];
          if (!isTibetan(nextChar)) {
            break;
          }
          let testCombinedChar = combinedChar + nextChar;
          let testCombinedWidth = Renderer.measure(font, testCombinedChar).width;
          if (testCombinedWidth <= combinedWidth) {
            combinedChar = testCombinedChar;
            combinedWidth = testCombinedWidth; // 更新合并后的宽度
            nextCharIndex++;
          } else {
            break;
          }
        }
        mergedCharacters.push(combinedChar);
        i = nextCharIndex;
      }
      result = mergedCharacters;
    }
    return result || [];
  },
  replaceChar(value: string) {
    for (const k in this.specialCharObj) {
      if (value.indexOf(k) > -1) {
        const reg = new RegExp(k, "g");
        value = value.replace(reg, this.specialCharObj[k]);
      }
    }
    return value;
  },
  restoreChar(char: string) {
    for (const k in this.specialCharObj) {
      if (char === this.specialCharObj[k]) {
        char = k;
      }
    }
    return char;
  },
  convertImg(char: Character) {
    const canvas = document.createElement("canvas");
    const context = canvas.getContext("2d")!;

    // 计算实际像素比例
    const pixelRatio = window.devicePixelRatio || 1;

    // 设置 Canvas 宽度和高度
    canvas.width = char.width * pixelRatio;
    canvas.height = char.height * pixelRatio;

    // // 调整 Canvas 分辨率
    context.scale(pixelRatio, pixelRatio);

    // 擦除矩形，擦除的意思是把该区域变为透明
    context.clearRect(0, 0, canvas.width, canvas.height);
    context.fillStyle = "#000";
    context.font = char.font.getCss();

    // 绘制文本
    context.fillText(char.value, 0, char.height - 2, char.width);
    const dataUrl = canvas.toDataURL("image/png");// 注意这里背景透明的话，需要使用png
    return dataUrl;
  }
};


// 获取滚动滚动到的最大值
export function getMaxScrollVal(editor: Editor) {
  const y =
          ((editor.page_size.height + editor.config.page_margin_bottom) *
            editor.pages.length +
            editor.config.editor_padding_top) *
          editor.viewScale -
          editor.init_canvas.height / editor.config.devicePixelRatio;
  return y / editor.viewScale;
}