import Font from "./Font";
import { Config, rulerHeight } from "./Config";
import Character from "./Character";
import Widget from "./Widget";
import Editor from "./Editor";
import Shape from "./shape";
import Button from "./Button";
import EditorHelper from "./EditorHelper";
import { Direction, ElementInParagraph } from "./Definition";
import Row from "./Row";
import { DEFINITION } from "./Constant";
import { isInViewport } from "./Helper";
import EditorLocalTest from "../../localtest";
import Page from "./Page";
import Group from "./Groups";

function renderer() {
  let renderer: CanvasRenderingContext2D | null = null;
  let canvasDom: HTMLCanvasElement | null = null;
  let measuredCharMap: any = new Map();
  function measureDecorator(func: Function) {
    return function (font: Font, text: string, editor?: Editor) {
      let multiple = 1;
      // 该逻辑主要处理排版问题，因为正常个别字号计算出来的空格宽度不是汉字的一半，会导致排版时对不齐
      if (text === "\n" || text === "\r") {
        text = "正";
        multiple = 0.5;
      }
      if (text === "\t") {
        text = "正";
        multiple = 2;
      }
      // 调用原始方法
      const result = func(font, text, editor);
      // 在方法执行后的操作
      result.width *= multiple;
      return result;
    };
  }

  /** 服务端调用时的 measure 测量字体宽度逻辑 */
  function _measure_server(font: Font, text: string): number {
    // TODO 有时候会传入文本域的数字类型变量进来
    text = text.toString();

    const codePoint = text.codePointAt(0);
    if (codePoint === undefined) {
      console.warn("测量了空字符串");
      return 0;
    }

    if (codePoint < 10) {
      return 0;
    }

    const { dict, idToSizeMap } =
      Config.configFontJson[font.family] || Config.configFontJson["宋体"];

    // 二分搜索，找到小于等于codePoint的最大值
    let dictIdx = -1;
    {
      let left = 0;
      let right = dict.length - 1;
      while (left <= right) {
        const mid = Math.floor((left + right) / 2);
        if (dict[mid][0] <= codePoint) {
          left = mid + 1;
        } else {
          right = mid - 1;
        }
      }
      dictIdx = right;
    }

    const styleToSizeIdMap = dict[dictIdx][1];

    if (!styleToSizeIdMap) {
      console.warn(`找不到codePoint: ${codePoint}`);
      return 0;
    }

    // 字形  0 常规 1 加粗  2 倾斜 3 加粗倾斜
    let fontStyle = 0;
    if (font.bold) {
      fontStyle |= 1;
    }
    if (font.italic) {
      fontStyle |= 2;
    }

    const id = styleToSizeIdMap[fontStyle];
    if (id == undefined) {
      console.warn(`找不到fontStyle: ${codePoint}, ${fontStyle}`);
      return 0;
    }

    const sizeMap = idToSizeMap[id];

    let height = font.height;
    while (sizeMap[height] == null && height > 0) {
      // 减 0.1, 并保留最多一位小数
      height = parseFloat((height - 0.1).toFixed(1));
    }

    let width = sizeMap[height];

    // 跟老字典一样四舍五入到整数
    width = Math.round(width);

    if (font.script === 1 || font.script === 2) {
      width = width / 2;
    }

    if (isNaN(width)) {
      console.error(`找不到size: ${codePoint}, ${fontStyle}, ${height}`);
      return 0;
    }

    return width;
  }

  function _measure(
    font: Font,
    text: string,
    editor?: Editor
  ): { width: number } {
    //锚点高度小于零时宽度置成0
    if (font.height <= 0) {
      return { width: 0 };
    }

    // 说明是服务器端调用
    if (!renderer) {
      return { width: _measure_server(font, text) };
    }

    const fontCss = font.getCss(editor);
    const findWidth = measuredCharMap.get(fontCss + text);
    if (findWidth) {
      return { width: findWidth };
    } else {
      renderer!.font = fontCss;
      const measure = renderer!.measureText(text);
      const measureWidth = Math.round(measure.width);
      measuredCharMap.set(fontCss + text, measureWidth);
      return { width: measureWidth };
    }
  }
  // function drawMarkEditor (
  //   left: number,
  //   top: number,
  //   width: number,
  //   height: number
  // ): void {
  //   if (!renderer) return;
  //   renderer.strokeStyle = "red";
  //   renderer.strokeRect(left, top, width, height);
  // }
  function _draw_horizontal(
    y: number,
    left: number,
    right: number,
    color: string = "red",
    lineWidth?: number
  ): void {
    if (!renderer) return;
    renderer.strokeStyle = color;
    if (lineWidth) {
      renderer.lineWidth = lineWidth;
    } else {
      renderer.lineWidth = 1;
    }

    renderer.beginPath();
    renderer.moveTo(left, Math.round(y) + 0.5);
    renderer.lineTo(right, Math.round(y) + 0.5);

    renderer.stroke();
  }

  function _draw_vertical(
    x: number,
    top: number,
    bottom: number,
    color: string,
    type: string
  ): void {
    if (!renderer) return;
    renderer.strokeStyle = color;
    renderer.beginPath();
    // 画虚线
    if (type === "dotted") {
      renderer.setLineDash([2, 2]);
    }
    renderer.moveTo(x, top);
    renderer.lineTo(x, bottom);

    renderer.stroke();
  }

  function _draw_rectangle(
    left: number,
    top: number,
    width: number,
    height: number,
    color: string,
    borderRadius?: number
  ): void {
    if (!renderer) return;
    renderer.save();
    renderer.fillStyle = color;
    if (!borderRadius) {
      renderer.fillRect(left, top, width, height);
    } else {
      renderer.beginPath();
      renderer.moveTo(left + borderRadius, top); // 左上角起点
      renderer.arcTo(left + width, top, left + width, top + height, borderRadius); // 右上角
      renderer.arcTo(left + width, top + height, left, top + height, borderRadius); // 右下角
      renderer.arcTo(left, top + height, left, top, borderRadius); // 左下角
      renderer.arcTo(left, top, left + width, top, borderRadius); // 左上角
      renderer.closePath();
      renderer.fill();
    }
    renderer.restore();
  }

  return {
    init(editor: Editor, canvas: HTMLCanvasElement) {
      if (canvas !== canvasDom) {
        renderer = canvas.getContext("2d");
        canvasDom = canvas;
        renderer!.imageSmoothingEnabled = editor.config.imageSmoothingEnabled;
      }
    },

    drawRulerTop(editor: Editor) {
      const page = editor.pages[0];
      const rulerTop = 0;
      const rulerLeft = page.left;
      const rulerWidth = page.width;

      // 绘制卡尺背景
      this.draw_rectangle(rulerLeft, rulerTop, rulerWidth, rulerHeight, "#f8f9fa");

      // 绘制卡尺底部边框
      this.draw_horizontal(rulerTop + rulerHeight - 1, rulerLeft, rulerLeft + rulerWidth, "#d1d5db", 1);

      // 绘制刻度
      this.drawRulerScale(editor, rulerLeft, rulerTop, rulerWidth, rulerHeight);

      // 绘制左右边距指示器
      this.drawMarginIndicators(editor, rulerLeft, rulerTop, rulerWidth, rulerHeight);

      // 绘制拖动辅助虚线
      this.drawRulerDragGuideLine(editor);
    },

    // 绘制卡尺刻度
    drawRulerScale(editor: Editor, rulerLeft: number, rulerTop: number, rulerWidth: number, rulerHeight: number) {
      const pixelsPerCm = 37.8; // 1厘米约等于37.8像素 (96dpi)

      // 使用初始边距作为0点基准，刻度固定不变
      const initialLeftMargin = editor.config.page_padding_left || 0;
      const zeroPointX = rulerLeft + initialLeftMargin;

      // 计算需要绘制的刻度范围
      const leftmostX = rulerLeft;
      const rightmostX = rulerLeft + rulerWidth;

      // 计算左侧负数刻度的起始位置
      const leftCmStart = Math.floor((leftmostX - zeroPointX) / pixelsPerCm);
      const rightCmEnd = Math.ceil((rightmostX - zeroPointX) / pixelsPerCm);

      // 绘制厘米刻度 (以初始可编辑区域左边界为0点，刻度固定不动)
      for (let cm = leftCmStart; cm <= rightCmEnd; cm++) {
        const x = zeroPointX + cm * pixelsPerCm;
        if (x < rulerLeft || x > rulerLeft + rulerWidth) continue;

        // 厘米主刻度线 (长)
        this.draw_vertical(x, rulerTop + rulerHeight - 12, rulerTop + rulerHeight - 1, "#6b7280", "solid");

        // 厘米数字标记 (0点在初始可编辑区域左边界，往左为负数)
        this.drawRulerText(cm.toString(), x - 5, rulerTop + 12, "#374151", 10);

        // 绘制毫米刻度 (每厘米内的小刻度)
        for (let mm = 1; mm < 10; mm++) {
          const mmX = zeroPointX + (cm * 10 + mm) * (pixelsPerCm / 10);
          if (mmX < rulerLeft || mmX > rulerLeft + rulerWidth) continue;

          let tickHeight;
          if (mm === 5) {
            // 0.5厘米刻度 (中等长度)
            tickHeight = 8;
          } else {
            // 毫米刻度 (短)
            tickHeight = 4;
          }

          this.draw_vertical(mmX, rulerTop + rulerHeight - tickHeight, rulerTop + rulerHeight - 1, "#9ca3af", "solid");
        }
      }
    },

    // 绘制边距指示器
    drawMarginIndicators(editor: Editor, rulerLeft: number, rulerTop: number, rulerWidth: number, rulerHeight: number) {
      const page = editor.pages[0];
      const leftMargin = page.padding_left;
      const rightMargin = page.padding_right;

      // 左边距指示器 (蓝色三角形)
      const leftMarginX = rulerLeft + leftMargin;
      const leftColor = (editor.internal.ruler_dragging && editor.internal.ruler_drag_target === 'left')
        ? "#ff6b6b" : "#3b82f6"; // 拖动时显示红色
      this.drawTriangleIndicator(leftMarginX, rulerTop + rulerHeight - 1, "down", leftColor);

      // 右边距指示器 (蓝色三角形)
      const rightMarginX = rulerLeft + rulerWidth - rightMargin;
      const rightColor = (editor.internal.ruler_dragging && editor.internal.ruler_drag_target === 'right')
        ? "#ff6b6b" : "#3b82f6"; // 拖动时显示红色
      this.drawTriangleIndicator(rightMarginX, rulerTop + rulerHeight - 1, "down", rightColor);

      // 绘制文本区域背景高亮
      this.draw_rectangle(leftMarginX, rulerTop, rightMarginX - leftMarginX, rulerHeight, "rgba(59, 130, 246, 0.1)");

      // 绘制边距分隔线 (只在标尺区域内绘制)
      this.draw_vertical(leftMarginX, rulerTop, rulerTop + rulerHeight - 1, "#3b82f6", "solid");
      this.draw_vertical(rightMarginX, rulerTop, rulerTop + rulerHeight - 1, "#3b82f6", "solid");
    },

    // 绘制拖动辅助虚线
    drawRulerDragGuideLine(editor: Editor) {
      // 只在拖动时显示辅助虚线
      if (!editor.internal.ruler_dragging || !editor.internal.ruler_drag_target) {
        return;
      }

      const page = editor.pages[0];
      let guideLineX = 0;

      // 计算当前拖动位置
      if (editor.internal.ruler_drag_target === 'left') {
        guideLineX = page.left + page.padding_left;
      } else if (editor.internal.ruler_drag_target === 'right') {
        guideLineX = page.left + page.width - page.padding_right;
      }

      // 绘制从上到下贯穿整个页面的虚线
      const pageTop = page.top;
      const pageBottom = page.top + page.height;

      // 绘制垂直虚线
      this.draw_line_dash(
        guideLineX, // x坐标（垂直线的x位置）
        pageTop,    // 起始y坐标
        pageBottom, // 结束y坐标
        "#ff6b6b",  // 红色，更醒目
        "vertical", // 垂直方向
        3           // 虚线间隔
      );
    },

    // 绘制三角形指示器
    drawTriangleIndicator(x: number, y: number, direction: "up" | "down" | "left" | "right", color: string) {
      if (!renderer) return;

      renderer.save();
      renderer.fillStyle = color;
      renderer.beginPath();

      const size = 6;
      if (direction === "down") {
        // 向下的三角形
        renderer.moveTo(x, y);
        renderer.lineTo(x - size, y - size);
        renderer.lineTo(x + size, y - size);
      } else if (direction === "up") {
        // 向上的三角形
        renderer.moveTo(x, y);
        renderer.lineTo(x - size, y + size);
        renderer.lineTo(x + size, y + size);
      } else if (direction === "left") {
        // 向左的三角形
        renderer.moveTo(x, y);
        renderer.lineTo(x + size, y - size);
        renderer.lineTo(x + size, y + size);
      } else if (direction === "right") {
        // 向右的三角形
        renderer.moveTo(x, y);
        renderer.lineTo(x - size, y - size);
        renderer.lineTo(x - size, y + size);
      }

      renderer.closePath();
      renderer.fill();
      renderer.restore();
    },

    // 绘制卡尺文字
    drawRulerText(text: string, x: number, y: number, color: string, fontSize: number) {
      if (!renderer) return;

      renderer.save();
      renderer.fillStyle = color;
      renderer.font = `${fontSize}px Arial, sans-serif`;
      renderer.textAlign = "center";
      renderer.textBaseline = "middle";
      renderer.fillText(text, x, y);
      renderer.restore();
    },

    drawRulerLeft(editor: Editor, page: Page) {
      const rulerWidth = 25; // 左侧标尺宽度
      const rulerLeft = 0;
      const rulerTop = page.top;
      const rulerHeight = page.height;

      // 计算可编辑区域的位置
      const editableTop = page.top + page.padding_top; // 可编辑区域上边界
      const editableBottom = editableTop + (page.height - page.padding_top - page.padding_bottom); // 可编辑区域下边界

      // 绘制不可编辑区域背景 (上侧)
      if (editableTop > rulerTop) {
        this.draw_rectangle(rulerLeft, rulerTop, rulerWidth, editableTop - rulerTop, "#e5e7eb");
      }

      // 绘制可编辑区域背景 (中间)
      this.draw_rectangle(rulerLeft, editableTop, rulerWidth, editableBottom - editableTop, "#f8f9fa");

      // 绘制不可编辑区域背景 (下侧)
      if (editableBottom < rulerTop + rulerHeight) {
        this.draw_rectangle(rulerLeft, editableBottom, rulerWidth, (rulerTop + rulerHeight) - editableBottom, "#e5e7eb");
      }

      // 绘制左侧标尺右边框
      this.draw_vertical(rulerLeft + rulerWidth - 1, rulerTop, rulerTop + rulerHeight, "#d1d5db", "solid");

      // 绘制刻度 (从可编辑区域开始计算)
      this.drawRulerLeftScale(editor, page, rulerLeft, editableTop, rulerWidth, rulerHeight);

      // 绘制上下边距指示器
      this.drawRulerLeftMarginIndicators(editor, page, rulerLeft, editableTop, rulerWidth, rulerHeight);
    },

    // 绘制左侧标尺刻度
    drawRulerLeftScale(editor: Editor, page: Page, rulerLeft: number, editableTop: number, rulerWidth: number, rulerHeight: number) {
      const pixelsPerCm = 37.8; // 1厘米约等于37.8像素 (96dpi)
      const editableHeight = page.height - page.padding_top - page.padding_bottom;
      const totalCm = Math.ceil(editableHeight / pixelsPerCm);

      // 绘制厘米刻度 (从可编辑区域开始，1厘米对应可编辑区域顶部)
      for (let cm = 0; cm <= totalCm; cm++) {
        const y = editableTop + cm * pixelsPerCm;
        const pageBottom = page.top + page.height;
        if (y > pageBottom) break;

        // 厘米主刻度线 (长)
        this.draw_horizontal(y, rulerLeft + rulerWidth - 12, rulerLeft + rulerWidth - 1, "#6b7280", 1);

        // 厘米数字标记 (从1开始标记)
        if (cm >= 0) {
          const displayNumber = cm + 1; // 显示1, 2, 3...
          this.drawRulerLeftText(displayNumber.toString(), rulerLeft + 12, y + 5, "#374151", 10);
        }

        // 绘制毫米刻度 (每厘米内的小刻度)
        for (let mm = 1; mm < 10; mm++) {
          const mmY = editableTop + (cm * 10 + mm) * (pixelsPerCm / 10);
          if (mmY > pageBottom) break;

          let tickWidth;
          if (mm === 5) {
            // 0.5厘米刻度 (中等长度)
            tickWidth = 8;
          } else {
            // 毫米刻度 (短)
            tickWidth = 4;
          }

          this.draw_horizontal(mmY, rulerLeft + rulerWidth - tickWidth, rulerLeft + rulerWidth - 1, "#9ca3af", 1);
        }
      }
    },

    // 绘制左侧标尺边距指示器
    drawRulerLeftMarginIndicators(editor: Editor, page: Page, rulerLeft: number, editableTop: number, rulerWidth: number, rulerHeight: number) {
      const editableHeight = page.height - page.padding_top - page.padding_bottom;

      // 上边距指示器 (蓝色三角形) - 可编辑区域上边界
      const topMarginY = editableTop;
      this.drawTriangleIndicator(rulerLeft + rulerWidth - 1, topMarginY, "left", "#3b82f6");

      // 下边距指示器 (蓝色三角形) - 可编辑区域下边界
      const bottomMarginY = editableTop + editableHeight;
      this.drawTriangleIndicator(rulerLeft + rulerWidth - 1, bottomMarginY, "left", "#3b82f6");

      // 在可编辑区域边界绘制分隔线
      this.draw_horizontal(topMarginY, rulerLeft, rulerLeft + rulerWidth - 1, "#3b82f6", 1);
      this.draw_horizontal(bottomMarginY, rulerLeft, rulerLeft + rulerWidth - 1, "#3b82f6", 1);
    },

    // 绘制左侧标尺文字 (垂直方向)
    drawRulerLeftText(text: string, x: number, y: number, color: string, fontSize: number) {
      if (!renderer) return;

      renderer.save();
      renderer.fillStyle = color;
      renderer.font = `${fontSize}px Arial, sans-serif`;
      renderer.textAlign = "center";
      renderer.textBaseline = "middle";

      // 旋转90度绘制垂直文字
      renderer.translate(x, y);
      renderer.rotate(-Math.PI / 2);
      renderer.fillText(text, 0, 0);

      renderer.restore();
    },

    render(editor: Editor) {
      // TODO 考虑是否需要将Renderer置为Editor的一个属性
      this.init(editor, editor.init_canvas); // TODO 为什么还要 init 不是在 attach 的时候 就已经执行完了吗 而且 Renderer 中的画笔是闭包数据 用的都是同一个画笔
      this.get().textBaseline = "bottom"; // canvas 每次宽高改变时都会重置设置的属性，此处要重新赋值
      this.save();
      this.clearRect(0, 0, editor.init_canvas.width, editor.init_canvas.height);
      this.draw_rectangle(
        0,
        0,
        editor.init_canvas.width,
        editor.init_canvas.height,
        editor.config.background_color
      );
      this.restore();
      this.save();

      if (!editor.isMobileTerminal()) {
        if (editor.print_mode) {
          this.get().scale(editor.config.printRatio, editor.config.printRatio);
        } else {
          this.get().scale(
            editor.viewScale * editor.config.devicePixelRatio,
            editor.viewScale * editor.config.devicePixelRatio
          );
        }
        this.translate(editor.internal.view_scale_offset, -editor.scroll_top);
      } else {
        this.translate(
          editor.offsetX * editor.config.devicePixelRatio,
          editor.offsetY * editor.config.devicePixelRatio
        );
        this.get().scale(
          editor.viewScale * editor.config.devicePixelRatio,
          editor.viewScale * editor.config.devicePixelRatio
        );
      }
      // }
      //判断是不是分组打印
      const list = editor.pages.filter((item: Page) => item.groups.length && item.groups.filter((itm: Group) => itm.meta.print).length)
      if (list.length) {
        editor.group_print = true
      }else{
        editor.group_print = false
      }
      editor.pages.forEach((item) => {
        const bottom = item.bottom + editor.config.page_margin_bottom;
        if (isInViewport(editor, item.top, bottom)) {
          // 只有在视口内的页面才需要绘制 每次最多也就绘制两个页面
          item.draw();
          if (editor.isShowRuler) {
            this.drawRulerLeft(editor, item);
          }
          if (!editor.print_mode) {
            if (editor.is_edit_hf_mode) {
              item.drawContentShadow();
            } else {
              item.drawHeaderFooterShadow();
            }
          }
        }
      });

      editor.floatModels.forEach((floaModel) => floaModel.draw());

      // 续打模式绘制遮罩
      if (editor.print_continue) {
        if (
          editor.internal.print_absolute_y !==
          editor.config.editor_padding_top + editor.config.page_padding_top
        ) {
          this.get().globalAlpha = 0.5;
          this.draw_rectangle(
            0,
            0,
            editor.init_canvas.width,
            editor.internal.print_absolute_y,
            Config.print_shadow_color
          );
          this.get().globalAlpha = 1;
        }
      } else if (editor.area_print) {
        if (
          editor.internal.area_location.start_absolute_y &&
          editor.internal.area_location.end_absolute_y
        ) {
          this.get().globalAlpha = 0.2;
          this.draw_rectangle(
            editor.internal.area_location.start_absolute_x,
            editor.internal.area_location.start_absolute_y,
            editor.internal.area_location.end_absolute_x -
            editor.internal.area_location.start_absolute_x,
            editor.internal.area_location.end_absolute_y -
            editor.internal.area_location.start_absolute_y,
            Config.area_print_shadow_color
          );
          this.get().globalAlpha = 1;
        }
      } else {
        editor.renderSelection();
        if (editor.config.fieldBgColor && !editor.print_mode) {
          const allFields = editor.getAllFields();
          for (const field of allFields) {
            const position = field.cell.getOrigin().getLocation();
            if (position === "footer" || position === "header" || field.type === "label" || !field.start_symbol || !field.end_symbol) {
              continue;
            }
            // 不用考虑页眉页脚 只考虑正文 因为云病历 医生不编辑页眉页脚
            const getFieldArea = (fieldId: string) => {
              const field = editor.getFieldById(fieldId);
              if (!field || field.isRemove || !field.end_para || (!field.canBeCopied)) {
                return false;
              }
              const areas = [
                {
                  start_para_path: field.start_para_path,
                  end_para_path: field.end_para_path_outer,
                },
              ];
              return areas;
            };
            const areas = getFieldArea(field.id);
            EditorHelper.drawSelectionAreas(editor, areas, editor.config.fieldBgColor)
          }
        } else {
          EditorHelper.renderContainerArea(editor);
        }
      }
      if (editor.is_comment_mode && !editor.useNewVersionCommentList) {
        const firstPage = editor.pages[0];
        const top = editor.scroll_top ? editor.scroll_top + 1 : firstPage.top;
        const bottom =
          editor.init_canvas.height / editor.config.devicePixelRatio;
        const left = editor.page_left + editor.page_size.width + 1;

        this.draw_rect_shadow(
          left,
          top,
          300,
          (bottom + 20) / editor.viewScale - editor.pages[0].top,
          editor.config.page_color
        );
        editor.commentBox = [];
        editor.internal.createCommentBox();
        const commentBox = editor.commentBox;
        let totalHeight = commentBox.length * 30;
        commentBox.forEach((comment: any) => {
          totalHeight += comment.height;
        });

        if (
          totalHeight >
          (editor.init_canvas.height / editor.config.devicePixelRatio - 30) /
          editor.viewScale
        ) {
          editor.drawCommentScrollBar();
        }
      }

      if (editor.shapes.length) {
        this.translate(editor.page_left, 0);
        for (let i = 0; i < editor.shapes.length; i++) {
          const shape: Shape = editor.shapes[i];
          shape.draw();
        }
      }
      if (
        editor.internal.is_mobile_edit &&
        !editor.internal.VL.is_mobile_selection
      ) {
        this.draw_mobile_selection(
          editor.caret.x,
          editor.caret.y + editor.caret.height,
          "center",
          editor
        );
      }
      if (editor.floatModels.length > 0) {
        editor.floatModels.forEach((it) => it.draw());
      }

      // if (editor.showWartFontEditor) {
      //   const mark = editor.internal.focusMark;
      //   const x = mark.start.x;
      //   const y = mark.params.real_y - mark.height * 1.4 - 6 - 30;
      //   drawMarkEditor(x + editor.page_left - 6 + mark.width, y, mark.width, mark.height + 30);
      //   console.log(editor.internal.focusMark);
      // }
      this.restore();

      if (editor.isShowRuler) {
        // 绘制标尺
        this.drawRulerTop(editor);
      }

      this.save();
      // 绘制滚动条
      if (
        ((editor.page_size.height + editor.config.page_margin_bottom) *
          editor.pages.length +
          editor.config.editor_padding_top) *
        editor.viewScale >=
        editor.init_canvas.height / editor.config.devicePixelRatio &&
        !editor.print_mode
      ) {
        if (!editor.isMobileTerminal()) {
          this.get().scale(
            editor.config.devicePixelRatio,
            editor.config.devicePixelRatio
          );
          editor.drawScrollBar();
        }
      }
      if (
        editor.internal.showCompleteUserName &&
        editor.internal.showCompleteUserName.show
      ) {
        const showCompleteUserName = editor.internal.showCompleteUserName;
        this.drawTextBubble(
          showCompleteUserName.text,
          showCompleteUserName.left + 10,
          showCompleteUserName.top - 20,
          8,
          "white",
          "green",
          1,
          5
        );
      }
      this.drawItalicWatermark(editor);
      this.restore();
      // 光标闪烁只在非只读模式下调用即可,且处于非选区状态
      if (
        !editor.readonly &&
        editor.is_focus &&
        !editor.formReadonly &&
        !editor.is_shape_mode &&
        editor.selection.isCollapsed &&
        !editor.internal.is_in_field_drag
      ) {
        editor.internal.rendering = false;

        // 光标闪烁功能
        clearTimeout(editor.internal.cursorFlashingTimer);

        // 打印模式下新生成的编辑器如果也调该方法，会造成闪烁，猜测可能为使用同一个Render造成
        editor.internal.cursorFlashingTimer = window.setTimeout(() => {
          if (!editor.internal.rendering) {
            editor.caret.show = !editor.caret.show;
            editor.render();
          }
        }, 600);
      }
    },
    drawRoundedRect(
      x: number,
      y: number,
      width: number,
      height: number,
      radius: number
    ) {
      if (!renderer) return;
      renderer.beginPath();
      renderer.moveTo(x + radius, y);
      renderer.lineTo(x + width - radius, y);
      renderer.quadraticCurveTo(x + width, y, x + width, y + radius);
      renderer.lineTo(x + width, y + height - radius);
      renderer.quadraticCurveTo(
        x + width,
        y + height,
        x + width - radius,
        y + height
      );
      renderer.lineTo(x + radius, y + height);
      renderer.quadraticCurveTo(x, y + height, x, y + height - radius);
      renderer.lineTo(x, y + radius);
      renderer.quadraticCurveTo(x, y, x + radius, y);
      renderer.closePath();
    },
    drawTextBubble(
      text: string,
      x: number,
      y: number,
      padding: number,
      backgroundColor: string,
      borderColor: string,
      borderWidth: number,
      borderRadius: number
    ) {
      // 设置字体
      if (!renderer) return;
      const font = "16px Arial";
      renderer.font = font;

      // 测量文本的宽度和高度
      const textMetrics = renderer.measureText(text);
      const textWidth = textMetrics.width;
      const textHeight = parseInt(renderer.font, 10); // 使用字体大小作为文本高度

      // 计算矩形的宽度和高度
      const rectWidth = textWidth + padding * 2;
      const rectHeight = textHeight + padding * 2;

      // 绘制圆角矩形背景
      this.drawRoundedRect(x, y, rectWidth, rectHeight, borderRadius);
      renderer.fillStyle = backgroundColor;
      renderer.fill();

      // 绘制矩形边框
      renderer.lineWidth = borderWidth;
      renderer.strokeStyle = borderColor;
      renderer.stroke();

      // 绘制文本
      renderer.fillStyle = "black"; // 设置文本颜色
      renderer.fillText(text, x + padding, y + padding + textHeight); // 添加偏移量以适应文本位置
    },
    drawItalicWatermark(editor: Editor) {
      const italicWatermark = editor.internal.italicWatermark;
      if (!renderer) return;
      if (!italicWatermark.text) return;
      //如果不包含页面展示，终止，打印时预览把类型改为页面展示
      if (
        !italicWatermark.module.length ||
        (italicWatermark.module.length &&
          !italicWatermark.module.includes("pageWrite"))
      )
        return;
      this.save();
      let font = italicWatermark.font;
      renderer.font = `${Number(font.fontSize) / 0.75}px ${font.fontFamily}`;
      renderer.fillStyle = font.color;
      renderer.globalAlpha = font.opacity;
      const textWidth = renderer.measureText(italicWatermark.text).width;
      const textHeight = Number(font.fontSize);
      let angle; // 旋转角度
      if (italicWatermark.direction === "left") {
        angle = -40;
      } else if (italicWatermark.direction === "right") {
        angle = 40;
      } else {
        angle = 0;
      }
      for (let x = 0; x <= editor.init_canvas.width; x += textWidth + 280) {
        for (
          let y = 150;
          y <= editor.init_canvas.height + editor.scroll_top;
          y += textHeight + 280
        ) {
          renderer.save();
          renderer.translate(x, y);
          renderer.rotate((angle * Math.PI) / 180);
          renderer.fillText(italicWatermark.text, 0, 0);
          renderer.restore();
        }
      }
      this.restore();
    },
    changeSize(x?: number, y?: number) {
      renderer?.scale(x ?? 2, y ?? 2);
    },

    get(): CanvasRenderingContext2D {
      return renderer!;
    },

    getCanvasDom(): HTMLCanvasElement | null {
      return canvasDom;
    },

    clearRect(left: number, top: number, width: number, height: number) {
      if (!renderer) return;
      renderer.clearRect(left, top, width, height);
    },

    draw_horizontal(
      y: number,
      left: number,
      right: number,
      color: string = "red",
      lineWidth: number = 1
    ): void {
      _draw_horizontal(y, left, right, color, lineWidth);
    },

    draw_vertical(
      x: number,
      top: number,
      bottom: number,
      color: string = "yellow",
      type: string = "solid"
    ): void {
      _draw_vertical(x, top, bottom, color, type);
    },

    /**
     * 裁剪单元格
     * @param left 0
     * @param top 0
     * @param width 单元格的宽度
     * @param height 单元格的高度
     * @returns
     */
    clipCell(left: number, top: number, width: number, height: number) {
      if (!renderer) return;
      // 设置内容最大可展示区域，超出部分进行裁剪
      renderer.rect(left, top, width, height);
      renderer.clip();
    },

    clipRect(left: number, top: number, width: number, height: number) {
      // 这里边不能加 save 和 restore 否则就没用了
      if (!renderer) return
      this.clipCell(left, top, width, height)
    },


    /**
     * 绘制单条线
     * @param start 开始坐标位置
     * @param end 结束坐标位置
     * @param color 线的颜色
     * @param alpha 线的透明度
     * @returns
     */
    draw_line(
      start: number[],
      end: number[],
      color: string,
      alpha: number,
      lineWidth = 0.5,
      type: string = "solid"
    ) {
      if (!renderer) return;
      renderer.save();
      renderer.lineWidth = lineWidth;
      renderer.strokeStyle = color;
      renderer.globalAlpha = alpha;
      renderer.beginPath();
      if (type === "dash") {
        renderer.setLineDash([5, 5]);
      }
      renderer.moveTo(Math.round(start[0]) + 0.5, Math.round(start[1]) + 0.5);
      renderer.lineTo(Math.round(end[0]) + 0.5, Math.round(end[1]) + 0.5);
      renderer.stroke();
      renderer.globalAlpha = 1;
      renderer.restore();
    },
    drawShapeLine(
      startXY: any,
      endXY: any,
      color: string,
      lineWidth: number,
      type: string = "solid"
    ) {
      this.draw_line(
        [startXY.x, startXY.y],
        [endXY.x, endXY.y],
        color,
        1,
        lineWidth,
        type
      );
    },
    drawFieldSetWidth(
      text: string,
      y: number,
      left: number,
      right: number,
      color: string = "red",
      fontSize: number = 12
    ) {
      if (!renderer) return;
      renderer.save();
      renderer.fillStyle = color;
      renderer.font = `${fontSize}px Arial`;
      renderer.fillText(text, 0.5 * (left + right) - 15, y + 5);
      renderer.restore();
    },
    /**
     * 画一个弧形，可以是圆，可以是半弧，Math.PI = 180°，根据自己需要计算角度==》转化弧度
     * @param x 横坐标
     * @param y 纵坐标
     * @param radius 半径
     * @param startAngle 开始位置弧度
     * @param endAngle 结束弧度
     * @param color 线条颜色
     * @param anticlockwise 顺时针、逆时针
     */
    drawArc(
      x: number,
      y: number,
      radius: number,
      startAngle: number = 0,
      endAngle: number = Math.PI * 2,
      color: string = "red",
      alpha: number = 1,
      anticlockwise: boolean = false,
      type: string = "hollow"
    ) {
      if (!renderer) return;
      renderer.save();
      renderer.beginPath();
      renderer.lineWidth = 0.5;
      renderer.globalAlpha = alpha;
      renderer.arc(x, y, radius, startAngle, endAngle, anticlockwise);
      renderer.strokeStyle = color;

      if (type === "hollow") {
        renderer.fillStyle = "#ffffff";
      } else {
        renderer.fillStyle = color;
      }
      renderer.fill();
      renderer.stroke();
      renderer.restore();
    },
    drawEllipse(x: number, y: number, width: number, height: number) {
      if (!renderer) return;
      renderer.save();
      renderer.beginPath();
      renderer.lineWidth = 0.5;
      renderer.ellipse(x, y, width, height, 0, 0, 2 * Math.PI);
      renderer.stroke();
      renderer.restore();
    },
    drawDelete(x: number, y: number, width: number, color: string = "black") {
      if (!renderer) return;
      renderer.save();
      renderer.beginPath();
      renderer.strokeStyle = color;
      this.draw_border_rect(x - 1.5, y - width - 2.5, 3, 2, "white");
      renderer.moveTo(x - width - 1, y - width - 0.5);
      renderer.lineTo(x + width + 1, y - width - 0.5);
      renderer.moveTo(x - width, y - width + 1);
      renderer.lineTo(x - width, y + width + 1);
      renderer.moveTo(x + width, y - width + 1);
      renderer.lineTo(x + width, y + width + 1);
      renderer.moveTo(x - width, y + width + 1);
      renderer.lineTo(x + width, y + width + 1);
      renderer.moveTo(x - 1.5, y - width + 1.5);
      renderer.lineTo(x - 1.5, y + width - 1.5);
      renderer.moveTo(x + 1.5, y - width + 1.5);
      renderer.lineTo(x + 1.5, y + width - 1.5);
      renderer.stroke();
      renderer.closePath();
      renderer.restore();
    },
    drawCross(
      x: number,
      y: number,
      width: number,
      height: number,
      color: string = "black"
    ) {
      if (!renderer) return;
      renderer.save();
      renderer.beginPath();
      renderer.lineWidth = 0.5;
      renderer.strokeStyle = color;
      renderer.moveTo(x - width, y - height);
      renderer.lineTo(x + width, y + height);
      renderer.moveTo(x + width, y - height);
      renderer.lineTo(x - width, y + height);
      renderer.stroke();
      renderer.closePath();
      renderer.restore();
    },
    drawShapeSign(x: number, y: number) {
      if (!renderer) return;
      renderer.save();
      this.draw_border_rect(x - 27.5, y - 35.5, 15, 15, "white");
      this.draw_border_rect(x - 7.5, y - 35.5, 15, 15, "white");
      this.draw_border_rect(x + 12.5, y - 35.5, 15, 15, "white");
      this.drawArc(x - 20, y - 27.5, 4, 0, 360, "red");
      this.drawCross(x, y - 27.5, 4, 4, "rgb(50,144,252)");
      this.drawDelete(x + 20, y - 27.5, 3.5, "black");

      renderer.restore();
    },
    draw_line_dash(
      y: number,
      left: number,
      right: number,
      color: string = "red",
      type: string = "horizontal",
      width: number = 5
    ) {
      if (!renderer) return;
      renderer.lineWidth = 1;
      renderer.strokeStyle = color;
      renderer.beginPath();
      renderer.setLineDash([width, width]);
      if (type === "horizontal") {
        renderer.moveTo(left, y);
        renderer.lineTo(right, y);
      } else {
        renderer.moveTo(y, left);
        renderer.lineTo(y, right);
      }
      renderer.stroke();
    },
    draw_shadow(
      left: number,
      top: number,
      width: number,
      height: number,
      color: string = "#ffffff"
    ) {
      if (!renderer) return;
      if (color === "#ffffff") {
        renderer.globalAlpha = 0.4;
      } else {
        renderer.globalAlpha = 0;
      }

      _draw_rectangle(left, top, width, height, color);
      renderer.globalAlpha = 1;
    },
    draw_rectangle(
      left: number,
      top: number,
      width: number,
      height: number,
      color: string,
      borderRadius?: number
    ): void {
      _draw_rectangle(left, top, width, height, color, borderRadius);
    },
    draw_stroke_rect(
      left: number,
      top: number,
      width: number,
      height: number,
      color: string = "rgb(150,150,150)",
      line: string = "line",
      lineWidth: number = 0.5,
      borderRadius?: number
    ): void {
      if (!renderer) return;
      renderer.save();
      renderer.lineWidth = lineWidth;
      renderer.strokeStyle = color;
      if (line === "dotted") {
        renderer.setLineDash([4, 5]);
      }
      if (!borderRadius) {
        renderer.strokeRect(left, top, width, height);
      } else {
        renderer.beginPath();
        renderer.moveTo(left + borderRadius, top); // 左上角起点
        renderer.arcTo(left + width, top, left + width, top + height, borderRadius); // 右上角
        renderer.arcTo(left + width, top + height, left, top + height, borderRadius); // 右下角
        renderer.arcTo(left, top + height, left, top, borderRadius); // 左下角
        renderer.arcTo(left, top, left + width, top, borderRadius); // 左上角
        renderer.closePath();
        renderer.stroke();
      }
      renderer.restore();
    },

    draw_border_rect(
      left: number,
      top: number,
      width: number,
      height: number,
      color: string
    ): void {
      if (!renderer) return;

      _draw_rectangle(left, top, width, height, color);

      renderer.strokeStyle = "rgb(150,150,150)";

      renderer.strokeRect(left, top, width, height);
    },
    draw_table_corner(
      left: number,
      top: number,
      location: number,
      color: string = "blue",
      long: number = 8
    ) {
      if (!renderer) return;
      renderer.lineWidth = 1;
      renderer.strokeStyle = color;
      const spacing = 4;
      switch (location) {
        case 1:
          renderer.beginPath();
          renderer.moveTo(left + long, top - spacing);
          renderer.lineTo(left - spacing, top - spacing);
          renderer.lineTo(left - spacing, top + long);
          renderer.stroke();
          break;
        case 2:
          renderer.beginPath();
          renderer.moveTo(left - long, top - spacing);
          renderer.lineTo(left + spacing, top - spacing);
          renderer.lineTo(left + spacing, top + long);
          renderer.stroke();
          break;
        case 3:
          renderer.beginPath();
          renderer.moveTo(left + long, top + spacing);
          renderer.lineTo(left - spacing, top + spacing);
          renderer.lineTo(left - spacing, top - long);
          renderer.stroke();
          break;
        case 4:
          renderer.beginPath();
          renderer.moveTo(left - long, top + spacing);
          renderer.lineTo(left + spacing, top + spacing);
          renderer.lineTo(left + spacing, top - long);
          renderer.stroke();
          break;
      }
    },
    draw_rect_shadow(
      left: number,
      top: number,
      width: number,
      height: number,
      color: string
    ): void {
      if (!renderer) return;
      renderer.shadowBlur = 40;
      renderer.shadowColor = Config.page_shadow_color;
      _draw_rectangle(left, top, width, height, color);
      renderer.shadowBlur = 0;
    },

    draw_character(
      character: Character,
      baseline: number,
      row_height: number,
      line_padding: number,
      editor: Editor,
      isEndChar: boolean = false
    ) {
      if (!renderer) return;
      const font = character.font;

      // 将白色为#FFF的值转为透明背景色，之后新设置的白色背景需要设置成#FFFFFF,目的是为了解决之前修改过字体样式后会自动设置白色背景色问题
      if (font.bgColor === "#FFF") {
        font.bgColor = null;
      }
      const group_id = character.comment_id?.split("$$")[1] + "";
      const cusCommentGroupId = character.cusCommentId?.split("$$")[1] + "";
      const is_draw_comment_bgc =
        editor.is_comment_mode &&
        character.comment_id &&
        editor.document_meta?.commentsIDSet?.[group_id]?.find(
          (obj: any) => obj.id === character.comment_id
        );
      const isDrawCusCommentBgc =
        editor.isCusCommentMode &&
        character.cusCommentId &&
        editor.document_meta?.cusCommentsIDSet?.[cusCommentGroupId]?.find(
          (obj: any) => obj.id === character.cusCommentId
        );
      if (
        font.temp_word_bgColor ||
        font.bgColor ||
        is_draw_comment_bgc ||
        isDrawCusCommentBgc
      ) {
        _draw_rectangle(
          character.left,
          0,
          isEndChar ? character.width : character.draw_width,
          row_height,
          font.temp_word_bgColor ||
          character.temp_bgColor ||
          (is_draw_comment_bgc
            ? editor.config.comment.wordUnselectedBgColor
            : font.bgColor) ||
          (isDrawCusCommentBgc ? "rgba(255, 255, 255, 0)" : font.bgColor) ||
          ""
        );
      }
      if (font.highLight) {
        _draw_rectangle(
          character.left,
          0,
          character.draw_width,
          baseline,
          font.highLight
        );
      }
      renderer.fillStyle =
        font.temp_valid_color || font.temp_word_color || font.color;
      renderer.font = font.getCss(editor, true);
      renderer.globalAlpha = character.transparent;
      let drawValue;
      if (character.isShowAsterisk(editor)) {
        drawValue = "*";
        renderer.fillStyle = "#000";
      } else {
        drawValue = character.value;
      }
      renderer.fillText(
        drawValue,
        character.left,
        baseline - line_padding,
        character.draw_width
      );
      let color = font.color;

      renderer.globalAlpha = 1;
      if (font.underline || font.dblUnderLine) {
        let y = baseline - line_padding;
        if (editor.view_mode === "person") {
          color = "rgb(24, 144, 255)";
          y = baseline;
        }
        _draw_horizontal(
          y,
          character.left,
          character.left + character.draw_width,
          color
        );
        if (font.dblUnderLine) {
          let dblY = baseline - line_padding / 2 + 1;
          if (editor.view_mode === "person") {
            dblY = baseline - 2;
          }
          _draw_horizontal(
            dblY,
            character.left,
            character.left + character.draw_width,
            color
          );
        }
      }

      if (font.strikethrough) {
        if (editor.view_mode === "person") {
          color = "red";
        }
        _draw_horizontal(
          baseline - line_padding - character.height / 2,
          character.left,
          character.left + character.draw_width,
          color
        );
      }
    },

    draw_field_bgColor(
      character: ElementInParagraph,
      nextChar: ElementInParagraph,
      row: Row,
      color: string = "#f0f8ff",
    ) {
      if (!renderer) return;
      if (!character.value) return;
      if (row.parent.getFieldById(character.field_id!)?.editable) {
        let realWidth = character.width + 1;
        if (nextChar && nextChar.left > character.left) {
          realWidth = nextChar.left - character.left;
        }
        renderer.save();
        _draw_rectangle(
          character.left,
          character.top + 2,
          realWidth + 1,
          row.height - 2,
          color
        );
        renderer.restore();
      }
    },
    draw_field_symbol(character: Character, row: Row, editor: Editor) {
      if (!renderer) return;
      if (!character.value) return;
      if (character.height <= 0) {
        return;
      }
      renderer.save();
      const font = character.font;
      renderer.lineWidth = 1;
      renderer.strokeStyle = editor.config.field_symbol_color;
      let x = Math.round(character.left) + DEFINITION;
      const height = font.height;

      // Math.round 和 + 0.5 是经过测试的 否则 边框横向的部分会显示有点重复,有点粗,跟小点似的,还不一致,现在好像也不一致 但是好多了
      const y =
        Math.round(row.height - row.padding_vertical - height) + DEFINITION; // row.height - row.padding_vertical 的逻辑跟 draw_character 一致 但是这里因为绘制的线条,所以 row.height - row.padding_vertical 得到的基线 还要再剪切边框的高度
      const corLen = 3.5;
      renderer.beginPath();
      // 上角
      // if (character.field_position === "start") {
      //   renderer.moveTo(x, y);
      //   renderer.lineTo(x, y + height);
      //   renderer.moveTo(x, y);
      //   renderer.lineTo(x + corLen, y);
      // } else {
      //   renderer.moveTo(x, y);
      //   renderer.lineTo(x, y + height);
      //   renderer.moveTo(x, y);
      //   renderer.lineTo(x - corLen, y);
      // }
      // 下角
      // if (character.field_position === "start") {
      //   renderer.moveTo(x, y + font.height - height + 2);
      //   renderer.lineTo(x, y + font.height + 3);
      //   renderer.moveTo(x, y + font.height + 3);
      //   renderer.lineTo(x + corLen, y + font.height + 3);
      // } else {
      //   renderer.moveTo(x, y + font.height - height + 2);
      //   renderer.lineTo(x, y + font.height + 3);
      //   renderer.moveTo(x, y + font.height + 3);
      //   renderer.lineTo(x - corLen, y + font.height + 3);
      // }
      // 全框
      if (character.field_position === "start") {
        x -= 1;
        renderer.moveTo(x, y);
        renderer.lineTo(x, y + height);
        renderer.moveTo(x, y);
        renderer.lineTo(x + corLen, y);
        renderer.moveTo(x, y + height);
        renderer.lineTo(x + corLen, y + height);
      } else {
        renderer.moveTo(x, y);
        renderer.lineTo(x, y + height);
        renderer.moveTo(x, y);
        renderer.lineTo(x - corLen, y);
        renderer.moveTo(x, y + height);
        renderer.lineTo(x - corLen, y + height);
      }
      renderer.closePath();
      renderer.stroke();
      renderer.restore();
    },

    draw_image(
      image: any,
      x: number,
      baseline: number,
      width: number,
      height: number
    ) {
      if (!renderer || !image) return;

      const margin = Config.img_margin; // 图片的margin值

      if (image.isLoaded) {
        renderer.drawImage(
          image.data,
          x + margin,
          baseline - height + margin,
          width - margin * 2,
          height - margin * 2
        );
      }
    },
    draw_box(x: number, y: number, color: string = "rgb(134,140,245)") {
      if (!renderer) return;
      const top_start = [x, y - 2];
      const top_break = [x - 2, y + 2];
      const top_end = [x + 2, y + 2];
      this.drawTriangle(top_start, top_break, top_end, color);
    },
    draw_button(button: Button, baseline: number, line_padding: number) {
      if (!renderer) return;
      renderer.save();
      renderer.save();
      const gradient = renderer.createLinearGradient(
        button.left,
        baseline - button.height - line_padding,
        button.left,
        baseline - line_padding
      );
      gradient.addColorStop(0, "rgb(250,250,250)");
      gradient.addColorStop(0.5, "rgb(220,220,220)");
      gradient.addColorStop(1, button.color);
      renderer.fillStyle = gradient;
      renderer.beginPath();
      renderer.roundRect(
        button.left + 2,
        baseline - line_padding - button.height,
        button.width - 2,
        button.height,
        5
      );
      renderer.stroke();
      this.drawRoundRect(
        button.left + 2,
        baseline - line_padding - button.height,
        button.width - 2,
        button.height,
        5,
        "gradient"
      );
      if (button.pointer_in) {
        this.drawRoundRect(
          button.left + 2,
          baseline - line_padding - button.height,
          button.width - 2,
          button.height,
          5,
          "rgba(255,255,255,0.4)"
        );
      }
      renderer.restore();
      renderer.fillStyle = "#000";
      renderer.font = `${(button.height / 4) * 3}px "宋体"`;
      const { width } = renderer.measureText(button.value);
      let realWidth = button.left + (button.width - width) / 2;
      if (button.value && button.value[button.value.length - 1] === ".") {
        realWidth += 2;
      }
      renderer.fillText(
        button.value,
        realWidth,
        baseline - line_padding - button.height / 8
      );
      renderer.globalAlpha = 1;
      renderer.restore();
    },
    draw_field_editor(
      x: number,
      y: number,
      width: number,
      height: number,
      color = "rgb(50,144,252)"
    ) {
      if (!renderer) return;
      renderer.strokeStyle = color;
      renderer.fillStyle = color;
      renderer.strokeRect(x + 0.5, y + 0.5, width, height);
      renderer.fillRect(x + 2, y + 2, width - 3, height - 3);
    },
    drawRoundRect(
      x: number,
      y: number,
      width: number,
      height: number,
      radius: number,
      fillColor: string
    ) {
      if (!renderer) return;
      renderer.beginPath();
      renderer.moveTo(x + radius, y);
      renderer.lineTo(x + width - radius, y);
      renderer.arcTo(x + width, y, x + width, y + radius, radius);
      renderer.lineTo(x + width, y + height - radius);
      renderer.arcTo(
        x + width,
        y + height,
        x + width - radius,
        y + height,
        radius
      );
      renderer.lineTo(x + radius, y + height);
      renderer.arcTo(x, y + height, x, y + height - radius, radius);
      renderer.lineTo(x, y + radius);
      renderer.arcTo(x, y, x + radius, y, radius);
      renderer.fillStyle = fillColor;
      renderer.fill();
    },
    draw_mobile_selection(
      x: number,
      y: number,
      direction: string,
      editor: Editor,
      color: string = "rgb(65, 116, 234)"
    ) {
      if (!renderer) return;
      renderer.save();
      renderer.beginPath();
      const radius = editor.internal.mobile_radius;
      renderer.strokeStyle = color;
      renderer.fillStyle = color;
      let arcX = x - radius;
      let rectX = x - radius + 0.5;
      if (direction === "right") {
        arcX = x + radius;
        rectX = x - 0.5;
      } else if (direction === "center") {
        arcX = x;
        rectX = x - 0.5;
      }
      if (direction === "center") {
        renderer.save();
        renderer.translate(arcX + 0.5, y);
        renderer.rotate((45 * Math.PI) / 180);
        renderer.fillRect(0, 0, radius, radius);
        renderer.arc(radius + 0.5, radius + 0.5, radius, 0, Math.PI * 2, false);
        renderer.restore();
      } else {
        renderer.fillRect(rectX, y - 0.5, radius, radius);
        renderer.arc(arcX, y + radius, radius, 0, Math.PI * 2, false);
      }
      renderer.fill();
      renderer.stroke();
      renderer.restore();
    },
    draw_horizontal_line(
      x: number,
      y: number,
      width: number,
      height: number,
      color: string,
      lineHeight: number
    ) {
      if (!renderer) return;
      _draw_horizontal(y + height / 2, x, x + width, color, lineHeight);
      renderer.lineWidth = 1;
    },
    drawCheckBox(
      widget: Widget,
      left: number,
      top: number,
      height: number,
      border: string,
      color_text: string,
      editor: Editor
    ) {
      if (!renderer) return;

      const bgColor = editor.config.page_color;
      _draw_rectangle(left, top, height, height, bgColor);
      renderer.strokeStyle = color_text;
      const margin = Config.widget_margin; // 图片的margin值

      renderer.lineWidth = 1;

      if (widget.selected) {
        if (border !== "none") {
          // border 值不为 none 的时候才需要绘制边框
          renderer.save();
          // renderer.setLineDash([3.4, 3.4]);
          if (border === "dashed") {
            renderer.setLineDash([3.4, 3.4]);
          } else if (border === "dotted") {
            renderer.setLineDash([1.3, 1.3]);
          }
          renderer.strokeStyle = renderer.strokeStyle =
            editor.config.widgetSelectColor ?? "#000000";
          renderer.strokeRect(
            left + margin,
            top + margin,
            height - margin * 2,
            height - margin * 2
          );
          renderer.restore();
        }
        // 开始一个新的绘制路径
        renderer.beginPath();
        renderer.moveTo(left + height / 4, top + height / 2);
        renderer.lineTo(left + height * (9 / 20), top + height * (3 / 4));
        renderer.lineTo(left + height * (3 / 4), top + height / 4);
        renderer.strokeStyle = editor.config.widgetSelectColor ?? "#000000";

        // 沿着坐标点顺序的路径绘制直线
        renderer.stroke();
        // //关闭当前的绘制路径
        renderer.closePath();
      } else {
        if (border !== "none") {
          // border 值不为 none 的时候才需要绘制边框
          renderer.save();
          // renderer.setLineDash([3.4, 3.4]);
          if (border === "dashed") {
            renderer.setLineDash([3.4, 3.4]);
          } else if (border === "dotted") {
            renderer.setLineDash([1.3, 1.3]);
          }
          // renderer.strokeStyle = renderer.strokeStyle = editor.config.widgetSelectColor ?? "#000000";
          renderer.strokeRect(
            left + margin,
            top + margin,
            height - margin * 2,
            height - margin * 2
          );
          renderer.restore();
        }
      }
    },
    drawRadio(
      widget: Widget,
      left: number,
      top: number,
      height: number,
      color_text: string
    ) {
      if (!renderer) return;
      const margin = Config.widget_margin; // 单选、复选框的margin值
      // 设置描边颜色
      renderer.strokeStyle = color_text;
      // 绘制圆形
      renderer.lineWidth = 1;
      renderer.beginPath();
      renderer.arc(
        left + height / 2,
        top + height / 2,
        (height - margin * 2) / 2,
        0,
        2 * Math.PI
      );
      renderer.stroke();
      if (widget.selected) {
        renderer.fillStyle = color_text;
        renderer.beginPath();
        renderer.arc(
          left + height / 2,
          top + height / 2,
          (height - margin * 2) / 4,
          0,
          2 * Math.PI,
          true
        );
        renderer.closePath();
        renderer.fill();
      }
    },

    drawCaliper(widget: Widget, left: number, top: number) {
      if (!renderer) return;
      renderer.save();
      renderer.beginPath();
      // 先绘制开始部分
      renderer.moveTo(left + 5.5, top);
      renderer.lineTo(left + 5.5, top + widget.height - 12);
      renderer.moveTo(left + 5.5, top + widget.height / 2 - 6);
      renderer.lineTo(left - 5.5 + widget.width, top + widget.height / 2 - 6);
      renderer.fillStyle = "#000";
      renderer.font = "bold 12px fangSong";
      renderer.fillText("0", left + 2.5, top + widget.height);
      if (widget.params && widget.params.num) {
        // 绘制中间循环部分
        for (let i = 1; i < widget.params.num; i++) {
          renderer.moveTo(
            left + 5.5 + widget.params.spacing * i,
            top + widget.height * 0.1
          );
          renderer.lineTo(
            left + 5.5 + widget.params.spacing * i,
            top + 0.9 * widget.height - 12
          );
          const width = renderer.measureText(i + "").width;
          renderer.fillText(
            i + "",
            left + 2.5 + widget.params.spacing * i - width / 4,
            top + widget.height
          );
        }
      }

      // 绘制结尾部分
      renderer.moveTo(left - 5.5 + widget.width, top);
      renderer.lineTo(left - 5.5 + widget.width, top + widget.height - 12);
      const width = renderer.measureText(widget.params.num + "").width;
      renderer.fillText(
        widget.params.num + "",
        left - 8.5 + widget.width - width / 4,
        top + widget.height
      );
      // 线条的颜色
      renderer.lineWidth = 0.5;
      renderer.strokeStyle = "#000";
      // 沿着坐标点顺序的路径绘制直线
      renderer.stroke();
      // //关闭当前的绘制路径
      renderer.closePath();
      if (widget.selectNum !== undefined) {
        renderer.save();
        renderer.beginPath();
        renderer.arc(
          left + 5.5 + widget.selectNum * widget.params.spacing,
          top + widget.height / 2 - 6,
          widget.params.spacing * 0.3,
          0,
          Math.PI * 2,
          true
        );
        renderer.stroke();
        renderer.restore();
      }
      renderer.restore();
    },

    drawTriangle(
      start_path: any,
      break_path: any,
      end_path: any,
      color: string = "#000"
    ) {
      if (!renderer) return;
      renderer.fillStyle = color;
      renderer.beginPath();
      renderer.moveTo(start_path[0], start_path[1]);
      renderer.lineTo(break_path[0], break_path[1]);
      renderer.lineTo(end_path[0], end_path[1]);
      renderer.closePath();
      renderer.fill();
    },
    drawCornerLine(start_path: any, break_path: any, end_path: any) {
      if (!renderer) return;
      // 开始一个新的绘制路径
      renderer.beginPath();
      // 定义直线的起点坐标为(10,10)
      renderer.moveTo(start_path.x, start_path.y);
      // 定义直线的终点坐标为(50,10)
      renderer.lineTo(break_path.x, break_path.y);
      renderer.lineTo(end_path.x, end_path.y);
      // 线条的颜色
      renderer.lineWidth = 1;
      renderer.strokeStyle = "#000";
      // 沿着坐标点顺序的路径绘制直线
      renderer.stroke();
      // //关闭当前的绘制路径
      renderer.closePath();
    },
    // 画图标
    drawListSymbol(
      x: number,
      baseline: number,
      line_height: number,
      level: number
    ) {
      if (!renderer) return;
      const y = baseline;
      const radius = line_height / 2;
      renderer.beginPath();
      renderer.fillStyle = "#000";
      switch (level % 2) {
        case 1:
          renderer.arc(x, y, radius, 0, Math.PI * 2, true);
          break;
        case 0:
          renderer.fillRect(
            x - line_height / 2,
            y - line_height / 2,
            line_height,
            line_height
          );
          break;
      }
      renderer.fill();
    },

    measure: measureDecorator((font: Font, text: string, editor?: Editor) => {
      return _measure(font, text, editor);
    }),

    identity() {
      if (!renderer) return;

      renderer.resetTransform();
    },

    translate(x: number, y: number) {
      if (!renderer) return;
      renderer.translate(Math.round(x), Math.round(y));
    },

    translate_to(x: number, y: number) {
      if (!renderer) return;

      renderer.setTransform(1, 0, 0, 1, x, y);
    },
    draw_text(
      font: Font,
      text: string,
      x: number,
      baseline: number,
      editor?: Editor
    ): void {
      if (!renderer) return;
      renderer.save();
      const { width } = _measure(font, text, editor);

      if (font.bgColor) {
        _draw_rectangle(
          x,
          baseline - font.height,
          x + width,
          baseline,
          font.bgColor
        );
      }

      renderer.fillStyle = font.color;

      renderer.font = font.getCss(editor);

      renderer.fillText(text, x, baseline);

      if (font.underline) {
        _draw_horizontal(baseline, x, x + width, font.color);
      }

      if (font.strikethrough) {
        _draw_horizontal(
          baseline - font.height / 2 - 2,
          x,
          x + width,
          font.color
        );
      }
      renderer.restore();
    },
    drawWrapText(
      text: string,
      maxWidth: number,
      lineHeight: number,
      font: Font,
      x: number,
      y: number,
      editor: Editor
    ) {
      if (!renderer) return;
      renderer.save();
      const words = text.split("");
      renderer.fillStyle = font.color;
      renderer.font = font.getCss(editor);
      let line = "";
      for (let i = 0; i < words.length; i++) {
        const testLine = line + words[i];
        const testWidth = renderer.measureText(testLine).width;
        if (testWidth > maxWidth && i > 0) {
          renderer.fillText(line, x, y);
          if (font.strikethrough) {
            const { width } = _measure(font, line, editor);
            _draw_horizontal(y - font.height / 2 - 2, x, x + width, "red");
          }
          line = words[i];
          y += lineHeight;
        } else {
          line = testLine;
        }
      }
      renderer.fillText(line, x, y);
      if (font.strikethrough) {
        const { width } = _measure(font, line, editor);
        _draw_horizontal(y - font.height / 2 - 2, x, x + width, "red");
      }
      renderer.restore();
    },
    drawArrow(
      bottomCenterPoint: number[],
      lineWidth: number,
      direction: string = "left",
      color: string = "rgb(150,150,150)"
    ) {
      const startX = bottomCenterPoint[0];
      const startY = bottomCenterPoint[1];

      if (!renderer) return;
      switch (direction) {
        case "right":
          renderer.beginPath();
          renderer.moveTo(startX, startY);
          renderer.lineTo(startX, startY + lineWidth * 0.5);
          renderer.lineTo(startX + 3 * lineWidth, startY + lineWidth * 0.5);
          renderer.lineTo(startX + 3 * lineWidth, startY + lineWidth * 1.5);
          renderer.lineTo(startX + 5 * lineWidth, startY);
          renderer.lineTo(startX + 3 * lineWidth, startY - lineWidth * 1.5);
          renderer.lineTo(startX + 3 * lineWidth, startY - lineWidth * 0.5);
          renderer.lineTo(startX, startY - lineWidth / 2);
          renderer.lineTo(startX, startY);
          renderer.closePath();
          renderer.fillStyle = color;
          renderer.fill();
          break;
        case "left":
          renderer.beginPath();
          renderer.moveTo(startX, startY);
          renderer.lineTo(startX, startY + lineWidth * 0.5);
          renderer.lineTo(startX - 3 * lineWidth, startY + lineWidth * 0.5);
          renderer.lineTo(startX - 3 * lineWidth, startY + lineWidth * 1.5);
          renderer.lineTo(startX - 5 * lineWidth, startY);
          renderer.lineTo(startX - 3 * lineWidth, startY - lineWidth * 1.5);
          renderer.lineTo(startX - 3 * lineWidth, startY - lineWidth * 0.5);
          renderer.lineTo(startX, startY - lineWidth / 2);
          renderer.lineTo(startX, startY);
          renderer.closePath();
          renderer.fillStyle = color;
          renderer.fill();
          break;
        case "bottom":
          renderer.beginPath();
          renderer.moveTo(startX, startY);
          renderer.lineTo(startX + lineWidth * 0.5, startY);
          renderer.lineTo(startX + lineWidth * 0.5, startY + lineWidth * 3);
          renderer.lineTo(startX + lineWidth * 1.5, startY + lineWidth * 3);
          renderer.lineTo(startX, startY + lineWidth * 5);
          renderer.lineTo(startX - 1.5 * lineWidth, startY + lineWidth * 3);
          renderer.lineTo(startX - 0.5 * lineWidth, startY + lineWidth * 3);
          renderer.lineTo(startX - lineWidth * 0.5, startY);
          renderer.lineTo(startX, startY);
          renderer.closePath();
          renderer.fillStyle = color;
          renderer.fill();
          break;
        case "top":
          renderer.beginPath();
          renderer.moveTo(startX, startY);
          renderer.lineTo(startX + lineWidth * 0.5, startY);
          renderer.lineTo(startX + lineWidth * 0.5, startY - lineWidth * 3);
          renderer.lineTo(startX + lineWidth * 1.5, startY - lineWidth * 3);
          renderer.lineTo(startX, startY - lineWidth * 5);
          renderer.lineTo(startX - 1.5 * lineWidth, startY - lineWidth * 3);
          renderer.lineTo(startX - 0.5 * lineWidth, startY - lineWidth * 3);
          renderer.lineTo(startX - lineWidth * 0.5, startY);
          renderer.lineTo(startX, startY);
          renderer.closePath();
          renderer.fillStyle = color;
          renderer.fill();
          break;
      }
    },

    // 绘制人形箭头(V形箭头)
    drawChevron(
      parameter: {
          position: number[], 
          direction?: Direction,
          width?: number, 
          height?: number, 
          lineWidth?: number, 
          color?: string
        }
    ) {
      const { position, width = 6, height = 6, lineWidth = 1.5, color = "#888", direction = Direction.left } = parameter;
      if (!renderer) return;
      const [x, y] = position;
      renderer.save();
      renderer.strokeStyle = color;
      renderer.lineWidth = lineWidth;
      renderer.lineJoin = "round";
      renderer.beginPath();
      if (direction === Direction.up) {
        renderer.moveTo(x, y + height);
        renderer.lineTo(x + width / 2, y);
        renderer.lineTo(x + width, y + height);
      } else if (direction === Direction.right) {
        renderer.moveTo(x, y);
        renderer.lineTo(x + width, y + height / 2);
        renderer.lineTo(x, y + height);
      } else if (direction === Direction.down) {
        renderer.moveTo(x, y);
        renderer.lineTo(x + width / 2, y + height);
        renderer.lineTo(x + width, y);
      } else if (direction === Direction.left) {
        renderer.moveTo(x + width, y);
        renderer.lineTo(x, y + height / 2);
        renderer.lineTo(x + width, y + height);
      }
      renderer.stroke();
      renderer.closePath();
      renderer.restore();
    },
  

    save() {
      if (!renderer) return;

      renderer.save();
    },

    restore() {
      if (!renderer) return;

      renderer.restore();
    },

    drawSwitch(parameter: {
      start: number[], 
      width?: number, 
      height?: number, 
      lineWidth?: number, 
      color?: string
    }) {
      if (!renderer) return;
      let { start, width = 20, height = 15, lineWidth = 0.8, color = "#000" } = parameter;
      const [x, y] = start;
      renderer.save();
      renderer.strokeStyle = color || "#000";
      renderer.lineWidth = lineWidth ?? 1;
      renderer.lineJoin = "round";
      renderer.beginPath();
      const LEN = 3; // 等边三角形 没想好 应该怎么个计算比例 好看 就先这么着吧 先写死5
      height = Math.max(LEN * 2 + 5, height);
      renderer.moveTo(x + LEN, y);
      renderer.lineTo(x, y + LEN);
      renderer.lineTo(x + width, y + LEN);
      renderer.stroke();
      renderer.closePath();
      
      renderer.beginPath();
      renderer.moveTo(x, y + (height - LEN));
      renderer.lineTo(x + width, y + (height - LEN));
      renderer.lineTo(x + width - LEN, y + (height - LEN) + LEN);

      renderer.stroke();
      renderer.closePath();      
      renderer.restore();

    }

    
    // setCursorStyle (style: string) {}
  };
}

export default renderer();
