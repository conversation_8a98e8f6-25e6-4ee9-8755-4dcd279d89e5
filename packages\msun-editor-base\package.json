{"name": "msun-editor", "version": "10.24.5", "description": "EMR editor", "main": "./dist/initEditor.umd.cjs", "module": "./dist/initEditor.js", "exports": {".": "./src/index.ts"}, "publishConfig": {"exports": {".": "./dist/initEditor.js"}}, "files": ["dist", "lib"], "projectType": "library", "scripts": {"start": "vite", "build": "vite build", "debug": "tsc && vite build --mode=debug", "preview": "vite preview", "pack": "npm run build && npm pack --force"}, "keywords": [], "author": "张超，薛再强，李振平，席忠强，张鹏飞，唐常锐", "license": "ISC", "dependencies": {"msun-lib-editor-transform": "workspace:*"}, "type": "module", "devDependencies": {"@babel/core": "^7.14.3", "@babel/preset-env": "7.14.4", "@typescript-eslint/eslint-plugin": "^5.56.0", "@typescript-eslint/parser": "^5.0.0", "eslint": "^7.28.0", "eslint-config-prettier": "^8.3.0", "eslint-config-standard": "^16.0.3", "eslint-plugin-import": "^2.23.4", "eslint-plugin-node": "^11.1.0", "eslint-plugin-prettier": "^3.4.0", "eslint-plugin-promise": "^5.1.0", "prettier": "^2.3.1", "rimraf": "^3.0.2", "tslib": "^2.2.0", "typescript": "^4.9.5", "vite": "^4.4.5", "vite-plugin-babel": "^1.1.3", "vite-plugin-dts": "^1.7.1", "rollup-plugin-visualizer": "^5.9.2"}, "repository": {"type": "git", "url": "https://gitlab.msunhis.com/msunClound/emr-editor-group/msun-editor-base.git"}, "realVersion": "10.24.5"}